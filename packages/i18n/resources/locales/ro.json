{"STATUS_400__CODE_101": "Not a brand", "STATUS_400__CODE_103": "Limits for game type {{{type}}} and currency {{{currency}}} incorrect. Please check {{{invalidField}}}", "STATUS_400__CODE_107": "Invalid game payment: {{{payment}}}", "STATUS_400__CODE_108": "Max capacity of transaction is reached", "STATUS_400__CODE_109": "Operation is permitted only for brands or merchants", "STATUS_400__CODE_151": "Too many items for group action, limit is {{{limit}}} and received {{{receivedCount}}}", "STATUS_400__CODE_152": "Incorrect action query", "STATUS_400__CODE_199": "Email already used", "STATUS_400__CODE_207": "Provider user, code or secret is missing", "STATUS_400__CODE_214": "Game doesn't belong to game group", "STATUS_400__CODE_217": "Bad query for updating agent status", "STATUS_400__CODE_218": "Bad query for updating agent", "STATUS_400__CODE_226": "Bad query for updating token status", "STATUS_400__CODE_228": "New password should be different from a previous one", "STATUS_400__CODE_229": "The '{{{permission}}}' Permission not exist in list", "STATUS_400__CODE_300": "Payment API respond with error", "STATUS_400__CODE_303": "Failed to delete entity game with child entity games", "STATUS_400__CODE_306": "Game {{{gameCode}}} is suspended", "STATUS_400__CODE_311": "Game provider {{{providerCode}}} is suspended", "STATUS_400__CODE_320": "Start game token error", "STATUS_400__CODE_321": "Start game token is expired", "STATUS_400__CODE_322": "Game token error", "STATUS_400__CODE_323": "Game token expired", "STATUS_400__CODE_328": "Bad request for terminal updating", "STATUS_400__CODE_329": "Bad query for getting terminals", "STATUS_400__CODE_330": "Bad request for lobby updating", "STATUS_400__CODE_331": "Lobby could not be deleted cos it have assigned terminals: {{{terminals}}}", "STATUS_400__CODE_340": "No receivers found for notification", "STATUS_400__CODE_341": "Notification not found", "STATUS_400__CODE_40": "<PERSON><PERSON>re de validare: {{{messages}}}", "STATUS_400__CODE_403": "{{{capitalizedKey}}} is not valid for sort by", "STATUS_400__CODE_41": "The Email Address is in an invalid format", "STATUS_400__CODE_42": "Customer code should contain from 6 to 30 characters", "STATUS_400__CODE_43": "Provided password is not valid", "STATUS_400__CODE_501": "Merchant type {{{type}}} is not supported", "STATUS_400__CODE_503": "Merchant brand doesn't support this operation", "STATUS_400__CODE_504": "Not a merchant brand", "STATUS_400__CODE_61": "Parent not found", "STATUS_400__CODE_62": "One of the parents is suspended", "STATUS_400__CODE_622": "Bad query for updating Role", "STATUS_400__CODE_625": "Add Role to <PERSON>r failed {{{reason}}}", "STATUS_400__CODE_64": "Entity is not empty!", "STATUS_400__CODE_669": "Label record not created", "STATUS_400__CODE_670": "Payment method not found", "STATUS_400__CODE_671": "Payment type should have \"deposit\" or \"withdrawal\" value", "STATUS_400__CODE_672": "Could not find transaction to perform operation.", "STATUS_400__CODE_674": "Malformed JSON : {{{reason}}}", "STATUS_400__CODE_675": "Invalid payment action attribute: {{{attribute}}}", "STATUS_400__CODE_676": "Invalid payment action type: {{{action}}}", "STATUS_400__CODE_677": "Negative payment action value", "STATUS_400__CODE_678": "Payment action list is empty", "STATUS_400__CODE_685": "Insufficient free bets balance", "STATUS_400__CODE_686": "Invalid free bet", "STATUS_400__CODE_687": "Operațiuni concurente în cadrul promoției", "STATUS_400__CODE_688": "Promoția a fost deja adăugată pentru jucător", "STATUS_400__CODE_703": "IP address {{{ip}}} cannot be resolved", "STATUS_400__CODE_705": "Fail to obtain external game url", "STATUS_400__CODE_708": "It is forbidden to start game from unauthorized site", "STATUS_400__CODE_709": "Jucătorul nu deține o astfel de promoție", "STATUS_400__CODE_711": "Promoția nu este validă", "STATUS_400__CODE_712": "Jucătorul este suspendat", "STATUS_400__CODE_713": "Password not confirm", "STATUS_400__CODE_714": "Password should be different from username", "STATUS_400__CODE_716": "JPN bad request error", "STATUS_400__CODE_81": "Country with code {{{code}}} is default. You cannot remove default country", "STATUS_400__CODE_82": "Country {{{country}}} not in list", "STATUS_400__CODE_83": "Country {{{country}}} not exist in parent", "STATUS_400__CODE_86": "Currency {{{currency}}} is default. You cannot remove default currency", "STATUS_400__CODE_87": "Currency {{{currency}}} not in list", "STATUS_400__CODE_88": "Currency {{{currency}}} not exist in parent", "STATUS_400__CODE_89": "Currency {{{currency}}} not exist", "STATUS_400__CODE_90": "Amount is negative", "STATUS_400__CODE_92": "Bad TransactionId", "STATUS_400__CODE_91": "Player does not have sufficient balance to perform an operation", "STATUS_400__CODE_94": "Language {{{language}}} is default. You cannot remove default language", "STATUS_400__CODE_95": "Language {{{language}}} not in list", "STATUS_400__CODE_96": "Language {{{language}}} not exist in parent", "STATUS_400__CODE_97": "Countries is not array", "STATUS_400__CODE_98": "Currencies is not array", "STATUS_400__CODE_99": "Languages is not array", "STATUS_400__CODE_725": "Create Report request error", "STATUS_400__CODE_726": "Reports list does not initialized", "STATUS_400__CODE_727": "Report not found", "STATUS_400__CODE_728": "Report is suspended", "STATUS_400__CODE_729": "Entitatea nu are un sold suficient pentru a efectua o operațiune", "STATUS_400__CODE_730": "Entity has different environment id", "STATUS_400__CODE_731": "Parent is brand", "STATUS_400__CODE_732": "Promoțiile nu sunt activate pentru comerciant", "STATUS_400__CODE_902": "Static domain is not defined", "STATUS_400__CODE_903": "Dynamic domain is not defined", "STATUS_401__CODE_10": "Access Token is missing", "STATUS_401__CODE_201": "Password does not match", "STATUS_401__CODE_202": "User or Password does not match", "STATUS_401__CODE_203": "Password incorrect", "STATUS_401__CODE_204": "Access token error", "STATUS_401__CODE_205": "Access Token is expired. {{{reason}}}", "STATUS_401__CODE_208": "Terminal token error", "STATUS_401__CODE_209": "Provider secret incorrect", "STATUS_401__CODE_221": "Reset password link is expired", "STATUS_401__CODE_222": "Reset password already complete or something go wrong", "STATUS_401__CODE_223": "Player created without password", "STATUS_401__CODE_224": "Password has not changed", "STATUS_400__CODE_225": "Player info has not changed", "STATUS_401__CODE_230": "{{{entityName}}} authentication is blocked", "STATUS_401__CODE_699": "Provided auth code is incorrect", "STATUS_401__CODE_704": "Two factor auth token is expired", "STATUS_401__CODE_715": "Two Factor Authentication is not set for user", "STATUS_401__CODE_718": "Two factor auth token error", "STATUS_401__CODE_719": "An error occurred when sending sms. Check logs", "STATUS_401__CODE_720": "An error occurred when sending email. Check logs", "STATUS_401__CODE_721": "Selected {{{authType}}} auth type is not allowed", "STATUS_401__CODE_722": "Two factor auth is not configured", "STATUS_401__CODE_723": "Two factor auth code was not generated or expired", "STATUS_403__CODE_206": "Forbidden. {{{reason}}}", "STATUS_403__CODE_219": "Can not update archived promo", "STATUS_403__CODE_50": "Not master entity", "STATUS_403__CODE_626": "You cannot manage role", "STATUS_403__CODE_701": "Country {{{country}}} [from {{{source}}}] is restricted [{{{reason}}}]", "STATUS_403__CODE_702": "Currency {{{currency}}} in country {{{countryCode}}} [from {{{source}}}] restricted", "STATUS_403__CODE_706": "Can not update promo that is not pending", "STATUS_403__CODE_707": "<PERSON> has exceeded {{{maxNumber}}} of test players. It's max number.", "STATUS_404__CODE_102": "Jucătorul nu a fost găsit", "STATUS_404__CODE_104": "Limits for currency {{{currency}}} not found", "STATUS_404__CODE_105": "Session not found", "STATUS_404__CODE_106": "Session already finished", "STATUS_404__CODE_110": "Whitelist not found", "STATUS_404__CODE_198": "User does not exist", "STATUS_404__CODE_211": "Game group is not found", "STATUS_404__CODE_213": "Game {{{gameCode}}} is not available for entity", "STATUS_404__CODE_215": "No record for this Domain in agents", "STATUS_404__CODE_227": "Site token does not exist", "STATUS_404__CODE_240": "Game not found. GameCode: {{{gameCode}}}", "STATUS_404__CODE_304": "Game category is not found", "STATUS_404__CODE_312": "Game provider not found", "STATUS_404__CODE_324": "Lobby is not found", "STATUS_404__CODE_327": "Terminal is not found", "STATUS_404__CODE_343": "Site not found", "STATUS_404__CODE_502": "Merchant not found", "STATUS_404__CODE_51": "Could not find entity", "STATUS_404__CODE_600": "Transaction not found", "STATUS_404__CODE_623": "Role not exist", "STATUS_404__CODE_680": "Promoția nu a fost găsită", "STATUS_400__CODE_681": "Promoția a expirat", "STATUS_404__CODE_683": "Game history details not found", "STATUS_404__CODE_684": "Referenced {{{item}}} is not found", "STATUS_404__CODE_80": "Country {{{country}}} not found", "STATUS_404__CODE_85": "Currency {{{currency}}} not found", "STATUS_404__CODE_93": "Language {{{language}}} not found", "STATUS_404__CODE_900": "Domain not exist", "STATUS_409__CODE_100": "Player {{{details}}} already exist", "STATUS_409__CODE_111": "Whitelist already exists. You can patch it", "STATUS_409__CODE_200": "User already exist", "STATUS_409__CODE_210": "Game group with name {{{name}}} already exists", "STATUS_409__CODE_212": "Game {{{gameCode}}} already exists in game group {{{gameGroupName}}}", "STATUS_409__CODE_241": "Game already exists", "STATUS_409__CODE_302": "Failed to delete entity game with child entity games. Need force flag", "STATUS_409__CODE_305": "Game category already exists", "STATUS_409__CODE_310": "Game provider already exists", "STATUS_409__CODE_325": "Lobby already exists", "STATUS_409__CODE_326": "Terminal already exists", "STATUS_409__CODE_342": "Site already exists", "STATUS_409__CODE_500": "Merchant already exists", "STATUS_409__CODE_60": "Entity already exists", "STATUS_409__CODE_624": "Failed to delete role with linked users. Need force flag", "STATUS_409__CODE_63": "Entity is being edited now!", "STATUS_409__CODE_682": "Failed to delete item that is referenced from other table. Need force flag", "STATUS_409__CODE_724": "Report with this name already exists. You can patch it", "STATUS_409__CODE_753": "Two factor auth code has been sent recently. Repeat attempt a little later", "STATUS_409__CODE_901": "Domain is used by entity", "STATUS_500__CODE_1": "Internal server error", "STATUS_500__CODE_216": "Bad BrandId from list of agents or Domain is not unique", "STATUS_500__CODE_220": "Bad BrandId or code is not unique", "STATUS_500__CODE_301": "Payment API transient error", "STATUS_500__CODE_405": "Missing currency exchange rates", "STATUS_500__CODE_505": "Merchant should have url and password in params", "STATUS_500__CODE_621": "Role record not created", "STATUS_500__CODE_673": "Not yet ready to rollback transaction. Repeat later.", "STATUS_500__CODE_700": "Ip location lookup error: {{{message}}}", "STATUS_500__CODE_717": "JPN internal server error", "STATUS_500__CODE_506": "Merchant internal error {{{reason}}}", "STATUS_500__CODE_507": "Error during integration with merchant:\n {{{reason}}}", "STATUS_500__CODE_672": "Can't transfer out money to external wallet", "STATUS_403__CODE_1500": "Operațiunea nu poate fi executată. Jucătorul este în pauză.", "STATUS_403__CODE_1501": "Operațiunea nu poate fi executată. Jucătorul este autoexclus.", "STATUS_403__CODE_1502": "Operațiunea nu poate fi executată. Jucătorul a atins limita de depunere.", "STATUS_403__CODE_1503": "<PERSON><PERSON><PERSON> atins limita de timp pentru sesiune.", "STATUS_403__CODE_1504": "A<PERSON>i atins limita de timp zilnică pe care ați stabilit-o anterior. Reveniți și jucați din nou mâine.", "STATUS_403__CODE_1505": "Dorim să vă înștiințăm că ați jucat deja un timp egal cu intervalul de analiză a realității.", "STATUS_403__CODE_1506": "Operațiunea nu poate fi executată. Jucătorul este blocat permanent.", "STATUS_403__CODE_1507": "Valoarea pariului selectat depășește limita de pierdere pentru astăzi.", "STATUS_403__CODE_1508": "<PERSON><PERSON><PERSON> atins limita de pierdere pentru as<PERSON>zi.", "STATUS_403__CODE_1509": "Valoarea pariului selectat depășește limita de pierdere pentru această săptămână.", "STATUS_403__CODE_1510": "<PERSON><PERSON><PERSON> atins limita de pierdere pentru această săptămână.", "STATUS_403__CODE_1511": "Valoarea pariului selectat depășește limita de pierdere pentru această lună.", "STATUS_403__CODE_1512": "<PERSON><PERSON><PERSON> atins limita de pierdere pentru această lună.", "STATUS_403__CODE_1513": "<PERSON><PERSON><PERSON> atins limita de pierdere pentru sesiune.", "STATUS_403__CODE_1514": "<PERSON><PERSON><PERSON>, atunci când jucătorul depășește limitele legate de reglementări.", "STATUS_403__CODE_1515": "V-ați atins limita de pariere per sesiune.", "STATUS_403__CODE_1516": "<PERSON><PERSON><PERSON> atins limita de pariere pentru as<PERSON>.", "STATUS_403__CODE_1517": "<PERSON><PERSON><PERSON> atins limita de pariere pentru această săptămână.", "STATUS_403__CODE_1518": "<PERSON><PERSON><PERSON> atins limita de pariere pentru această lună.", "STATUS_403__CODE_1519": "<PERSON><PERSON>i atins limita de retragere.", "STATUS_403__CODE_1520": "Lipsește o limită obligatorie.", "STATUS_403__CODE_1521": "DAți atins limita de pierdere pentru grupul de jocuri într-o singură sesiune.", "STATUS_403__CODE_1522": "<PERSON><PERSON>i atins limita de pierdere pentru grupul de jocuri pentru ziua de azi.", "STATUS_403__CODE_1523": "<PERSON><PERSON>i atins limita de pierdere pentru grupul de jocuri pentru săptămâna curentă.", "STATUS_403__CODE_1524": "<PERSON><PERSON><PERSON> atins limita de pierdere pentru grupul de jocuri pentru luna curentă.", "STATUS_403__CODE_1525": "Ați depășit pariul maxim pentru fondurile bonus."}