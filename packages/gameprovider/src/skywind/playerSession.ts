import {
    PlayerGameBonusCoin,
    PlayerGameFreebet
} from "@skywind-group/sw-management-promo-wallet";
import { BrandFinalizationType } from "@skywind-group/sw-wallet-adapter-core";

export interface PlayerOperatorFreeBetPromo {
    coin: number;
    coinMultiplier: number;
}

export interface PlayerGamePromo {
    freeBet?: PlayerGameFreebet[];
    bonusCoin?: PlayerGameBonusCoin;
    operatorFreeBet?: PlayerOperatorFreeBetPromo;
}

export interface GameSessionInfo extends AdditionalSessionInfo {
    promo?: PlayerGamePromo;
}

export interface AdditionalSessionInfo {
    isGRCGame?: boolean;
    // boolean flag that claims that bet win history of this provider's game operations must be stored at our DB
    mustStoreExternalBetWinHistory?: boolean;
    isNewExternalBetWinHistory?: boolean;
    offlineWin?: boolean;
    gameProviderCode?: string; // game provider's code if mustStoreExternalBetWinHistory is true
    sharedPromoEnabled?: boolean;
    operatorSiteId?: number;
    operatorFinalizationSupport?: BrandFinalizationType;
    operatorFinalizeOnOfflineWin?: boolean;
    rtpDeduction?: number;
    bonusPaymentMethod?: string;
}
