import config from "../config";

import { Redis as RedisClient } from "ioredis";
import { redis } from "@skywind-group/sw-utils";

const pool: redis.RedisPool<RedisClient> = redis.createRedisPool(config.redis);
const redisFactory: redis.RedisClientFactory<RedisClient> = redis.getRedisFactory(config.redis);

export function getRedisPool(): redis.RedisPool<RedisClient> {
    return pool;
}

export function getRedisFactory(): redis.RedisClientFactory<RedisClient> {
    return redisFactory;
}

export function get(): Promise<RedisClient> {
    return pool.get();
}

export function release(client: RedisClient): void {
    pool.release(client);
}

export function create(): RedisClient {
    return redisFactory.createClient();
}

export async function usingDb<T>(callback: (client: RedisClient) => Promise<T>): Promise<T> {
    return pool.usingDb<T>(callback);
}

export async function usingDbWithReplicate<T>(callback: (client: RedisClient) => Promise<T>): Promise<T> {
    return pool.usingDbWithReplicate<T>(callback);
}
