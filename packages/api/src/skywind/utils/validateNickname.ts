import {
    InternalServerError,
    <PERSON>nameBad<PERSON>ordsError,
    NicknameMaxSymbolsError,
    NicknameMinSymbolsError,
    NicknameSymbolsError,
    NicknameIdenticalUsernameError,
} from "../errors";
import config from "../config";
import { IncomingMessage } from "node:http";
import { NICKNAME_MAX_LENGTH, NICKNAME_MIN_LENGTH } from "./common";
import { logging } from "@skywind-group/sw-utils";
import { SWError } from "@skywind-group/sw-wallet-adapter-core";
import { generateBanWordsToken } from "./token";
import request = require("request");

const log = logging.logger("validate-nickname");

export const BAN_WORDS_TOKEN = "x-ban-words-token";

export class ValidateNickname {
    private urlPath = "v1/players/validate-nickname/";

    constructor(protected isMerchant = false) {
    }

    public async checkIdenticalUsername(nickname: string, playerCode: string) {
        if (nickname === playerCode) {
            throw new NicknameIdenticalUsernameError(nickname);
        }
    }

    public async checkSymbols(nickname: string): Promise<void> {
        const urlPath = this.urlPath + nickname;
        return this.get(urlPath);
    }

    private async get(urlPath: string) {
        const token = await generateBanWordsToken({});

        return new Promise<any>((resolve, reject) => {
            request.get(urlPath, {
                baseUrl: config.banWords.baseUrl,
                timeout: config.banWords.timeout,
                json: true,
                headers: {
                    [BAN_WORDS_TOKEN]: token,
                    "Content-Type": "application/json",
                },
            }, (error: Error, response: IncomingMessage, responseBody: any) =>
                this.processResponse(resolve, reject, error, response, responseBody));
        });
    }

    private processResponse(resolve, reject, error: Error, response: IncomingMessage, responseBody: any) {
        if (error) {
            log.warn("Failed to process request", error);
            return reject(new InternalServerError(error.message));

        } else if (response.statusCode >= 400 && response.statusCode <= 500) {
            log.warn("Response error", responseBody);
            const swError = this.toSwError(responseBody);
            return reject(swError);

        } else {
            return resolve(responseBody);
        }
    }

    private toSwError(error: SWError): SWError {
        switch (error.code) {
            case 852: return new NicknameSymbolsError();
            case 853: return new NicknameMinSymbolsError(NICKNAME_MIN_LENGTH);
            case 854: return new NicknameMaxSymbolsError(NICKNAME_MAX_LENGTH);
            case 855: return new NicknameBadWordsError();
            default:
                return new InternalServerError(error.message);
        }
    }
}
