import { EntityCountryRestrictions, EntitySettings } from "../entities/settings";
import { MerchantParams } from "../entities/merchant";
import { getIpLocationService, IpLocationService } from "./iplocation";
import { BO_WHITELIST_KEY, IP_WHITELIST_KEY } from "../services/settings";
import * as Errors from "../errors";
import config from "../config";
import logger from "./logger";
import { BrandEntity } from "../entities/brand";
import * as MerchantCache from "../cache/merchant";
import { getPlayerInfoService } from "../services/playerInfo";
import { PlayerInfo } from "../entities/playerInfo";

const log = logger();

interface IpData {
    readonly entitySettings: EntitySettings;
    readonly playerInfo?: PlayerInfo;
    readonly ip?: string;
}

interface MerchantData extends IpData {
    readonly merchantParams?: MerchantParams;
    readonly countryCode?: string;
}

interface Params {
    readonly brand: BrandEntity;
    readonly entitySettings: EntitySettings;
    readonly playerCode?: string;
    readonly countryCode?: string;
    readonly ip?: string;
}

export async function getCountrySource({ brand, playerCode, ...params }: Params): Promise<CountrySource> {
    const playerInfo = playerCode && await getPlayerInfoService().getPlayerInfo(playerCode, brand.id);
    if (brand.isMerchant) {
        const merchant = await MerchantCache.findOne(brand);
        return getMerchantCountrySource({
            merchantParams: merchant.params,
            playerInfo,
            ...params
        });
    }
    return getIpCountrySource({ playerInfo, ...params });
}

export async function getMerchantCountrySource({
                                                   merchantParams,
                                                   countryCode,
                                                   ...params
                                               }: MerchantData): Promise<CountrySource> {
    const ipSource = await getIpCountrySource(params);
    if (ipSource.whitelisted) {
        return ipSource;
    }
    if (ipSource.restricted) {
        return ipSource;
    }
    if (merchantParams?.gameRestrictionsUseIp || config.merchantGameRestrictionsUseIp) {
        return ipSource;
    }
    if (!countryCode) {
        return ipSource;
    }
    return new MerchantSource(countryCode, ipSource.code);
}

export async function getIpCountrySource({ entitySettings, playerInfo, ip }: IpData): Promise<CountrySource> {
    const source = `player IP:${ip}`;
    const service = getIpLocationService();
    if (isIpWhitelisted(service, entitySettings, ip)) {
        return new WhitelistedSource(source);
    }
    const countryCode = await service.getCountryAndRegionCodesByIp(ip);
    if (!countryCode) {
        return Promise.reject(new Errors.UnknownIpAddress(ip));
    }
    const { reason, restricted } = getIpRestrictionByCountryCode(countryCode, entitySettings, playerInfo);
    return new IpSource(restricted, reason, source, countryCode);
}

export interface CountrySource {
    readonly whitelisted: boolean;
    readonly restricted: boolean;
    readonly source: string;
    readonly code?: string;
    readonly ipCountryCode?: string;
    readonly reason?: string;
}

class MerchantSource implements CountrySource {
    public readonly whitelisted = false;
    public readonly restricted = false;
    public readonly source = "operator";

    public constructor(public readonly code: string, public readonly ipCountryCode: string) {
    }
}

class IpSource implements CountrySource {
    public readonly whitelisted = false;

    public constructor(public readonly restricted: boolean,
                       public readonly reason: string,
                       public readonly source: string,
                       public readonly code: string) {
    }
}

class WhitelistedSource implements CountrySource {
    public readonly whitelisted = true;
    public readonly restricted = false;

    public constructor(public readonly source: string) {
    }
}

function getIpWhitelist(entitySettings: EntitySettings): string[] {
    const ipWhitelist = entitySettings[IP_WHITELIST_KEY] || [];
    const boWhitelist = entitySettings[BO_WHITELIST_KEY] || [];
    return ipWhitelist.concat(boWhitelist);
}

function isIpWhitelisted(service: IpLocationService, entitySettings: EntitySettings, ip?: string): boolean {
    if (!ip) {
        log.info("IP undefined");
        return true;
    }
    if (service.isIpOfPrivateAddressSpace(ip)) {
        log.info(`IP:${ip} from private address space`);
        return true;
    }
    const whitelist = getIpWhitelist(entitySettings);
    if (whitelist.length > 0 && service.isWhitelisted(ip, whitelist)) {
        log.info(`IP:${ip} is whitelisted by entity settings`);
        return true;
    }
    return false;
}

function getIpRestrictionByCountryCode(countryCode: string, entitySettings: EntitySettings, playerInfo?: PlayerInfo) {
    function build(name: string, countries: string[]) {
        const restricted = countries.includes(countryCode);
        return {
            restricted,
            ...(restricted ? { reason: `${name}.restrictedIpCountries` } : {})
        };
    }

    const validate = (name: string, rule?: EntityCountryRestrictions | null) => {
        if (rule?.ignore === true) {
            return { restricted: false };
        }
        const countries = rule?.countries;
        if (Array.isArray(countries) && countries.length) {
            return build(name, countries);
        }
        return false;
    };

    let result = validate("playerInfo", playerInfo?.restrictedIpCountries);
    if (result !== false) {
        return result;
    }
    let restrictions = entitySettings.restrictedIpCountries;
    if (!restrictions) {
        const countries = entitySettings.merchantGameRestrictionsUseIpCountries;
        if (Array.isArray(countries) && countries.length) {
            restrictions = {
                countries
            };
        }
    }
    result = validate("entitySettings", restrictions);
    if (result !== false) {
        return result;
    }
    if (Array.isArray(config.restrictedIpCountries) && config.restrictedIpCountries.length) {
        return build("config", config.restrictedIpCountries);
    }
    return {
        restricted: false
    };
}
