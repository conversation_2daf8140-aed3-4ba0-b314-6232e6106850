import { QueryTypes, Transaction } from "sequelize";
import { sequelize as db } from "../storage/db";
import { measures } from "@skywind-group/sw-utils";
import measure = measures.measure;
import measureParam = measures.measureParam;

export enum ApplicationLockId {
    UPDATE_DYNAMIC_DOMAIN = -1,
    START_PROMOTION = -2,
    UPDATE_CURRENCY_RATES = -3,
    SELECT_BI_REPORT_DOMAINS = -4
}

export class ApplicationLock {
    @measure({ name: "ApplicationLock.lock", isAsync: true})
    // Obtain exclusive transaction level advisory lock. This lock is used to ensure that there will be no concurrent
    // operations that modify the same critical data. Note that 'pg_advisory_xact_lock' is used for db transaction-level
    // lock as opposed to pg_advisory_lock which obtains lock for the whole session and requires explicit UNLOCK call.
    // https://www.postgresql.org/docs/9.3/functions-admin.html
    public static async lock(transaction: Transaction, @measureParam() id: ApplicationLockId) {
        return db.query({ query: "select pg_advisory_xact_lock(?)", values: [id] },
            { type: QueryTypes.SELECT, transaction, raw: true });
    }

    // Commented it out as "pg_advisory_xact_lock works the same as pg_advisory_lock, except
    // the lock is automatically released at the end of the current transaction and cannot be released explicitly" - so
    // as long as we use pg_advisory_xact_lock there is no need in explicit unlock call
    /* @measure({ name: "ApplicationLock.unlock", isAsync: true})
    public static async unlock(transaction: Transaction,  @measureParam() id: ApplicationLockId) {
        return db.query({ query: "select pg_advisory_unlock(?)", values: [id] },
            { type: QueryTypes.SELECT, transaction, raw: true });
    } */
}
