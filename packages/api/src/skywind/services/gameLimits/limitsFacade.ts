import { Game } from "../../entities/game";
import { CurrencyCode, Limits } from "../../entities/gamegroup";
import { getSchemaDefinitionService } from "./schemaDefinition";
import { OperationForbidden, ValidationError } from "../../errors";
import { GameLimitsByCurrencyCode, GameLimitsConfigurationInStorage } from "../../models/gameLimitsConfiguration";
import { BaseEntity } from "../../entities/entity";
import { NewLimitsSystemBuilder } from "./limitsBuilder";
import { getCurrencyMultiplierService } from "./currencyMultiplier";
import { CurrencyMultiplier } from "../../models/currencyMultiplier";
import { mergeDeep } from "../../utils/common";
import { findGameLimitsConfiguration } from "./gameLimitsStorage";
import { getEntitySettings } from "../settings";
import { GameGroupDBInstance } from "../../models/gamegroup";
import { getGameGroupService } from "../gamegroup";
import { findOneEntityGame } from "../game";
import { getGameLimitLevelService } from "./entityLimitLevels";
import { EntityGameLimitLevel } from "../../models/entityLimitLevels";
import { DefaultConfigurationFacade, getConfigurationFacade } from "./defaultConfigurationFacade";
import { getGameLimitsCurrency } from "../../cache/gameLimitsCurrencies";
import { Op } from "sequelize";

class NewLimitsFacade {
    constructor(private entity: BaseEntity) {
    }

    public async buildForAllCurrencies(gameCode: string,
                                       gameGroupName?: string,
                                       currencyFilter?: string): Promise<GameLimitsByCurrencyCode> {

        const entityGame = await findOneEntityGame(this.entity, gameCode);
        if (!entityGame.game.schemaDefinitionId) {
            throw new ValidationError("The game doesn't support new limits");
        }

        const entitySettings = await getEntitySettings(this.entity.path);

        if (entityGame.game.features?.isMarketplaceSupported && entitySettings.isMarketplaceSupported) {
            throw new OperationForbidden("Method is not available for marketplace limits");
        }

        let gameGroupId: number;
        if (gameGroupName) {
            const gameGroup: GameGroupDBInstance = await getGameGroupService()
                .findOne(this.entity, { name: gameGroupName }, true);
            gameGroupId = gameGroup.get("id");
        }

        const result: GameLimitsByCurrencyCode = {};
        const currencies = currencyFilter ? [currencyFilter] : this.entity.getCurrencies();

        for (const currency of currencies) {
            result[currency] = await this.buildGameLaunch(
                entityGame.game,
                currency,
                gameGroupId);
        }

        return result;
    }

    public async buildGameLaunch(game: Game,
                                 currency: CurrencyCode,
                                 gameGroupId?: number,
                                 segmentId?: number,
                                 isMPSupported?: boolean,
                                 skipCurrencyMultiplierError?: boolean): Promise<Limits> {

        if (!game.schemaDefinitionId) {
            throw new ValidationError("Game should have game limits definition");
        }
        const schemaDefinition = await getSchemaDefinitionService().retrieve(game.schemaDefinitionId);

        const facade = await getConfigurationFacade(schemaDefinition, game.code);

        const gameLimitsConfiguration: GameLimitsConfigurationInStorage = await findGameLimitsConfiguration(this.entity,
            game.code,
            schemaDefinition.id,
            gameGroupId,
            segmentId);

        const levelCustomizations = await getGameLimitLevelService(this.entity)
            .getGameLimitLevels({ gameCode: { [Op.eq]: game.code } });

        const gameLimitsCurrenciesVersion = game && game.features && game.features.gameLimitsCurrenciesVersion ?
            game.features.gameLimitsCurrenciesVersion :
            1;
        const isSmartRoundingSupported = game.features && game.features.isSmartRoundingSupported;

        return this.build(
            facade,
            game.totalBetMultiplier,
            currency,
            gameLimitsConfiguration,
            isMPSupported,
            undefined,
            levelCustomizations,
            gameLimitsCurrenciesVersion,
            isSmartRoundingSupported,
            skipCurrencyMultiplierError);
    }

    public async defaults(currency: string,
                          facade: DefaultConfigurationFacade) {

        const limitsBuilder = new NewLimitsSystemBuilder(facade, currency);

        return facade.configuration && limitsBuilder.build();
    }

    public async build(facade: DefaultConfigurationFacade,
                       totalBetMultiplier: number = 1,
                       currency: string,
                       gameLimitsConfiguration: GameLimitsConfigurationInStorage = { gameLimits: {} },
                       isMPSupported?: boolean,
                       customCurrencyMultipliers?: CurrencyMultiplier,
                       gameLimitLevels: EntityGameLimitLevel[] = [],
                       gameLimitsCurrenciesVersion?: number,
                       isSmartRoundingSupported?: boolean,
                       skipCurrencyMultiplierError?: boolean): Promise<Limits> {

        if (isMPSupported) {
            if (!customCurrencyMultipliers) {
                customCurrencyMultipliers = await getCurrencyMultiplierService().findOne(this.entity);
            }

            await this.convertToCustomBaseCurrency(totalBetMultiplier,
                facade,
                gameLimitsConfiguration,
                customCurrencyMultipliers);
        }

        let customToEURMultiplier: number | undefined;
        if (!customCurrencyMultipliers) {
            const gameLimitsCurrency = await getGameLimitsCurrency(
                currency,
                await getEntitySettings(this.entity.path),
                { gameLimitsCurrenciesVersion }
            );
            customToEURMultiplier = gameLimitsCurrency?.toEURMultiplier || undefined;
        }

        const customLimitsBuilder = new NewLimitsSystemBuilder(facade,
            currency,
            gameLimitsConfiguration,
            totalBetMultiplier,
            customCurrencyMultipliers,
            gameLimitLevels,
            customToEURMultiplier,
            isSmartRoundingSupported);

        return customLimitsBuilder.build(false, skipCurrencyMultiplierError);
    }

    // for MP EU need to convert defaults to MP base currency using our multipliers system
    // after it they'll be converted one more time using MP multipliers system
    private async convertToCustomBaseCurrency(totalBetMultiplier: number,
                                              facade: DefaultConfigurationFacade,
                                              gameLimitsConfiguration: GameLimitsConfigurationInStorage,
                                              customCurrencyMultipliers?: CurrencyMultiplier) {
        if (customCurrencyMultipliers) {
            gameLimitsConfiguration = gameLimitsConfiguration || {};
            gameLimitsConfiguration.gameLimits = gameLimitsConfiguration.gameLimits || {};

            const baseCurrency = customCurrencyMultipliers.baseCurrency;
            const defaultLimitsBuilder = new NewLimitsSystemBuilder(facade, baseCurrency, gameLimitsConfiguration,
                totalBetMultiplier);

            const defaultLimits = await defaultLimitsBuilder.build(true);

            gameLimitsConfiguration.gameLimits[baseCurrency] = mergeDeep({},
                defaultLimits, gameLimitsConfiguration.gameLimits[baseCurrency]);
        }
    }
}

export function getNewLimitsFacade(entity: BaseEntity) {
    return new NewLimitsFacade(entity);
}
