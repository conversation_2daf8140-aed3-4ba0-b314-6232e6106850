import { GameVersionAttributes, GameVersionItem } from "../models/gameClientVersion";
import { DestroyOptions, Op, Transaction, WhereOptions } from "sequelize";
import { GameVersionsUpdateError, ValidationError } from "../errors";
import { sequelize as db } from "../storage/db";
import { DeploymentGroupService, getDeploymentGroupService } from "./deploymentGroup";
import logger from "../utils/logger";
import { lazy } from "@skywind-group/sw-utils";
import { EntityGame, GameClientFeatures } from "../entities/game";
import { BaseEntity } from "../entities/entity";
import { UrlPlaceholders } from "./gameUrl/urlPlaceholders";
import { Models } from "../models/models";
import { DeploymentGroupAttributes } from "../entities/deploymentGroup";

const log = logger("deployment-group-game-client-version");
const GameVersionModel = Models.GameVersionModel;

export interface VersionsPerRoute {
    [route: string]: { clientVersion: string, clientFeatures?: GameClientFeatures };
}

export interface GameClientVersionService {

    getGameClientVersion(entity: BaseEntity, entityGame: EntityGame): Promise<string>;

    getGameClientVersionItem(entity: BaseEntity, entityGame: EntityGame): Promise<GameVersionItem>;

    getGameClientVersionByGroupId(groupId: number, gameCode: string): Promise<GameVersionItem>;

    getGameClientVersionsPerRoute(gameCode: string): Promise<VersionsPerRoute>;

    updateGameClientVersionPerRoute(gameCode: string, versions: VersionsPerRoute): Promise<VersionsPerRoute>;

    removeGameClientVersion(groupId: number, gameCode: string): Promise<VersionsPerRoute>;

    removeGameClientVersionByRoute(route: string, gameCode: string): Promise<VersionsPerRoute>;
}

class DeploymentGroupGameClientVersionService implements GameClientVersionService {

    constructor(private readonly depGroupService: DeploymentGroupService) {
    }

    public async getGameClientVersion(entity: BaseEntity, entityGame: EntityGame): Promise<string> {

        const gameVersionItem = await this.getGameClientVersionItem(entity, entityGame);
        if (gameVersionItem) {
            return gameVersionItem.clientVersion;
        }

        const defaultVersion = entityGame.game.defaultClientVersion;

        if (defaultVersion) {
            return defaultVersion;
        }

        throw new ValidationError(
            `Game url has ${UrlPlaceholders.KEY_CLIENT_VERSION} placeholder but no default version`);
    }

    public async getGameClientVersionItem(entity: BaseEntity, entityGame: EntityGame): Promise<GameVersionItem> {

        const entityGroupId = entity.deploymentGroupId;
        const gameGroupId = entityGame.game.deploymentGroupId;
        const gameCode = entityGame.game.code;

        // Deployment group assigned to entity has greater priority
        const versionInEntityGroup = await this.getGameClientVersionByGroupId(entityGroupId, gameCode);
        if (versionInEntityGroup) {
            return versionInEntityGroup;
        }

        const versionInGameGroup = await this.getGameClientVersionByGroupId(gameGroupId, gameCode);
        if (versionInGameGroup) {
            return versionInGameGroup;
        }
    }

    public async getGameClientVersionByGroupId(groupId: number, gameCode: string): Promise<GameVersionItem> {
        const options = {
            where: {
                deploymentGroupId: { [Op.eq]: groupId },
                gameCode: { [Op.eq]: gameCode }
            }
        };
        const item = await GameVersionModel.findOne(options);

        if (!item) {
            return undefined;
        }

        return new GameVersionItem(item);
    }

    public async getGameClientVersionsPerRoute(gameCode: string): Promise<VersionsPerRoute> {
        const whereOptions: WhereOptions<any> = { gameCode: gameCode };
        const options = { where: whereOptions };
        const items = await GameVersionModel.findAll(options);

        const result: VersionsPerRoute = {};

        for (const item of items) {
            const deploymentGroup = await this.depGroupService.getDeploymentGroup(item.get("deploymentGroupId"));
            const clientVersion = item.get("clientVersion");
            const clientFeatures = item.get("clientFeatures");

            result[deploymentGroup.route] = { clientVersion, clientFeatures };
        }

        return result;
    }

    public async updateGameClientVersionPerRoute(gameCode: string,
                                                 versions: VersionsPerRoute): Promise<VersionsPerRoute> {

        const routes = Object.keys(versions);
        const deploymentGroups: DeploymentGroupAttributes[] = [];
        for (const route of routes) {
            deploymentGroups.push(await this.depGroupService.getDeploymentGroupByRoute(route));
        }

        try {
            await this.bulkUpdateGameClientVersions(gameCode, deploymentGroups, versions);
        } catch (err) {
            log.error(err, "Failed to update game client versions");
            throw new GameVersionsUpdateError();
        }

        return this.getGameClientVersionsPerRoute(gameCode);
    }

    public async removeGameClientVersionByRoute(route: string, gameCode: string): Promise<VersionsPerRoute> {
        const group = await this.depGroupService.getDeploymentGroupByRoute(route);

        const whereOptions: WhereOptions = { deploymentGroupId: group.id, gameCode: gameCode };
        const options = { where: whereOptions };

        return this.removeGameClientVersionByOptions(options, gameCode);
    }

    public async removeGameClientVersion(groupId: number, gameCode: string): Promise<VersionsPerRoute> {
        const group = await this.depGroupService.getDeploymentGroup(groupId);

        const whereOptions: WhereOptions = { deploymentGroupId: group.id, gameCode: gameCode };
        const options = { where: whereOptions };

        return this.removeGameClientVersionByOptions(options, gameCode);
    }

    private async bulkUpdateGameClientVersions(gameCode: string,
                                               deploymentGroups: DeploymentGroupAttributes[],
                                               versions: VersionsPerRoute): Promise<void> {

        const gameVersionModel = GameVersionModel;

        await db.transaction(async (transaction: Transaction) => {

            for (const deploymentGroup of deploymentGroups) {

                const whereOptions: WhereOptions = { deploymentGroupId: deploymentGroup.id, gameCode: gameCode };
                const options = { where: whereOptions, transaction: transaction };

                const data: GameVersionAttributes = {
                    deploymentGroupId: deploymentGroup.id,
                    gameCode: gameCode,
                    clientVersion: versions[deploymentGroup.route].clientVersion,
                    clientFeatures: versions[deploymentGroup.route].clientFeatures
                };

                const item = await gameVersionModel.findOne(options);
                if (item) {
                    await gameVersionModel.update(data, options);
                } else {
                    await gameVersionModel.create(data, { transaction: transaction });
                }
            }
        });
    }

    public async removeGameClientVersionByOptions(opt: DestroyOptions, gameCode: string): Promise<VersionsPerRoute> {
        await GameVersionModel.destroy(opt);

        return this.getGameClientVersionsPerRoute(gameCode);
    }
}

const gameClientVersionService = lazy<GameClientVersionService>(() =>
    new DeploymentGroupGameClientVersionService(getDeploymentGroupService()));

export function getGameClientVersionService() {
    return gameClientVersionService.get();
}
