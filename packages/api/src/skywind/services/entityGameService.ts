import {
    EntityGame,
    EntityGameCodeInfo,
    EntityGameData,
    LimitFiltersByCurrency,
    UpdateLimitsData,
    UpdateStatusesData
} from "../entities/game";
import {
    EntityGameImpl,
    findAllEntityGames,
    findOneEntityGame,
    removeGameRelations,
    validateGameRelations,
    validateGameSettings
} from "./game";
import { Op, Transaction } from "sequelize";
import { BaseEntity, ChildEntity, Entity } from "../entities/entity";
import {
    ENTITY_GAME_STATUS,
    filterObjectFields,
    GAME_TYPES,
    GROUP_ACTION_MAX_ITEMS,
    mergeArrayToObject,
    mergeDeep
} from "../utils/common";
import * as Errors from "../errors";
import { getChildIds } from "./entity";
import { sequelize as db } from "../storage/db";
import { EntityGameAttributes } from "../models/game";
import { validateLimitFilters } from "./limits";
import { LimitsByCurrencyCode } from "../entities/gamegroup";
import { favoriteGamesCache } from "./gameCategory/gameCategoryGamesService";
import { defaultSuspendGameService as SuspendGameService } from "./suspendGameService";
import { defaultOperatorInfoUpdater } from "./gameauth/defaultOperatorInfoUpdater";
import { gameRTPHistoryService } from "./gameRTPHistory";
import { validateTranslations } from "../utils/validateTranslations";
import * as LobbyCache from "../cache/lobby";
import { Models } from "../models/models";

const GameModel = Models.GameModel;
const EntityGameModel = Models.EntityGameModel;
const GameProviderModel = Models.GameProviderModel;

const EXCLUDED_STATUSES = [ENTITY_GAME_STATUS.SUSPENDED, ENTITY_GAME_STATUS.HIDDEN] as string[];

export async function getEntityGames(entity: BaseEntity) {
    const games = await EntityGameModel.findAll({
        where: {
            entityId: entity.id
        },
        include: [
            {
                model: GameModel,
                required: true,
                include: [
                    {
                        model: GameProviderModel,
                        required: true
                    }
                ]
            }
        ]
    });
    return games.map(item => new EntityGameImpl(item));
}

export interface EntityGameService {
    suspend(gameCode: string,
            isLiveGame: boolean,
            reason?: string,
            allowToFinishCurrentSession?: boolean,
            skipStatusChangeValidation?: boolean): Promise<EntityGameCodeInfo>;

    restore(gameCode: string, isLiveGame: boolean, skipStatusChangeValidation?: boolean): Promise<EntityGameCodeInfo>;

    update(gameCode: string,
           gameData?: EntityGameData,
           transaction?: Transaction,
           allowToFinishCurrentSession?: boolean): Promise<EntityGameCodeInfo>;

    updateLimits(gameCode: string,
                 newLimitFilters: LimitFiltersByCurrency): Promise<EntityGameCodeInfo>;

    bulkUpdateStatus(data: UpdateStatusesData, reason?: string): Promise<void>;

    bulkUpdateLimits(data: UpdateLimitsData): Promise<void>;

    delete(gameCode: string,
           isLiveGame: boolean,
           force?: boolean,
           transaction?: Transaction): Promise<EntityGameCodeInfo>;

    bulkDelete(gameCodes: string[],
               isLiveGame: boolean): Promise<EntityGameCodeInfo[]>;
}

class EntityGameServiceImpl implements EntityGameService {
    constructor(protected entity: BaseEntity) {
    }

    private async validateGameType(gameCode: string, isLiveGame: boolean): Promise<void> {
        const entityGame = await findOneEntityGame(this.entity, gameCode, undefined, true);
        if (isLiveGame && entityGame.game.type !== GAME_TYPES.live) {
            throw new Errors.UpdateNotLiveGameOfEntityError();
        }
        if (!isLiveGame && entityGame.game.type === GAME_TYPES.live) {
            throw new Errors.UpdateLiveGameOfEntityError();
        }
    }

    @resetCache()
    public async suspend(gameCode: string,
                         isLiveGame: boolean,
                         reason?: string,
                         allowToFinishCurrentSession?: boolean,
                         skipStatusChangeValidation?: boolean): Promise<EntityGameCodeInfo> {
        try {
            await this.validateGameType(gameCode, isLiveGame);
            const result = await this.updateStatus(
                this.entity,
                gameCode,
                ENTITY_GAME_STATUS.SUSPENDED,
                skipStatusChangeValidation
            );
            if (!allowToFinishCurrentSession) {
                await EntityGameServiceImpl.suspendGames(this.entity, [gameCode]);
            }
            return result;
        } catch (error) {
            return Promise.reject(error);
        }
    }

    @resetCache()
    public async restore(gameCode: string,
                         isLiveGame: boolean,
                         skipStatusChangeValidation?: boolean): Promise<EntityGameCodeInfo> {
        try {
            await this.validateGameType(gameCode, isLiveGame);
            const result = await this.updateStatus(
                this.entity,
                gameCode,
                ENTITY_GAME_STATUS.NORMAL,
                skipStatusChangeValidation);
            await EntityGameServiceImpl.restoreGames(this.entity, [gameCode]);
            return result;
        } catch (error) {
            return Promise.reject(error);
        }
    }

    private async updateStatus(entity: BaseEntity,
                               gameCode: string,
                               status: string,
                               skipStatusChangeValidation?: boolean): Promise<EntityGameCodeInfo> {
        const entityGame = await findOneEntityGame(entity, gameCode, undefined, true);
        this.validateChangeStatus(entityGame, status, skipStatusChangeValidation);
        if (!EXCLUDED_STATUSES.includes(status) && entity.isBrand()) {
            await validateGameSettings(entityGame.game, entityGame.settings, true, entity);
        }

        await EntityGameServiceImpl.updateEntityGamesInSubtree(entity, [entityGame.gameId], { status });
        entityGame.status = status;

        return entityGame.toCodeInfo();
    }

    private validateChangeStatus(entityGame: EntityGame,
                                 newStatus: string,
                                 skipStatusChangeValidation?: boolean): void {
        if (skipStatusChangeValidation) {
            return;
        }

        if (entityGame.parentGame) {
            const parentStatus = entityGame.parentGame.status;
            if (EXCLUDED_STATUSES.includes(parentStatus) && !EXCLUDED_STATUSES.includes(newStatus)) {
                throw new Errors.ParentSuspendedError();
            }

            if (parentStatus === ENTITY_GAME_STATUS.TEST && newStatus === ENTITY_GAME_STATUS.NORMAL) {
                throw new Errors.ValidationError("Parent's status is test, " +
                    "available statuses (test, suspended, hidden)");
            }
        }
    }

    @resetCache()
    public async bulkUpdateStatus(data: UpdateStatusesData,
                                  reason?: string): Promise<void> {

        EntityGameServiceImpl.validateMaxBulkUpdate(data.codes);

        const queries = new Map();

        queries.set("game", { code: { [Op.in]: data.codes } });

        const entityGames: EntityGame[] = await findAllEntityGames(this.entity, queries);
        const gameIds = entityGames.map(entityGame => entityGame.gameId);
        const isNormalOrTestGameStatus = data.status === ENTITY_GAME_STATUS.NORMAL
            || data.status === ENTITY_GAME_STATUS.TEST;
        if (this.entity.isBrand() && isNormalOrTestGameStatus) {
            for (const entityGame of entityGames) {
                await validateGameSettings(entityGame.game, entityGame.settings, true, this.entity);
            }
        }
        await EntityGameServiceImpl.updateEntityGamesInSubtree(this.entity, gameIds, { status: data.status });
        if (isNormalOrTestGameStatus || data.status === ENTITY_GAME_STATUS.HIDDEN) {
            await EntityGameServiceImpl.restoreGames(this.entity, data.codes);
        } else if (data.status === ENTITY_GAME_STATUS.SUSPENDED && !data.allowToFinishCurrentSession) {
            await EntityGameServiceImpl.suspendGames(this.entity, data.codes);
        }
    }

    @resetCache()
    public async bulkUpdateLimits(data: UpdateLimitsData): Promise<void> {

        EntityGameServiceImpl.validateMaxBulkUpdate(data.codes);

        const queries = new Map();

        queries.set("game", { code: { [Op.in]: data.codes } });

        const entityGames: EntityGame[] = await findAllEntityGames(this.entity, queries);
        const gameIds = entityGames.map(entityGame => entityGame.gameId);
        const gameTypesCount = new Set(entityGames.map(entityGame => entityGame.game.type)).size;
        const existGameCodes = entityGames.map(entityGame => entityGame.game.code);

        this.validateGameCodes(data.codes, existGameCodes);

        if (gameTypesCount > 1) {
            return Promise.reject(new Errors.UpdateMultipleGameTypesError());
        }

        for (const entityGame of entityGames) {
            try {
                validateLimitFilters(entityGame.game.type, data.limitFilters, entityGame.game.limits);
            } catch (err) {
                err.message = `Limits for game ${entityGame.game.code} are incorrect. ` + err.message;
                return Promise.reject(err);
            }
        }

        if (!data.merge) {
            await EntityGameServiceImpl.updateEntityGamesInSubtree(this.entity,
                gameIds, { limitFilters: data.limitFilters });
        } else {
            await this.patchEntityGamesInSubtree(this.entity, entityGames, data.limitFilters);
        }
    }

    private async patchEntityGamesInSubtree(entity: BaseEntity,
                                            entityGames: EntityGame[],
                                            limitFilters: LimitsByCurrencyCode) {
        await db.transaction(async (transaction: Transaction): Promise<any> => {
            await EntityGameServiceImpl.patchEntityGames(entityGames, limitFilters, transaction);

            const gameCodes = entityGames.map(entityGame => entityGame.game.code);

            await this.updateChildEntityGames(entity, gameCodes, limitFilters, transaction);
        });
    }

    private async updateChildEntityGames(entity: BaseEntity,
                                         gameCodes: string[],
                                         limitFilters: LimitsByCurrencyCode,
                                         transaction: Transaction) {
        if ((entity as Entity).child) {
            for (const child of (entity as Entity).child) {
                const queries = new Map();
                queries.set("game", { code: { [Op.in]: gameCodes } });
                const childEntityGames: EntityGame[] = await findAllEntityGames(child, queries);

                if (childEntityGames && childEntityGames.length) {
                    await EntityGameServiceImpl.patchEntityGames(childEntityGames, limitFilters, transaction);

                    await this.updateChildEntityGames(child, gameCodes, limitFilters, transaction);
                }
            }
        }
    }

    private static async patchEntityGames(entityGames: EntityGame[],
                                          limitFilters: LimitsByCurrencyCode,
                                          transaction: Transaction) {

        for (const entityGame of entityGames) {
            const mergedLimitFilters = mergeDeep({}, entityGame.limitFilters, limitFilters);

            try {
                await EntityGameModel.update(
                    { limitFilters: mergedLimitFilters },
                    {
                        where: { entityId: entityGame.entityId, gameId: entityGame.gameId },
                        transaction
                    }
                );
            } catch (err) {
                return Promise.reject(new Errors.BulkActionDbError());
            }
        }
    }

    private static validateMaxBulkUpdate(codes: string[]) {
        if (!Array.isArray(codes)) {
            throw new Errors.ValidationError("Game codes must be array");
        }

        if (codes.length > GROUP_ACTION_MAX_ITEMS) {
            throw new Errors.BulkActionLimitError(GROUP_ACTION_MAX_ITEMS, codes.length);
        }

        if (!codes.length) {
            throw new Errors.ValidationError("Game codes are not found");
        }
    }

    private validateGameCodes(codes: string[], existGameCodes: string[]) {
        const notExistedCodes = codes.filter(code => !existGameCodes.includes(code));

        if (notExistedCodes.length) {
            throw new Errors.ValidationError(`Games with codes "${notExistedCodes}" are not exist for entity`);
        }
    }

    private static async updateEntityGamesInSubtree(entity: BaseEntity,
                                                    gameIds: number[],
                                                    dataToUpdate: EntityGameAttributes,
                                                    transaction?: Transaction): Promise<void> {
        const idsToUpdate = EntityGameServiceImpl.getEntityTreeIds(entity);
        gameIds.sort();
        try {
            await EntityGameModel.update(
                dataToUpdate,
                {
                    where: { entityId: { [Op.in]: idsToUpdate }, gameId: { [Op.in]: gameIds } },
                    transaction
                });

        } catch (err) {
            return Promise.reject(new Errors.BulkActionDbError());
        }
    }

    @resetCache()
    public async update(gameCode: string,
                        gameData?: EntityGameData,
                        transaction?: Transaction,
                        allowToFinishCurrentSession?: boolean): Promise<EntityGameCodeInfo> {
        const entityGame = await findOneEntityGame(this.entity, gameCode, undefined, true);
        const currentEntityGame = { ...entityGame };
        const limitFilters = gameData?.limitFilters;
        if (limitFilters) {
            validateLimitFilters(entityGame.game.type, limitFilters, entityGame.game.limits);
            entityGame.limitFilters = limitFilters;
        }
        const oldStatus = entityGame.status;
        const newStatus = gameData?.status || entityGame.status;
        const newSettings = gameData?.settings || {};

        // validate game settings
        let mergedSettings = newSettings;
        if (!this.entity.isMaster()) {
            const parentGame = await findOneEntityGame((this.entity as ChildEntity).getParent(), gameCode);
            mergedSettings = filterObjectFields(mergeArrayToObject([
                parentGame.settings,
                entityGame.settings,
                newSettings
            ]));
        }

        const urlParams = gameData?.urlParams;

        if (urlParams) {
            if (typeof urlParams !== "object" || Array.isArray(urlParams)) {
                throw new Errors.ValidationError("urlParams must be object");
            }

            entityGame.urlParams = filterObjectFields(gameData.urlParams);
        }
        await validateGameSettings(entityGame.game, mergedSettings,
            this.entity.isBrand() && newStatus === ENTITY_GAME_STATUS.NORMAL, this.entity);

        entityGame.settings = filterObjectFields(mergeArrayToObject([entityGame.settings, newSettings]));

        if (gameData?.externalGameId) {
            entityGame.externalGameId = gameData.externalGameId;
        }

        if (gameData?.domain !== undefined) {
            entityGame.domain = gameData.domain;
        }

        if (gameData?.title !== undefined) {
            entityGame.title = gameData.title;
        }
        const features = gameData?.features;
        if (features !== undefined) {
            if (typeof features !== "object" || Array.isArray(features)) {
                return Promise.reject(new Errors.ValidationError("features should be an object"));
            }
            if (features.translations !== undefined) {
                const errors = validateTranslations(features.translations, "features");
                if (errors.length) {
                    return Promise.reject(new Errors.ValidationError(errors));
                }
            }
            entityGame.features = filterObjectFields(mergeArrayToObject([
                entityGame.features,
                features
            ]));
        }

        this.validateChangeStatus(entityGame, newStatus);
        if (transaction) {
            await EntityGameServiceImpl.updateGameEntityAndStatusWithTransaction(
                entityGame, this.entity, newStatus, transaction);
        } else {
            await db.transaction(async (newTransaction: Transaction): Promise<any> => {
                await EntityGameServiceImpl.updateGameEntityAndStatusWithTransaction(
                    entityGame, this.entity, newStatus, newTransaction);
            });
        }

        gameRTPHistoryService.get().logEntityGame(entityGame, currentEntityGame);
        const isNormalOrTestStatus = [
            ENTITY_GAME_STATUS.TEST,
            ENTITY_GAME_STATUS.NORMAL
        ].includes(
            newStatus as ENTITY_GAME_STATUS);
        if (!this.entity.isMaster() && newStatus !== oldStatus) {
            if (isNormalOrTestStatus) {
                await EntityGameServiceImpl.restoreGames(this.entity, [gameCode]);
            } else if (newStatus === ENTITY_GAME_STATUS.SUSPENDED && !allowToFinishCurrentSession) {
                await EntityGameServiceImpl.suspendGames(this.entity, [gameCode]);
            }
        }

        return entityGame.toCodeInfo();
    }

    @resetCache()
    public async updateLimits(gameCode: string,
                              newLimitFilters?: LimitFiltersByCurrency): Promise<EntityGameCodeInfo> {

        if (this.entity.isSuspended()) {
            return Promise.reject(new Errors.ParentSuspendedError());
        }
        const entityGame = await findOneEntityGame(this.entity, gameCode);
        const limitFilters = this.sanitizeLimitFiltersForOperator(newLimitFilters);

        validateLimitFilters(entityGame.game.type, limitFilters, entityGame.game.limits);
        entityGame.limitFilters = limitFilters;

        await db.transaction(async (newTransaction: Transaction): Promise<any> => {
            await EntityGameServiceImpl.updateGameEntityAndStatusWithTransaction(
                entityGame, this.entity, undefined, newTransaction);
        });

        return entityGame.toCodeInfo();
    }

    private sanitizeLimitFiltersForOperator(receivedLimits: LimitFiltersByCurrency): LimitFiltersByCurrency {
        const limitFilters: LimitFiltersByCurrency = {};
        if (!receivedLimits) {
            return receivedLimits;
        }

        Object.keys(receivedLimits).forEach(currency => {
            limitFilters[currency] = {
                stakeMax: receivedLimits[currency].stakeMax,
                stakeMin: receivedLimits[currency].stakeMin
            };
        });

        return limitFilters;
    }

    private static async updateGameEntityAndStatusWithTransaction(entityGame: EntityGame,
                                                                  entity: BaseEntity,
                                                                  newStatus: string,
                                                                  transaction: Transaction) {
        if (newStatus && newStatus !== entityGame.status) {
            await EntityGameServiceImpl.updateEntityGamesInSubtree(
                entity,
                [entityGame.gameId],
                { status: newStatus },
                transaction
            );
            entityGame.status = newStatus;
        }

        // TODO: need to fix 'any'
        await EntityGameModel.update(entityGame as any, { where: { id: entityGame.id }, transaction });
    }

    private static async suspendGames(entity: BaseEntity, games: string[]): Promise<void> {
        const idsToUpdate = EntityGameServiceImpl.getEntityTreeIds(entity);
        await SuspendGameService.get().suspendGames(idsToUpdate, games);
        await defaultOperatorInfoUpdater.get().suspendGame({ path: entity.path, gameCodes: games });
    }

    private static async restoreGames(entity: BaseEntity, games: string[]): Promise<void> {
        const idsToUpdate = EntityGameServiceImpl.getEntityTreeIds(entity);
        await SuspendGameService.get().restoreGames(idsToUpdate, games);
        await defaultOperatorInfoUpdater.get().restoreGame({ path: entity.path, gameCodes: games });
    }

    private static getEntityTreeIds(entity: BaseEntity): number[] {
        const childIds: number[] = getChildIds(entity);
        return [entity.id, ...childIds];
    }

    @resetCache()
    public async delete(gameCode: string,
                        isLiveGame = false,
                        force = false,
                        transaction?: Transaction): Promise<EntityGameCodeInfo> {
        return await withTransaction(transaction, tr => this.destroy(gameCode, isLiveGame, force, true, true, tr));
    }

    @resetCache()
    public async bulkDelete(gameCodes: string[],
                            isLiveGame = false): Promise<EntityGameCodeInfo[]> {
        return await db.transaction(async tr => {
            const entityGames: EntityGameCodeInfo[] = [];
            for (const gameCode of gameCodes) {
                const item = await this.destroy(gameCode, isLiveGame, false, false, false, tr);
                if (item) {
                    entityGames.push(item);
                }
            }
            return entityGames;
        });
    }

    private async destroy(gameCode: string,
                          isLiveGame: boolean,
                          force: boolean,
                          raiseErrorIfNotFound: boolean,
                          needForce: boolean,
                          transaction: Transaction): Promise<EntityGameCodeInfo> {
        const entityGameRecord = await EntityGameModel.findOne({
            transaction,
            include: [
                {
                    required: true,
                    model: GameModel,
                    where: {
                        code: gameCode,
                        type: isLiveGame ? GAME_TYPES.live : { [Op.ne]: GAME_TYPES.live }
                    }
                }
            ],
            where: {
                entityId: this.entity.id
            }
        });
        if (!entityGameRecord) {
            if (raiseErrorIfNotFound) {
                throw new Errors.EntityGameNotFound(gameCode);
            }
            return undefined;
        }
        const entityGame = new EntityGameImpl(entityGameRecord);
        if (!force) {
            await validateGameRelations(this.entity, [entityGame]);
        }
        for (const childEntity of (this.entity as Entity).child || []) {
            await this.destroyFromChild(childEntity, entityGame.game.id, force, needForce, transaction);
        }
        if (force) {
            await removeGameRelations(this.entity, [entityGame], transaction);
        }
        await entityGameRecord.destroy({ transaction });
        if (entityGame.isSuspended()) {
            await EntityGameServiceImpl.restoreGames(this.entity, [gameCode]);
        }
        return entityGame.toCodeInfo();
    }

    private async destroyFromChild(entity: BaseEntity,
                                   gameId: number,
                                   force: boolean,
                                   needForce: boolean,
                                   transaction: Transaction): Promise<void> {
        for (const childEntity of (entity as Entity).child || []) {
            await this.destroyFromChild(childEntity, gameId, force, needForce, transaction);
        }
        const entityGameRecord = await EntityGameModel.findOne({
            transaction,
            where: {
                entityId: entity.id,
                gameId
            }
        });
        if (entityGameRecord) {
            if (!force) {
                if (needForce) {
                    throw new Errors.FailedToDeleteEntityGameNeedForce();
                } else {
                    throw new Errors.FailedToDeleteEntityGame();
                }
            }
            await removeGameRelations(entity, [new EntityGameImpl(entityGameRecord)], transaction);
            await entityGameRecord.destroy({ transaction });
        }
    }
}

export const getEntityGameService = (entity: BaseEntity): EntityGameService => {
    return new EntityGameServiceImpl(entity);
};

export function resetCache() {
    return (target, key, descriptor) => {
        const originalMethod = descriptor.value;

        descriptor.value = async function(...argsOfDecoratedFunc: any[]) {
            const result = await originalMethod.apply(this, argsOfDecoratedFunc);
            await LobbyCache.reset();
            await favoriteGamesCache.reset();

            return result;
        };

        return descriptor;
    };
}

export async function getEntityGame(entity: BaseEntity,
                                    gameCode: string,
                                    throwErrorForHidden?: boolean,
                                    ignoreGameIsSuspended?: boolean,
                                    ignoreEntityGameNotFound?: boolean): Promise<EntityGame | undefined> {
    const entityGame: EntityGame = await findOneEntityGame(
        entity,
        gameCode,
        undefined,
        false,
        ignoreEntityGameNotFound
    );

    if (!entityGame) {
        if (ignoreEntityGameNotFound) {
            return undefined;
        }
        throw new Errors.GameNotFoundError(gameCode);
    }
    if (entityGame.isSuspended() && !ignoreGameIsSuspended) {
        throw new Errors.GameSuspendedError(gameCode);
    }
    if (throwErrorForHidden && entityGame.status === ENTITY_GAME_STATUS.HIDDEN) {
        throw new Errors.GameSuspendedError(gameCode);
    }

    return entityGame;
}

async function withTransaction<T>(trx: Transaction | undefined,
                                  callback: (trx: Transaction) => Promise<T>): Promise<T> {
    return trx ? callback(trx) : db.transaction(callback);
}
