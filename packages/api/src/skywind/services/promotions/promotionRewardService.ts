import { BrandEntity } from "../../entities/brand";
import { PROMO_STATUS } from "../../entities/promotion";
import * as Errors from "../../errors";
import { Player } from "../../entities/player";
import {
    PromotionToPlayer,
    PromotionToPlayerImpl,
    PromotionToPlayerStatus
} from "../../models/promotionPlayer";
import {
    PlayerPromotionDb,
    PlayerPromotionFreeBetLeft,
    PlayerPromotionInfo,
    PromotionToPlayerList
} from "./playerPromotionDb";
import PendingRewards, { PendingReward } from "./pendingPromotion";
import * as PromoService from "./promotion";
import { Op, Transaction, WhereOptions } from "sequelize";
import { PagingHelper } from "../../utils/paginghelper";
import { WalletFacade } from "@skywind-group/sw-management-wallet";
import PlayerSessionPromotion from "./playerSessionPromotion";
import { PlayerPromotionWalletFacade } from "@skywind-group/sw-management-promo-wallet";
import { IsActiveResult, PromotionPlayersUpdate } from "./promotionPlayersUpdate";

import { ITrxData } from "@skywind-group/sw-wallet";
import logger from "../../utils/logger";
import { getBrandPlayerService } from "../brandPlayer";
import { BaseEntity, ENTITY_TYPE } from "../../entities/entity";
import { getPromo, PromotionImpl } from "./promotion";
import { PromotionFreebetRewardImpl } from "./promotionReward";
import { PlayerFreebetPromotionInfo } from "./types/playerFreebetPromotion";
import { getEntitySettings } from "../settings";
import { getEntityJurisdictionService } from "../entityJurisdiction";

const log = logger("player-promotion");

function verifyConsistency() {
    return (target, key, descriptor) => {
        const originalMethod = descriptor.value;

        descriptor.value = async function() {
            await this.verifyConsistency(arguments[0]);

            return originalMethod.apply(this, arguments);
        };
    };
}

export abstract class PromotionRewardService {

    constructor(protected readonly brand: BrandEntity, protected readonly sharedPromoEnabled = false) {
    }

    @verifyConsistency()
    public async addPlayers(promo: PromotionImpl,
                            playerCodes: string[],
                            data?: any,
                            transaction?: Transaction,
                            entity?: BaseEntity): Promise<void> {
        if (promo.hasFinished()) {
            return Promise.reject(new Errors.PromoIsNotInValidState());
        }

        const brandPlayers = new Map<string, Player>();
        for (const player of await getBrandPlayerService()
            .searchPromoPlayers(this.brand, { code: { [Op.in]: playerCodes } })) {
            brandPlayers.set(player.code, player);
        }

        const promoPlayers = new Map<string, PromotionToPlayerImpl>();
        for (const promoPlayer of await PlayerPromotionDb.getPromotionPlayers(promo.getId())) {
            promoPlayers.set(promoPlayer.playerCode, promoPlayer);
        }

        const toAdd: PromotionToPlayerImpl[] = [];
        const duplicates: string[] = [];
        const notFound: string[] = [];

        for (const code of playerCodes) {
            const player = brandPlayers.get(code);
            if (!(promo.isExternal() || player || this.brand.type === ENTITY_TYPE.ENTITY || this.brand.isMerchant)) {
                notFound.push(code);
                continue;
            }
            let promoPlayer = new PromotionToPlayerImpl();
            promoPlayer.promotionId = promo.getId();
            promoPlayer.playerCode = code;
            if (player) {
                promoPlayer.playerId = player.id;
                promoPlayer.playerCurrency = player.currency;
            }
            promoPlayer.status = PromotionToPlayerStatus.PENDING;
            promoPlayer.data = data;

            const existing: PromotionToPlayerImpl = promoPlayers.get(code);
            if (existing) {
                if (!existing.isRevoked()) {
                    duplicates.push(code);
                } else {
                    promoPlayer = existing;
                    promoPlayer.status = PromotionToPlayerStatus.PENDING;
                    promoPlayer.startedAt = null;
                    promoPlayer.expireAt = null;
                    promoPlayer.rewardedAt = null;
                    promoPlayer.data = data;

                    toAdd.push(promoPlayer);
                }
            } else {
                toAdd.push(promoPlayer);
            }
        }

        if (notFound.length) {
            return Promise.reject(new Errors.PromotionPlayersValidationError("Players not found", notFound));
        }

        if (duplicates.length) {
            return Promise.reject(new Errors.PromotionPlayersValidationError("Promotion already exist", duplicates));
        }

        const update = new PromotionPlayersUpdate(promo, toAdd);
        if (promo.isActive()) {
            await this.confirmPlayers(update, transaction);
            if (promo.isRunning()) {
                const { forcePendingRewards } = await getEntitySettings(entity.path);
                const jurisdiction = await getEntityJurisdictionService().findOneByEntityId(entity.id);
                const shouldForcePendingRewards = forcePendingRewards || jurisdiction?.settings?.dynamicMaxTotalBetLimitEnabled;
                await this.rewardPlayers(
                    update,
                    new Date(),
                    false,
                    transaction,
                    !!shouldForcePendingRewards
                );
            }
        }

        await update.commit(transaction);

        if (promo.isRunning()) {
            await this.notifyStarted(promo, toAdd);
        }
    }

    @verifyConsistency()
    public async start(promo: PromotionImpl, promoPlayers: PromotionToPlayerList): Promise<void> {
        const update = new PromotionPlayersUpdate(promo, promoPlayers);

        update.setPromoEverStarted();

        await this.rewardPlayers(update, new Date(), false);

        await update.commit();

        await this.notifyStarted(promo, promoPlayers);
    }

    @verifyConsistency()
    public async updatePlayer(promo: PromotionImpl, playerPromo: PromotionToPlayerImpl, data: any): Promise<void> {
        if (!this.updatePlayers) {
            return Promise.reject(new Errors.OperationForbidden());
        }
        if (!playerPromo.isRunning()) {
            return Promise.reject(new Errors.PromoIsNotInValidState());
        }

        const update = new PromotionPlayersUpdate(promo, [playerPromo]);

        await this.updatePlayers(update, data);

        await update.commit();
    }

    @verifyConsistency()
    public async updatePlayersList(promo: PromotionImpl,
                                   promoPlayers: PromotionToPlayerImpl[],
                                   data: any): Promise<void> {
        if (!this.updatePlayers) {
            return Promise.reject(new Errors.OperationForbidden());
        }
        for (const playerPromo of promoPlayers) {
            if (!playerPromo.isRunning()) {
                return Promise.reject(new Errors.PromoIsNotInValidState());
            }
        }

        const update = new PromotionPlayersUpdate(promo, promoPlayers);

        await this.updatePlayers(update, data);

        await update.commit();
    }

    /**
     * Safe player removal.
     */
    @verifyConsistency()
    public async removePlayer(promo: PromotionImpl, playerPromo: PromotionToPlayerImpl): Promise<void> {
        if (playerPromo.isRunning() || playerPromo.hasFinished()) {
            return Promise.reject(new Errors.PromoIsNotInValidState());
        }

        const update = new PromotionPlayersUpdate(promo, [playerPromo]);

        await this.removePlayers(update);

        await update.commit();
    }

    /**
     * Force player removal. If promo already started, player wallet will be reset.
     */
    @verifyConsistency()
    public async revokePlayer(promo: PromotionImpl, playerPromo: PromotionToPlayerImpl): Promise<void> {
        const update = new PromotionPlayersUpdate(promo, [playerPromo]);

        if (playerPromo.rewardedAt) {
            await this.revokePlayers(update);
        } else {
            await this.removePlayers(update);
        }

        await update.commit();
    }

    public async getGamePromotion(playerCode: string,
                                  playerCurrency: string,
                                  gameCode: string,
                                  promoType: string,
                                  brandId: number): Promise<any> {
        let promo = await this.getPlayerGamePromotion(playerCode, playerCurrency, gameCode);

        if (!promo) {
            let doPendingReward = false;

            if (this.sharedPromoEnabled || this.brand.isMerchant) {
                doPendingReward = true;
            } else {
                const pendingReward = await PendingRewards.findPending(brandId, playerCode, promoType);
                if (pendingReward) {
                    const promoImpl = await PromoService.findOne({ id: pendingReward.promoId });
                    if (promoImpl && promoImpl.isStartRewardOnGameOpen()) {
                        doPendingReward = true;
                    }
                }
            }

            if (doPendingReward) {
                const rewarded = await this.rewardPending(playerCode, playerCurrency, promoType);
                if (rewarded) {
                    promo = await this.getPlayerGamePromotion(playerCode, playerCurrency, gameCode);
                }
            }
        }

        return promo;
    }

    protected abstract getPromotionsFromWallet(playerCode: string,
                                               playerCurrency: string): Promise<PlayerPromotionInfo[]>;

    public async getPromotionInfo(playerPromo: PromotionToPlayer)
        : Promise<PlayerPromotionInfo & PlayerFreebetPromotionInfo> {
        let data: PlayerPromotionInfo;
        if (playerPromo.rewardedAt) {
            const promos = await this.getPromotionsFromWallet(playerPromo.playerCode, playerPromo.playerCurrency);
            data = promos.find((p) => p.promoId === playerPromo.promotionId);
        }
        const result = data || {
            promoId: playerPromo.promotionId,
            status: playerPromo.status
        };
        if (playerPromo.playedAt) {
            (result as any).playedAt = playerPromo.playedAt;
        }
        if (playerPromo.expireAt) {
            if (playerPromo.expireAt < new Date()) {
                result.status = data ? "expired" : "finished";
            }
            result.expireAt = playerPromo.expireAt;
        }
        if (playerPromo.external_id) {
            result.externalId = playerPromo.external_id;
        }
        return result;
    }

    public async getFreeBeetLeft(playerPromo: PromotionToPlayer,
                                 entity: BaseEntity): Promise<PlayerPromotionFreeBetLeft> {
        const promoInfo = await this.getPromotionInfo(playerPromo);
        let freeBetLeft: number;
        if (promoInfo.freebets) {
            freeBetLeft = 0;
            for (const reward of promoInfo.freebets) {
                freeBetLeft += reward.amount;
            }
        } else { // if freebets are missing it means promo is not in wallet, so we take info form pg
            const promo = await getPromo(playerPromo.promotionId, entity);
            const rewards = promo.getRewards();
            freeBetLeft = 0;
            for (const reward of rewards) {
                if (reward instanceof PromotionFreebetRewardImpl) {
                    const { freebetAmount } = reward.toInfo();
                    // if rewardedAt != null and not in wallet it means it is amount: 0 (consumed)
                    freeBetLeft += playerPromo.rewardedAt ? 0 : freebetAmount;
                }
            }
        }
        return {
            freeBetLeft,
            promoId: playerPromo.promotionId,
            status: playerPromo.status,
            expireAt: playerPromo.expireAt,
            playerCode: playerPromo.playerCode,
            playedAt: playerPromo.rewardedAt
        };
    }

    @verifyConsistency()
    public async getPlayers(promo: PromotionImpl,
                            query: WhereOptions<any> = {},
                            limitQuery: WhereOptions<any> = {}): Promise<PlayerPromotionInfo[]> {
        const playersInPromo: PromotionToPlayerList = await PlayerPromotionDb.getPromotionPlayers(promo.getId(),
            query, limitQuery);

        const promotionsPlayersInfo: PlayerPromotionInfo[] = [];

        for (const playerPromo of playersInPromo) {
            const data = await this.getPromotionInfo(playerPromo);
            data.playerCode = playerPromo.playerCode;
            promotionsPlayersInfo.push(data);
        }

        return PagingHelper.copyInfo(promotionsPlayersInfo, playersInPromo);
    }

    @verifyConsistency()
    public async activatePromo(promo: PromotionImpl, userId: number): Promise<void> {
        const players = await PlayerPromotionDb.getPromotionPlayers(promo.getId(), {
            status: PromotionToPlayerStatus.PENDING
        });

        const update = new PromotionPlayersUpdate(promo, players, userId);
        update.updatePromoStatus(PROMO_STATUS.ACTIVE);

        await this.confirmPlayers(update);
        const currentDate = new Date();

        if (promo.getStartDate() <= currentDate && promo.getEndDate() >= currentDate) {
            update.setPromoEverStarted();
            await this.rewardPlayers(update, currentDate, false);
        }

        await update.commit();

        if (promo.isRunning()) {
            await this.notifyStarted(promo, players);
        }
    }

    @verifyConsistency()
    public async inactivatePromo(promo: PromotionImpl, userId: number): Promise<void> {
        const players = await PlayerPromotionDb.getPromotionPlayers(promo.getId(), {
            status: { [Op.in]: [PromotionToPlayerStatus.CONFIRMED, PromotionToPlayerStatus.STARTED] }
        });

        const filteredPlayers
            = players.filter(x => x.rewardedAt === null || x.status === PromotionToPlayerStatus.CONFIRMED);

        const update = new PromotionPlayersUpdate(promo, filteredPlayers, userId);
        update.updatePromoStatus(PROMO_STATUS.INACTIVE);
        await this.resetConfirmedPlayers(update);

        const pendingRewards = filteredPlayers.filter(x => x.rewardedAt === null)
            .map((x: PromotionToPlayerImpl) => {
                return {
                    brandId: promo.getBrandId(),
                    playerCode: x.playerCode,
                    promoType: promo.getType(),
                    promoId: promo.getId(),
                    startDate: new Date(promo.getStartDate()).getTime(),
                    createdAt: new Date(promo.getCreatedAt()).getTime(),
                    expireDate: null
                };
            });

        update.removePendingRewards(pendingRewards);

        await update.commit();
    }

    public async verifyConsistency(promo: PromotionImpl): Promise<void> {
        // verify if promo has been finished or expired, but there are left confirmed players
        // that were not rewarded with promotion for some reason (e.g. cron job failure)
        if (promo.hasFinished()) {
            const players = await PlayerPromotionDb.getPromotionPlayers(promo.getId(), {
                status: PromotionToPlayerStatus.CONFIRMED,
                activeTransactionId: null
            });
            if (players.length) {
                const update = new PromotionPlayersUpdate(promo, players);

                await this.resetConfirmedPlayers(update);

                await update.commit();
            }

            return;
        }

        // verify if previous promo modification has failed and there are left not completed wallet transactions
        const activeTrxPlayers = await PlayerPromotionDb.getPromotionPlayers(promo.getId(), {
            activeTransactionId: { [Op.ne]: null }
        });
        if (!activeTrxPlayers.length) {
            return;
        }

        const trxIds = new Set(activeTrxPlayers.map((player) => player.activeTransactionId));
        for (const trxId of trxIds) {
            const players = activeTrxPlayers.filter((player) => player.activeTransactionId === trxId);
            const operationId = players[0].activeOperation.operationId;

            const trxData: ITrxData = await WalletFacade.findCommittedTransaction(trxId, operationId);
            if (trxData) {
                const newPromoStatus = players[0].activeOperation.promoStatus;
                if (newPromoStatus) {
                    promo.setStatus(newPromoStatus);
                }
                await PlayerPromotionDb.commitPromotionPlayersUpdate(trxId, players, promo);
                players.forEach((p) => PromotionPlayersUpdate.commitPlayerPromoTransaction(p));
            } else {
                await PlayerPromotionDb.rollbackPromotionPlayersUpdate(trxId, players, promo);
                players.forEach((p) => PromotionPlayersUpdate.rollbackPlayerPromoTransaction(p));
            }
        }
    }

    private async notifyStarted(promo: PromotionImpl, promoPlayers: PromotionToPlayer[]) {
        for (const playerPromo of promoPlayers) {
            if (playerPromo.status === PromotionToPlayerStatus.STARTED) {
                await PlayerSessionPromotion.notifyPromoAdded(
                    this.brand.id, playerPromo.playerCode, playerPromo.playerCurrency, promo);
            }
        }
    }

    protected async confirmPlayers(update: PromotionPlayersUpdate, transaction?: Transaction) {
        update.updateAllPlayerPromos({ status: PromotionToPlayerStatus.CONFIRMED });
    }

    protected async resetConfirmedPlayers(update: PromotionPlayersUpdate) {
        update.updateAllPlayerPromos({
            status: PromotionToPlayerStatus.PENDING,
            startedAt: null
        });
    }

    protected async removePlayers(update: PromotionPlayersUpdate) {
        update.updateAllPlayerPromos({ status: PromotionToPlayerStatus.REVOKED });
    }

    private async rewardPending(playerCode: string,
                                playerCurrency: string,
                                promoType: string): Promise<PromotionToPlayerImpl> {
        const pendingReward = await this.findPendingReward(playerCode, promoType);
        if (!pendingReward) {
            return;
        }

        let playerPromo: PromotionToPlayerImpl;
        try {
            playerPromo = await PlayerPromotionDb.getPlayerPromotion(
                playerCode, pendingReward.promoId);

            if (!playerPromo || !playerPromo.isRunning()) {
                if (playerPromo && playerPromo.hasFinished()) {
                    // prolong
                    await PendingRewards.updatePending([{ ...pendingReward, expireDate: null }]);
                }
                return;
            }

            playerPromo.playerCurrency = playerCurrency;
            playerPromo.brandId = this.brand.id;

            const promo: PromotionImpl = await PromoService.findOne({ id: pendingReward.promoId });

            const update = new PromotionPlayersUpdate(promo, [playerPromo]);

            update.updatePlayerPromo(playerPromo, { playerCurrency, brandId: this.brand.id });

            await this.rewardPlayers(update, playerPromo.startedAt, true);

            update.updatePendingReward({
                brandId: promo.getBrandId(),
                playerCode: playerCode,
                promoId: pendingReward.promoId,
                promoType: pendingReward.promoType,
                startDate: pendingReward.startDate,
                createdAt: pendingReward.createdAt,
                expireDate: null
            });

            await update.commit();

            return playerPromo;
        } catch (err) {
            log.error(err, "Failed to reward pending promotion. Will be retried on next player visit", playerPromo);
        }
    }

    private async findPendingReward(playerCode: string, promoType: string): Promise<PendingReward> {
        const pending = await PendingRewards.findPending(this.brand.id, playerCode, promoType);
        if (!pending) {
            if (this.sharedPromoEnabled) {
                return PendingRewards.findPending(this.brand.parent, playerCode, promoType);
            }
        }

        return pending;
    }

    protected async isPromotionActive(promo: PromotionToPlayer, promoType: string): Promise<IsActiveResult> {
        if (promo.status === PromotionToPlayerStatus.STARTED && promo.rewardedAt) {
            const facade = PlayerPromotionWalletFacade.create(this.brand.id, promo.playerCode, promo.playerCurrency);
            return await facade.hasPromo(promo.promotionId, promoType) as IsActiveResult;
        }
        if (promo.status === PromotionToPlayerStatus.STARTED && !promo.rewardedAt) {
            return {
                result: promo.expireAt > new Date()
            };
        }
        return {
            result: promo.status === PromotionToPlayerStatus.CONFIRMED
        };
    }

    protected abstract rewardPlayers(update: PromotionPlayersUpdate,
                                     startDate: Date,
                                     isPendingReward: boolean,
                                     transaction?: Transaction,
                                     forcePending?: boolean): Promise<void>;

    protected async updatePlayers?(update: PromotionPlayersUpdate, data: any): Promise<void>;

    protected abstract revokePlayers(update: PromotionPlayersUpdate): Promise<void>;

    protected abstract getPlayerGamePromotion(playerCode: string,
                                              playerCurrency: string,
                                              gameCode: string): Promise<any>;
}
