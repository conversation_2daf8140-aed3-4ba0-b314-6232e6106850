import * as Errors from "../../errors";
import {
    PromotionToPlayer,
    PromotionToPlayerImpl,
    PromotionToPlayerStatus
} from "../../models/promotionPlayer";
import { Models } from "../../models/models";
import { sequelize as db } from "../../storage/db";
import { FindOptions, literal, Op, QueryTypes, Transaction, WhereOptions } from "sequelize";
import { PromotionImpl, savePromo } from "./promotion";
import * as FilterService from "../filter";
import { PagingHelper, PagingInfo } from "../../utils/paginghelper";

const PromotionToPlayerModel = Models.PromotionToPlayerModel;

export interface PlayerPromotionInfo {
    promoId: number;
    playerCode?: string;
    status: string;
    playedAt?: Date;
    expireAt?: Date;
    externalId?: string;
}

export interface PlayerPromotionFreeBetLeft extends PlayerPromotionInfo {
    freeBetLeft: number;
}

export interface PromotionAddResult {
    [playerCode: string]: string;
}

export interface PromotionUpdateResult {
    [playerCode: string]: PlayerPromotionInfo[] | { error: string };
}

export type PromotionToPlayerList = (PromotionToPlayerImpl & PagingInfo)[];
/**
 * Handles mapping of promotions to players and all active wallet transaction ids.
 */
export class PlayerPromotionDbImpl {

    private static BASE_SELECT_QUERY = ` 
p.id AS id,
p.active_transaction_id AS active_transaction_id,
p.active_operation AS active_operation,
p.player_id AS player_id,
p.player_code AS player_code,
p.player_currency AS player_currency,
p.promotion_id AS promotion_id,
CASE WHEN pu.status IS NULL THEN p.status ELSE pu.status END AS status,
CASE WHEN pu.finish_status IS NULL THEN p.finish_status ELSE pu.finish_status END AS finish_status,
p.data AS data,
p.started_at AS started_at,
p.rewarded_at AS rewarded_at,
p.expire_at AS expire_at,
CASE WHEN pu.played_at IS NULL THEN p.played_at ELSE pu.played_at END AS played_at,
CASE WHEN pu.finished_at IS NULL THEN p.finished_at ELSE pu.finished_at END AS finished_at,
p.created_at AS created_at,
p.updated_at AS updated_at,
p.brand_id AS brand_id,
(SELECT external_id FROM promotions WHERE id = p.promotion_id LIMIT 1) as external_id
FROM promotion_players p LEFT JOIN 
(SELECT DISTINCT ON (player_code, promotion_id) * FROM promotion_players_update 
ORDER BY player_code, promotion_id, inserted_at desc) AS pu
ON p.player_code = pu.player_code and p.promotion_id = pu.promotion_id AND p.updated_at < pu.inserted_at`;

    public static sortableKeys = ["expireAt", "playerCode"];
    public static DEFAULT_SORT_KEY = "playerCode";
    public static DEFAULT_OFFSET = 0;
    public static DEFAULT_LIMIT = 20;
    public static DEFAULT_SORT_ORDER = "ASC";
    public static queryParamsKeys = [...PlayerPromotionDbImpl.sortableKeys, "sortBy", "sortOrder"];
    public static limitQueryParamsKeys = ["offset", "limit"];

    private buildOptionsToGetPlayers(promotionId: number,
                                     query: WhereOptions<any> = {},
                                     limitQuery?: WhereOptions<any>): FindOptions<any> {
        const sortBy = FilterService.getSortKey(
            query,
            PlayerPromotionDbImpl.sortableKeys,
            PlayerPromotionDbImpl.DEFAULT_SORT_KEY
        );

        const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || PlayerPromotionDbImpl.DEFAULT_SORT_ORDER;

        const options: FindOptions<any> = {
            where: { promotionId, ...query },
            order: literal(`"${sortBy}" ${sortOrder}`),
        };

        if (limitQuery) {
            options.offset = FilterService.valueFromQuery(limitQuery, "offset") || PlayerPromotionDbImpl.DEFAULT_OFFSET;
            options.limit = FilterService.valueFromQuery(limitQuery, "limit") || PlayerPromotionDbImpl.DEFAULT_LIMIT;
        }

        return options;
    }

    public async getPromotionPlayers(promotionId: number,
                                     query: WhereOptions<any> = {},
                                     limitQuery?: WhereOptions<any>): Promise<PromotionToPlayerList> {
        const mapper = item => Object.assign(new PromotionToPlayerImpl(), item.toJSON());
        const options = this.buildOptionsToGetPlayers(promotionId, query, limitQuery);
        if (limitQuery) {
            return PagingHelper.findAndCountAll(PromotionToPlayerModel, options, mapper);
        } else {
            return PromotionToPlayerModel.findAll(options).then((result) => result.map(mapper));
        }
    }

    public async countPromotionPlayers(promotionId: number): Promise<number> {
        const result = await PromotionToPlayerModel.aggregate("playerCode", "count",
            {
                distinct: true,
                where: {
                    promotionId,
                    status: { [Op.ne] : PromotionToPlayerStatus.REVOKED }
                }
            }
        );

        return +result;
    }

    public async getPlayerPromotion(playerCode: string, promotionId: number): Promise<PromotionToPlayerImpl> {
        const selectData = `SELECT ${PlayerPromotionDbImpl.BASE_SELECT_QUERY} ` +
            "WHERE p.player_code = :playerCode AND p.promotion_id = :promotionId AND p.status != 'revoked'";

        const result = await db.query(selectData, {
            replacements: { playerCode, promotionId },
            model: PromotionToPlayerModel,
            type: QueryTypes.SELECT,
            mapToModel: true
        });

        return result && result.length ? Object.assign(new PromotionToPlayerImpl(), result[0].toJSON()) : undefined;
    }

    public async getPlayersPromotions(playerCodes: string[],
                                      promotionId: number): Promise<PromotionToPlayerImpl[]> {
        const selectData = `SELECT ${PlayerPromotionDbImpl.BASE_SELECT_QUERY} ` +
            "WHERE p.player_code IN (:playerCodes) AND p.promotion_id = :promotionId AND p.status != 'revoked'";

        const results = await db.query(selectData, {
            replacements: { playerCodes, promotionId },
            model: PromotionToPlayerModel,
            type: QueryTypes.SELECT,
            mapToModel: true
        });

        return  results && results.length ?
                results.map(res => Object.assign(new PromotionToPlayerImpl(), res.toJSON())) : undefined;
    }

    public async updatePromotionPlayer(promoPlayer: PromotionToPlayer, update: PromotionToPlayer): Promise<void> {
        // TODO: need to fix 'any'
        await PromotionToPlayerModel.update(update, {
            where: {
                promotionId: promoPlayer.promotionId,
                playerCode: promoPlayer.playerCode,
                activeTransactionId: promoPlayer.activeTransactionId
            },
            fields: Object.keys(update) as any
        });
    }

    public async updatePromotionPlayers(promoPlayers: PromotionToPlayer[],
                                        promo: PromotionImpl,
                                        updateData: Map<string, PromotionToPlayer>,
                                        batchUpdateData: PromotionToPlayer,
                                        mainTransaction?: Transaction): Promise<void> {
        await db.transaction(async (transaction) => {
            if (mainTransaction) {
                transaction = mainTransaction;
            }

            const batchCreate: PromotionToPlayer[] = [];
            const batchUpdate: PromotionToPlayer[] = [];

            for (const promoToUpdate of promoPlayers) {
                if (!promoToUpdate.id) {
                    const toCreate = {
                        ...promoToUpdate,
                        ...batchUpdateData,
                        ...updateData.get(promoToUpdate.playerCode),
                    };
                    batchCreate.push(toCreate);
                } else if (updateData.has(promoToUpdate.playerCode)) {
                    const toUpdate = {
                        ...batchUpdateData,
                        ...updateData.get(promoToUpdate.playerCode),
                    };

                    // TODO: need to fix 'any'
                    await PromotionToPlayerModel.update(toUpdate, {
                        where: {
                            promotionId: promoToUpdate.promotionId,
                            playerCode: promoToUpdate.playerCode,
                            activeTransactionId: null
                        },
                        transaction,
                        fields: Object.keys(toUpdate) as any
                    });
                } else {
                    batchUpdate.push(promoToUpdate);
                }
            }

            if (batchCreate.length) {
                await PromotionToPlayerModel.bulkCreate(batchCreate, { transaction });
            }

            if (batchUpdate.length && batchUpdateData) {
                // TODO: need to fix 'any'
                await PromotionToPlayerModel.update(batchUpdateData, {
                    where: {
                        promotionId: promo.getId(),
                        playerCode: { [Op.in]: batchUpdate.map((v) => v.playerCode )},
                        activeTransactionId: null,
                    },
                    transaction,
                    fields: Object.keys(batchUpdateData) as any
                });
            }

            await savePromo(promo, transaction);
        });
    }

    public async commitPromotionPlayersUpdate(transactionId: string,
                                              promoPlayers: PromotionToPlayer[],
                                              promo: PromotionImpl,
                                              mainTransaction?: Transaction): Promise<void> {
        await db.transaction(async (transaction) => {
            if (mainTransaction) {
                transaction = mainTransaction;
            }

            const batchUpdate: PromotionToPlayer[] = [];

            for (const playerPromo of promoPlayers) {

                if (playerPromo.activeOperation && playerPromo.activeOperation.update) {

                    const toUpdate: PromotionToPlayer = {
                        ...playerPromo.activeOperation.batchUpdate,
                        ...playerPromo.activeOperation.update,
                        activeOperation: null,
                        activeTransactionId: null
                    };

                    // TODO: need to fix 'any'
                    await PromotionToPlayerModel.update(toUpdate, {
                        where: {
                            promotionId: playerPromo.promotionId,
                            playerCode: playerPromo.playerCode,
                            activeTransactionId: transactionId,
                        },
                        transaction,
                        fields: Object.keys(toUpdate) as any
                    });
                } else {
                    batchUpdate.push(playerPromo);
                }
            }

            if (batchUpdate.length) {

                const toUpdate: PromotionToPlayer = {
                    ...(batchUpdate[0].activeOperation && batchUpdate[0].activeOperation.batchUpdate),
                    activeOperation: null,
                    activeTransactionId: null
                };

                // TODO: need to fix 'any'
                await PromotionToPlayerModel.update(toUpdate, {
                    where: {
                        promotionId: promo.getId(),
                        playerCode: { [Op.in]: batchUpdate.map((v) => v.playerCode )},
                        activeTransactionId: transactionId,
                    },
                    transaction,
                    fields: Object.keys(toUpdate) as any
                });
            }

            await savePromo(promo, transaction);
        });
    }

    public async rollbackPromotionPlayersUpdate(transactionId: string,
                                                promoPlayers: PromotionToPlayer[],
                                                promo: PromotionImpl): Promise<void> {
        await db.transaction(async (transaction) => {

            const batchDestroy: PromotionToPlayer[] = [];
            const batchUpdate: PromotionToPlayer[] = [];

            for (const playerPromo of promoPlayers) {
                if (playerPromo.activeOperation && playerPromo.activeOperation.isNew) {
                    batchDestroy.push(playerPromo);
                } else {
                    batchUpdate.push(playerPromo);
                }
            }

            if (batchDestroy.length) {
                await PromotionToPlayerModel.destroy({
                    where: {
                        promotionId: promo.getId(),
                        playerCode: { [Op.in]: batchDestroy.map((v) => v.playerCode) }
                    },
                    transaction
                });
            }

            if (batchUpdate.length) {
                const toUpdate: PromotionToPlayer = {
                    activeOperation: null,
                    activeTransactionId: null
                };
                // TODO: need to fix 'any'
                await PromotionToPlayerModel.update(toUpdate, {
                    where: {
                        promotionId: promo.getId(),
                        playerCode: { [Op.in]: batchUpdate.map((v) => v.playerCode) },
                        activeTransactionId: transactionId
                    },
                    transaction,
                    fields: Object.keys(toUpdate) as any
                });
            }

            await savePromo(promo, transaction);
        });
    }

    public async removePlayerPromotion(playerCode: string, promotionId: number): Promise<void> {
        const result = await PromotionToPlayerModel.destroy({
            where: { promotionId, playerCode, status: PromotionToPlayerStatus.PENDING, activeTransactionId: null }
        });

        if (!result) {
            // TODO new error
            return Promise.reject(new Errors.ValidationError("Failed to remove player promotion"));
        }
    }

    public async removePromotionPlayers(playerCodes: string[], promotionId: number): Promise<void> {
        const result = await PromotionToPlayerModel.destroy({
            where: {
                promotionId,
                playerCode: { [Op.in]: playerCodes },
                status: PromotionToPlayerStatus.PENDING,
                activeTransactionId: null
            }
        });

        if (!result) {
            // TODO new error
            return Promise.reject(new Errors.ValidationError("Failed to remove players from promotion"));
        }
    }

    public async getPlayerPromotions(brandId: number,
                                     playerCode: string,
                                     promoType?: string): Promise<(PromotionToPlayer&{promoType: string})[]> {
        const selectData = `SELECT ps.type as "promoType", ${PlayerPromotionDbImpl.BASE_SELECT_QUERY} ` +
            "JOIN promotions ps on p.promotion_id = ps.id AND ps.brand_id = :brandId " +
            (promoType ? "AND ps.type = :promoType " : "") +
            `WHERE p.player_code = :playerCode
             ORDER BY p.started_at ASC;`;

        const result = await db.query(selectData, {
            replacements: { brandId, playerCode, promoType },
            model: PromotionToPlayerModel,
            type: QueryTypes.SELECT,
            mapToModel: true
        });

        const mapper = (item): PromotionToPlayer & { promoType: string } => item.toJSON();
        return result.map(mapper);
    }

    public async getActivePlayersPromotions(brandId: number,
                                            playerCodes: string[],
                                            promoType: string,
                                            transaction?: Transaction): Promise<PromotionToPlayer[]> {
        if (!playerCodes.length) {
            return [];
        }
        const selectData = `SELECT ps.type as "promoType", ${PlayerPromotionDbImpl.BASE_SELECT_QUERY} ` +
            "JOIN promotions ps on p.promotion_id = ps.id AND ps.brand_id = :brandId AND ps.type = :promoType " +
            "WHERE p.player_code in (:playerCodes) AND p.status in ('confirmed', 'started')";

        const result = await db.query(selectData, {
            replacements: { brandId, playerCodes, promoType },
            model: PromotionToPlayerModel,
            type: QueryTypes.SELECT,
            mapToModel: true,
            transaction
        });

        return result.map((item) => item.toJSON());
    }
}

export const PlayerPromotionDb = new PlayerPromotionDbImpl();
