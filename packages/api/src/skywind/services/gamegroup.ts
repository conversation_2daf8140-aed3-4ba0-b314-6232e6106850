import { GameGroupDBInstance } from "../models/gamegroup";
import {
    GameGroupLimitDBInstance,
    GameGroupLimitsWithGame,
    isForbiddenAlignLimits
} from "../models/gamegrouplimit";
import * as Errors from "../errors";
import {
    CurrencyCode,
    GameGroupAndGame,
    GameGroupData,
    GameGroupFilter,
    GameGroupFilterOptions,
    GameGroupFindOptions,
    GameGroupInfo,
    Limits,
    LimitsByCurrencyCode,
    LimitsCustomizations,
    LimitType,
    SlotGameLimits,
    UpdateGameGroupData
} from "../entities/gamegroup";
import { BaseEntity, Entity, ENTITY_TYPE } from "../entities/entity";
import { col, FindOptions, fn, ForeignKeyConstraintError, literal, Op, UniqueConstraintError, WhereOptions } from "sequelize";
import { EntityGame, Game, GameCodeInfo, GameSettings } from "../entities/game";
import { EntityGameImpl, findOneEntityGame, GameImpl, parseGameFeaturesFilter } from "./game";
import { isLimits, IsLimitsResponse, sanitizeCurrencyLimits, validateLimits } from "./limits";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import logger from "../utils/logger";
import { lazy } from "@skywind-group/sw-utils";
import { getChildEntities, getChildIds, getParentIds, validateSuspended } from "./entity";
import { CacheWithNulls } from "../cache/cacheWithNulls";
import * as FilterService from "./filter";
import { PagingHelper } from "../utils/paginghelper";
import EntitySettingsService, { getChildEntitiesSettings, getEntitiesSettings, getEntitySettings } from "./settings";
import EntityCache from "../cache/entity";
import { EntitiesSettings, EntitySettings } from "../entities/settings";
import { getEntityJurisdictionService } from "./entityJurisdiction";
import { getCurrencyExchange } from "./currencyExchange";
import { Jurisdiction, JurisdictionSettings } from "../entities/jurisdiction";
import { BaseLimitsExchanger, getLimitsExchanger } from "./gameLimits/limitsExchanger";
import { StakeRangeDBInstance } from "../models/stakeRange";
import { GAME_TYPES } from "../utils/common";
import { getGameLimitsCurrency } from "../cache/gameLimitsCurrencies";
import { Models } from "../models/models";
import { isEqual } from "lodash";

const log = logger();

const GameGroupModel = Models.GameGroupModel;
const GameGroupLimitModel = Models.GameGroupLimitModel;
const GameGroupFilterModel = Models.GameGroupFilterModel;

const GameModel = Models.GameModel;
const GameProviderModel = Models.GameProviderModel;
const EntityGameModel = Models.EntityGameModel;
const StakeRangeModel = Models.StakeRangeModel;

const EUR_CURRENCY_CODE = "EUR";

interface GetFilterOptions {
    currency: string;
    gameCode: string;
    decreaseMaxBetSupported: boolean;
    increaseMinBetSupported: boolean;
}

interface JurisdictionOptions {
    totalBetMultiplier: number;
    limitFeaturesToMaxTotalStake: boolean;
    dynamicMaxTotalBetLimit?: number;
}

class GameGroupInfoImpl implements GameGroupInfo {

    public static keysMapper: Map<string, string> = new Map([["gameGroupId", "id"], ["gameGroup", "name"]]);

    public id?: number;
    public name: string;
    public description: string;
    public isDefault: boolean;
    public isOwner?: boolean;
    public filters?: GameGroupFilter[];

    constructor(item: GameGroupDBInstance, defaultGameGroupName?: string, keyEntity?: BaseEntity) {
        this.id = item.get("id");
        this.name = item.get("name");
        this.description = item.get("description");
        this.isDefault = defaultGameGroupName
                         ? this.name === defaultGameGroupName
                         : item.get("isDefault");
        if (keyEntity) {
            this.isOwner = keyEntity.id === item.get("brandId");
        }

        const filters = item.get("filters");
        if (filters && filters.length) {
            this.filters = filters.map(itm => itm.toInfo());
        }
    }

    public getFilter({
                         gameCode, currency,
                         decreaseMaxBetSupported, increaseMinBetSupported
                     }: GetFilterOptions): GameGroupFilter {
        if (!this.filters) {
            return;
        }

        const appropriatedFilters = [];

        for (const filter of this.filters) {
            if (filter.games.length && !filter.games.includes(gameCode)) {
                continue;
            }

            if (filter.currencies.length && !filter.currencies.includes(currency)) {
                continue;
            }

            if (!decreaseMaxBetSupported && filter.maxBetWillDecreased) {
                continue;
            }

            if (!increaseMinBetSupported && filter.minBetWillIncreased) {
                continue;
            }

            appropriatedFilters.push(filter);
        }

        if (!appropriatedFilters.length) {
            return;
        }

        const [lastUpdatedFilter] = appropriatedFilters.sort(
            (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());

        return lastUpdatedFilter;

    }
}

const containerGameGroup = lazy<GameGroupService>(() => new GameGroupServiceImpl());

export function getGameGroupService(): GameGroupService {
    return containerGameGroup.get();
}

export function compareLimits(limits: Limits,
                              otherLimits: Limits,
                              customizations: LimitsCustomizations,
                              type: LimitType) {
    if (!isEqual(limits, otherLimits)) {
        customizations.push(type);
    }
}

export interface GameGroupService {
    findGroupAndGame(entity: BaseEntity,
                     gameGroupName: string,
                     gameCode: string,
                     disableInheritance?: boolean): Promise<GameGroupAndGame>;

    findAll(entity: BaseEntity,
            entitySettings?: EntitySettings,
            requestQuery?: WhereOptions<any>): Promise<GameGroupInfo[]>;

    create(entity: BaseEntity, data: GameGroupData): Promise<GameGroupInfo>;

    findOne(entity: BaseEntity,
            findOptions: GameGroupFindOptions,
            raiseErrorIfNotFound?: boolean,
            inherited?: boolean): Promise<GameGroupDBInstance>;

    findOneDefault(entity: BaseEntity,
                   settings: EntitySettings): Promise<GameGroupDBInstance>;

    findOneForPlayer(entity: BaseEntity,
                     gameGroupName: string,
                     settings: EntitySettings): Promise<GameGroupDBInstance>;

    update(entity: BaseEntity, name: string, dataToUpdate: UpdateGameGroupData): Promise<GameGroupInfo>;

    rename(entity: BaseEntity, name: string, newName: string, force?: boolean): Promise<GameGroupInfo>;

    setDefault(entity: BaseEntity, name: string): Promise<GameGroupInfo>;

    delete(entity: BaseEntity, name: string, force?: boolean): Promise<void>;
}

const containerGameGroupGame = lazy<GameGroupGameService>(() => new GameGroupGameServiceImpl());

export function getGameGroupGameService(): GameGroupGameService {
    return containerGameGroupGame.get();
}

export interface GameGroupGameService {
    findAll(entity: BaseEntity,
            gameGroupName: string): Promise<GameCodeInfo[]>;

    add(entity: BaseEntity,
        gameGroupName: string,
        gameCode: string,
        limits: LimitsByCurrencyCode,
        overrideDefault?: boolean): Promise<void>;

    delete(entity: BaseEntity, gameGroupName: string, gameCode: string): Promise<void>;
}

const containerGameGroupLimit = lazy<GameGroupLimitService>(() => new GameGroupLimitServiceImpl());

export function getGameGroupLimitService(): GameGroupLimitService {
    return containerGameGroupLimit.get();
}

export interface GameGroupLimitService {

    findOneDBItem(entity: BaseEntity,
                  gameGroupName: string,
                  gameCode: string): Promise<GameGroupLimitDBInstance>;

    findByGameCode(entity: BaseEntity, gameCode: string): Promise<LimitsByCurrencyCode>;

    findOne(entity: BaseEntity,
            gameGroupName: string,
            gameCode: string,
            raiseErrorIfNotFound?: boolean): Promise<LimitsByCurrencyCode>;

    findAll(entity: BaseEntity, query: WhereOptions<any>): Promise<any>;

    findByCurrency(entity: BaseEntity,
                   gameGroupName: string,
                   gameCode: string,
                   currencyCode: CurrencyCode,
                   raiseErrorIfNotFound?: boolean): Promise<Limits>;

    findForPlayer(entity: BaseEntity,
                  gameGroupName: string,
                  entityGame: EntityGame,
                  currencyCode: CurrencyCode,
                  settings: EntitySettings,
                  skipJurisdictionFiltering?: boolean,
                  dynamicMaxTotalBetLimit?: number): Promise<[Limits, LimitsCustomizations]>;

    update(entity: BaseEntity,
           gameGroupName: string,
           gameCode: string,
           limits: LimitsByCurrencyCode,
           overrideDefault?: boolean): Promise<void>;

    updateByCurrency(entity: BaseEntity,
                     gameGroupName: string,
                     gameCode: string,
                     currencyCode: CurrencyCode,
                     limits: Limits): Promise<void>;

    removeByCurrency(entity: BaseEntity,
                     gameGroupName: string,
                     gameCode: string,
                     currencyCode: string): Promise<void>;
}

class GameGroupServiceImpl implements GameGroupService {
    public async findGroupAndGame(entity: BaseEntity,
                                  gameGroupName: string,
                                  gameCode: string,
                                  disableInheritance: boolean = true): Promise<GameGroupAndGame> {

        const settings: EntitySettings = await getEntitySettings(entity.path);
        const brandId = settings.gameGroupsInheritance && !disableInheritance
                        ? { [Op.in]: [entity.id, ...getParentIds(entity)] }
                        : entity.id;

        const gameGroup = await GameGroupModel.findOne({
            where: {
                brandId,
                name: gameGroupName
            }
        });

        if (!gameGroup) {
            return Promise.reject(new Errors.GameGroupNotFound());
        }

        const entityGame = await findOneEntityGame(entity, gameCode);

        return {
            gameGroup: gameGroup.toJSON(),
            entityGame
        };
    }

    public async findAll(entity: BaseEntity,
                         settings?: EntitySettings,
                         requestQuery: WhereOptions<any> = {}): Promise<GameGroupInfo[]> {
        validateSuspended(entity);
        const findOptions: FindOptions<any> = {
            include: [
                {
                    model: GameGroupFilterModel,
                    as: "filters"
                }
            ],
            offset: FilterService.valueFromQuery(requestQuery, "offset"),
            limit: FilterService.valueFromQuery(requestQuery, "limit"),
        };

        if (settings && settings.gameGroupsInheritance) {
            const entityIds = [entity.id, ...getParentIds(entity)];
            findOptions.where = {
                brandId: { [Op.in]: [entity.id, ...getParentIds(entity)] }
            };
            findOptions.order = fn("array_position", entityIds, col("brand_id"));
            findOptions.attributes = { include: ["brand_id"] };
        } else {
            findOptions.where = {
                brandId: entity.id
            };
        }

        const { rows, count } = await GameGroupModel.findAndCountAll(findOptions);
        const mappedRows = rows.map(item => new GameGroupInfoImpl(item, settings && settings.defaultGameGroup, entity));
        PagingHelper.fillInfo(mappedRows, count, findOptions);
        return mappedRows;
    }

    public async create(entity: BaseEntity,
                        data: GameGroupData): Promise<GameGroupInfo> {
        try {
            validateSuspended(entity);

            const entitiesSettings = await getEntitiesSettings(entity.path);
            const entitySettings = entitiesSettings[entity.path];

            if (entitySettings.gameGroupsInheritance) {
                await this.validateUniqueNameInSubtree(entitiesSettings, data.name);
            } else {
                GameGroupServiceImpl.validateEntityType(entity);
            }

            const item: GameGroupDBInstance = await GameGroupModel.create({
                brandId: entity.id,
                name: data.name,
                description: data.description
            });

            return new GameGroupInfoImpl(item, entitySettings.defaultGameGroup);
        } catch (err) {
            if (err instanceof UniqueConstraintError) {
                return Promise.reject(new Errors.GameGroupAlreadyExists(data.name));
            }

            return Promise.reject(err);
        }
    }

    public async findOne(entity: BaseEntity,
                         whereOptions: GameGroupFindOptions,
                         raiseErrorIfNotFound: boolean = true,
                         inherited: boolean = false): Promise<GameGroupDBInstance> {

        if (raiseErrorIfNotFound) {
            validateSuspended(entity);
        }

        const findOptions: FindOptions<any> = {
            include: [
                {
                    model: GameGroupFilterModel,
                    as: "filters"
                }
            ]
        };
        if (inherited) {
            const entityIds = [entity.id, ...getParentIds(entity)];
            findOptions.where = { brandId: { [Op.in]: entityIds }, ...whereOptions };
            findOptions.order = fn("array_position", entityIds, col("brand_id"));
            findOptions.attributes = { include: ["brand_id"] };
            findOptions.limit = 1;
        } else {
            findOptions.where = {
                brandId: { [Op.eq]: entity.id },
                ...whereOptions
            };
        }
        const item: GameGroupDBInstance = await GameGroupModel.findOne(findOptions);

        if (!item && raiseErrorIfNotFound) {
            return Promise.reject(new Errors.GameGroupNotFound());
        }

        return item;
    }

    public async findOneDefault(entity: BaseEntity,
                                settings: EntitySettings): Promise<GameGroupDBInstance> {

        const defaultGameGroup = settings && settings.defaultGameGroup;
        const inherited = settings && settings.gameGroupsInheritance;

        let gameGroupDBItem;
        if (defaultGameGroup) {
            gameGroupDBItem = await this.findOne(entity, {
                name: defaultGameGroup
            }, false, inherited);
        }

        if (!gameGroupDBItem) {
            gameGroupDBItem = await this.findOne(entity, {
                isDefault: true
            }, false, inherited);
        }

        return gameGroupDBItem;
    }

    public async findOneForPlayer(entity: BaseEntity,
                                  gameGroupName: string,
                                  settings: EntitySettings): Promise<GameGroupDBInstance> {
        let gameGroupDBItem;
        if (gameGroupName) {
            gameGroupDBItem = await this.findOne(entity, {
                name: gameGroupName
            }, false, settings && settings.gameGroupsInheritance);
        }

        if (!gameGroupDBItem) {
            gameGroupDBItem = await this.findOneDefault(entity, settings);
        }

        return gameGroupDBItem;
    }

    public async update(entity: BaseEntity,
                        name: string,
                        dataToUpdate: UpdateGameGroupData): Promise<GameGroupInfo> {
        const item: GameGroupDBInstance = await this.findOne(entity, { name });
        await item.update({
            description: dataToUpdate.description || item.get("description")
        });

        const settings = await getEntitySettings(entity.path);
        return new GameGroupInfoImpl(item, settings.defaultGameGroup);
    }

    public async delete(entity: BaseEntity,
                        name: string,
                        force: boolean = false): Promise<void> {

        const item: GameGroupDBInstance = await this.findOne(entity, { name });
        const childEntities = await getChildEntities(entity);
        childEntities.push(entity);
        const entitiesSettings: EntitiesSettings = await getChildEntitiesSettings([entity, ...childEntities]);

        try {
            await this.updateDefaultGameGroup(childEntities, entitiesSettings, name, undefined, force);

            await item.destroy();
        } catch (error) {
            if (error instanceof ForeignKeyConstraintError) {
                throw new Errors.GameGroupNotEmpty();
            }
            throw error;
        } finally {
            GameGroupLimitServiceImpl.cache.reset();
        }
    }

    private async validateUniqueNameInSubtree(entitiesSettings: EntitiesSettings, name: string) {
        const entityPathWithUniqueSetting: string = Object.keys(entitiesSettings)
            .find(entityName => entitiesSettings?.[entityName]?.gameGroupsInheritance);

        const entityWithInheritanceSetting = await EntityCache.findOne<Entity>({
            path: entityPathWithUniqueSetting
        });

        if (!entityWithInheritanceSetting) {
            return;
        }

        const childIds = getChildIds(entityWithInheritanceSetting);

        const duplicatedGameGroup: GameGroupDBInstance = await GameGroupModel.findOne({
            where: { brandId: { [Op.in]: [entityWithInheritanceSetting.id, ...childIds] }, name }
        });

        if (duplicatedGameGroup) {
            throw new Errors.GameGroupAlreadyExists(name);
        }
    }

    private static validateEntityType(entity: BaseEntity) {
        if (entity.type !== ENTITY_TYPE.BRAND && entity.type !== ENTITY_TYPE.MERCHANT) {
            throw new Errors.NotBrandOrMerchant();
        }
    }

    public async rename(entity: BaseEntity, name: string, newName: string, force?: boolean): Promise<GameGroupInfo> {
        try {
            validateSuspended(entity);
            const item: GameGroupDBInstance = await this.findOne(entity, { name });

            const childEntities = await getChildEntities(entity);
            childEntities.push(entity);
            const entitiesSettings: EntitiesSettings = await getChildEntitiesSettings([entity, ...childEntities]);
            const entitySettings = entitiesSettings?.[entity.path];

            if (entitySettings?.gameGroupsInheritance) {
                await this.validateUniqueNameInSubtree(entitiesSettings, newName);
            }

            await this.updateDefaultGameGroup(childEntities, entitiesSettings, name, newName, force);
            await item.update({ name: newName });

            return new GameGroupInfoImpl(item, entitySettings?.defaultGameGroup);

        } catch (err) {
            if (err instanceof UniqueConstraintError) {
                return Promise.reject(new Errors.GameGroupAlreadyExists(newName));
            }

            return Promise.reject(err);
        }
    }

    private async updateDefaultGameGroup(childEntities: BaseEntity[],
                                         entitiesSettings: EntitiesSettings,
                                         name: string,
                                         newName?: string,
                                         force: boolean = false) {

        for (const path in entitiesSettings) {
            if (entitiesSettings.hasOwnProperty(path)) {
                const defaultGameGroup = entitiesSettings?.[path]?.defaultGameGroup;
                if (!defaultGameGroup) {
                    continue;
                }

                if (name === defaultGameGroup) {
                    if (!force) {
                        throw new Errors.GameGroupIsDefault();
                    } else {
                        const childEntity = childEntities.find(child => child.path === path);
                        if (childEntity) {
                            entitiesSettings[path].defaultGameGroup = newName ? newName : undefined;
                            await (new EntitySettingsService(childEntity)).update(entitiesSettings[path]);
                        }
                    }

                }
            }

        }
    }

    public async setDefault(entity: BaseEntity, name: string): Promise<GameGroupInfo> {

        validateSuspended(entity);
        const entitySettings: EntitySettings = await getEntitySettings(entity.path);
        let item: GameGroupDBInstance;
        const resetGameGroup = name === null || name === "null";
        if (!resetGameGroup) {
            item = await this.findOne(entity,
                { name },
                true,
                entitySettings?.gameGroupsInheritance as boolean);
        }

        try {
            const defaultGameGroup = resetGameGroup ? null : name;
            const updatedSettings = await (new EntitySettingsService(entity)).patch({ defaultGameGroup });

            // TODO SWS-18578 remove this after column isDefault will be dropped
            await GameGroupModel.findOne({
                where: {
                    brandId: entity.id,
                    isDefault: false
                }
            });
            if (item) {
                return new GameGroupInfoImpl(item, updatedSettings?.defaultGameGroup);
            }
        } finally {
            GameGroupLimitServiceImpl.cache.reset();
        }
    }
}

class GameGroupLimitServiceImpl implements GameGroupLimitService {
    private static gameGroupService: GameGroupService = getGameGroupService();
    private gameKeysMapper: Map<string, string> = new Map([["gameCode", "code"], ["gameType", "type"]]);
    private findAllDefaultSortKey = "gamegroup.name";
    private defaultSortOrder = "ASC";

    private exchanger: BaseLimitsExchanger = getLimitsExchanger();

    public static cache: CacheWithNulls<string, GameGroupLimitDBInstance> = new CacheWithNulls("game-group-limit",
        GameGroupLimitServiceImpl.findGameGroupLimitCache);

    private static async findGameGroupLimitCache(_: string,
                                                 gameId: number,
                                                 gameGroupId: number): Promise<GameGroupLimitDBInstance> {
        return GameGroupLimitModel.findOne({
            where: {
                gamegroupId: gameGroupId,
                gameId: gameId
            },
            include: [GameModel]
        });
    }

    public async findOneDBItem(entity: BaseEntity,
                               gameGroupName: string,
                               gameCode: string): Promise<GameGroupLimitDBInstance> {

        validateSuspended(entity);
        const result = await GameGroupLimitServiceImpl.gameGroupService
            .findGroupAndGame(entity, gameGroupName, gameCode, false);

        return GameGroupLimitModel.findOne({
            where: {
                gamegroupId: { [Op.eq]: result.gameGroup.id },
                gameId: { [Op.eq]: result.entityGame.gameId }
            }
        });
    }

    public async findByGameCode(entity: BaseEntity, gameCode: string): Promise<LimitsByCurrencyCode> {
        const game = await EntityGameModel.findOne({
            include: [
                {
                    model: GameModel,
                    where: { code: { [Op.eq]: gameCode } }
                }
            ],
            where: { entityId: { [Op.eq]: entity.id } }
        });

        if (!game || !game.get("game")) {
            return Promise.reject(new Errors.GameNotFoundError(gameCode));
        }
        return game.get("game").limits;
    }

    public async findOne(entity: BaseEntity,
                         gameGroupName: string,
                         gameCode: string,
                         raiseErrorIfNotFound: boolean = true): Promise<LimitsByCurrencyCode> {

        if (raiseErrorIfNotFound) {
            validateSuspended(entity);
        }

        try {
            const limits = await this.findOneDBItem(entity,
                gameGroupName,
                gameCode);

            if (!limits && raiseErrorIfNotFound) {
                return Promise.reject(new Errors.GameIsNotInGameGroup());
            }
            if (!limits) {
                return this.findByGameCode(entity, gameCode);
            }

            return limits.limits;
        } catch (error) {
            if (error instanceof Errors.GameGroupNotFound && !raiseErrorIfNotFound) {
                log.warn(`Game group ${gameGroupName} not found`);
                return this.findByGameCode(entity, gameCode);
            }
            throw error;
        }
    }

    public async findAll(entity: BaseEntity,
                         requestQuery: WhereOptions<any> = {}): Promise<GameGroupLimitsWithGame[]> {

        const queryGameProvider: WhereOptions<any> = { status: "normal" };

        const queryGame: WhereOptions<any> = this.getQuery(this.gameKeysMapper, requestQuery);
        queryGame["status"] = "available";
        Object.assign(queryGame, parseGameFeaturesFilter(requestQuery));

        const queryGameGroup: WhereOptions<any> = this.getQuery(GameGroupInfoImpl.keysMapper, requestQuery);
        queryGameGroup["brandId"] = entity.id;

        const settings = await getEntitySettings(entity.path);
        return PagingHelper.findAndCountAll(GameGroupLimitModel, {
            include: [
                {
                    model: GameGroupModel,
                    where: queryGameGroup
                },
                {
                    model: EntityGameModel,
                    where: { entityId: { [Op.eq]: entity.id } },
                    attributes: { exclude: ["limitFilters", "urlParams"] },
                    include: [
                        {
                            model: GameModel,
                            where: queryGame,
                            include: [
                                {
                                    model: GameProviderModel,
                                    where: queryGameProvider,
                                    as: "gameProvider",
                                    attributes: ["status", "code", "title"]
                                }
                            ],
                            attributes: { exclude: ["limits"] }
                        }
                    ]
                }
            ],
            offset: FilterService.valueFromQuery(requestQuery, "offset") || 0,
            limit: FilterService.valueFromQuery(requestQuery, "limit") || 20,
            order: literal(`"${this.findAllDefaultSortKey}" ${this.defaultSortOrder}`),
        }, (item) => {
            const gameInfo = new EntityGameImpl(item.entityGame).toInfo();
            const gamegroup = new GameGroupInfoImpl(item.gamegroup, settings.defaultGameGroup, entity);

            return {
                overrideDefault: item.overrideDefault,
                limits: item.limits,
                game: gameInfo,
                gamegroup: gamegroup
            };
        }, true);
    }

    public async findByCurrency(entity: BaseEntity,
                                gameGroupName: string,
                                gameCode: string,
                                currencyCode: CurrencyCode,
                                raiseErrorIfNotFound: boolean = true): Promise<Limits> {

        if (!Currencies.exists(currencyCode)) {
            return Promise.reject(new Errors.CurrencyNotFoundError(currencyCode));
        }

        if (raiseErrorIfNotFound) {
            validateSuspended(entity);
        }

        const result = await this.findOne(entity, gameGroupName, gameCode, raiseErrorIfNotFound);
        return !result ? null : result[currencyCode];
    }

    public async findForPlayer(entity: BaseEntity,
                               gameGroupName: string,
                               entityGame: EntityGame,
                               currencyCode: CurrencyCode,
                               settings: EntitySettings,
                               skipJurisdictionFiltering?: boolean,
                               dynamicMaxTotalBetLimit?: number): Promise<[Limits, LimitsCustomizations]> {

        if (!Currencies.exists(currencyCode)) {
            return Promise.reject(new Errors.CurrencyNotFoundError(currencyCode));
        }

        const [limits, limitCustomizations] = await this.getLimits(entity,
            gameGroupName,
            entityGame,
            currencyCode,
            settings);

        const game = entityGame.game;
        const jurisdiction = await getEntityJurisdictionService().findOneByEntityId(entity.id);
        const jrsdDynamicMaxTotalBetLimit = jurisdiction?.settings?.dynamicMaxTotalBetLimit;
        const dynamicMaxTotalStakeCondition = jurisdiction?.settings?.dynamicMaxTotalBetLimitEnabled &&
            (dynamicMaxTotalBetLimit ||
                (jrsdDynamicMaxTotalBetLimit?.defaultTotalBet && jrsdDynamicMaxTotalBetLimit?.defaultMaxTotalBet)
            );
        if (!skipJurisdictionFiltering && jurisdiction &&
            (jurisdiction.settings.maxTotalStake || dynamicMaxTotalStakeCondition) &&
            game.totalBetMultiplier &&
            !isForbiddenAlignLimits(game.type, currencyCode)) {

            const options: JurisdictionOptions = {
                totalBetMultiplier: game.totalBetMultiplier,
                limitFeaturesToMaxTotalStake: this.getLimitFeaturesToMaxTotalStake(settings, entityGame.settings),
                dynamicMaxTotalBetLimit
            };
            const jurisdictionalLimits = await this.applyJurisdictionFilter(jurisdiction,
                limits,
                currencyCode,
                options);
            compareLimits(limits, jurisdictionalLimits, limitCustomizations, LimitType.JURISDICTION_FILTERS);
            return [jurisdictionalLimits, limitCustomizations];
        }

        return [limits, limitCustomizations];
    }

    private async getLimits(entity: BaseEntity,
                            gameGroupName: string,
                            entityGame: EntityGame,
                            currencyCode: CurrencyCode,
                            settings: EntitySettings): Promise<[Limits, LimitsCustomizations]> {
        const limitsCustomizations = [];
        const game = entityGame.game;

        const { copyLimitsFrom, toEURMultiplier: customToEURMultiplier } = await getGameLimitsCurrency(
            currencyCode,
            settings,
            game?.features
        );

        const defaultLimits = await game.getLimits(currencyCode, copyLimitsFrom) || {};
        const eurGameLimits = await game.getLimits(EUR_CURRENCY_CODE, copyLimitsFrom);

        const totalBetMultiplier = game.totalBetMultiplier;
        const isSmartRounding = game.features?.isSmartRoundingSupported ?? true;

        if (game.type === GAME_TYPES.slot || game.type === GAME_TYPES.external) {
            await this.calculateDefaultGameLimits(defaultLimits as SlotGameLimits, eurGameLimits as SlotGameLimits,
                currencyCode, totalBetMultiplier, isSmartRounding, customToEURMultiplier);
        }

        try {
            const gameGroupDBItem: GameGroupDBInstance = await GameGroupLimitServiceImpl.gameGroupService
                .findOneForPlayer(entity, gameGroupName, settings);

            if (!gameGroupDBItem) {
                await this.recalculateStakeAll(defaultLimits as SlotGameLimits, game, totalBetMultiplier);

                return [defaultLimits, limitsCustomizations];
            }

            const gameGroup = new GameGroupInfoImpl(gameGroupDBItem);

            const key = GameGroupLimitServiceImpl.createCacheKey(entity.path, gameGroupName, game.code);
            const groupLimits = await GameGroupLimitServiceImpl.cache.find(key, game.id, gameGroup.id);

            if (groupLimits && (game.type === GAME_TYPES.slot || game.type === GAME_TYPES.external)) {
                const currencyGroupLimits = await groupLimits.getLimits(currencyCode) || {};
                const eurGroupLimits = await groupLimits.getLimits(EUR_CURRENCY_CODE) as SlotGameLimits;

                await this.calculateCustomGameLimits(defaultLimits as SlotGameLimits,
                    currencyGroupLimits as SlotGameLimits,
                    eurGroupLimits as SlotGameLimits,
                    currencyCode,
                    totalBetMultiplier,
                    isSmartRounding,
                    customToEURMultiplier);
            }

            const limits = await this.applyOverrideDefaultIfNeeded(groupLimits, defaultLimits, currencyCode);

            compareLimits(defaultLimits, limits, limitsCustomizations, LimitType.GAME_GROUP);

            await this.recalculateStakeAll(limits as SlotGameLimits, game, totalBetMultiplier);

            const filterOptions: GetFilterOptions = {
                gameCode: game.code,
                currency: currencyCode,
                decreaseMaxBetSupported: entityGame.decreaseMaxBetSupported,
                increaseMinBetSupported: entityGame.increaseMinBetSupported
            };

            const filter = gameGroup.getFilter(filterOptions);

            if (entityGame.limitFiltersWillBeApplied && filter) {
                const gameGroupFilterOptions: GameGroupFilterOptions = {
                    totalBetMultiplier: game.totalBetMultiplier,
                    highestWin: game.features.highestWin,
                    limitFeaturesToMaxTotalStake: this.getLimitFeaturesToMaxTotalStake(settings, entityGame.settings)
                };

                const filterLimits = this.applyGameGroupFilter(filter,
                    limits,
                    currencyCode,
                    gameGroupFilterOptions,
                    customToEURMultiplier);

                compareLimits(limits, filterLimits, limitsCustomizations, LimitType.GAME_GROUP_FILTERS);

                return [filterLimits, limitsCustomizations];
            }

            return [limits, limitsCustomizations];
        } catch (error) {
            if (error instanceof Errors.GameGroupNotFound) {
                log.warn(`Game group ${gameGroupName} not found`);

                return [defaultLimits, limitsCustomizations];
            }

            return Promise.reject(error);
        }
    }

    private async calculateDefaultGameLimits(defaultLimits: SlotGameLimits,
                                             eurGameLimits: SlotGameLimits,
                                             currencyCode: string,
                                             totalBetMultiplier: number,
                                             isSmartRounding: boolean,
                                             customToEURMultiplier: number) {
        const currency = Currencies.value(currencyCode);
        const alignCurrencies = eurGameLimits ? eurGameLimits.alignCurrencies : false;

        let lowerStakesShouldBeAdded = false;

        if (!defaultLimits.stakeAll) {
            if (this.shouldValueBeAlignedToEUR(eurGameLimits,
                currency,
                customToEURMultiplier,
                alignCurrencies) && eurGameLimits.stakeAll) {
                defaultLimits.stakeAll = [
                    ...this.exchanger.convertCoinBets(eurGameLimits.stakeAll,
                        currencyCode,
                        isSmartRounding,
                        customToEURMultiplier)
                ];
                lowerStakesShouldBeAdded = true;
            } else {
                const stakeRanges = await this.getDefaultCoinBetsFromConst(currencyCode);
                defaultLimits.stakeAll = stakeRanges && stakeRanges.coinBets ? stakeRanges.coinBets : [];
            }
        }

        if (!defaultLimits.stakeMin) {
            if (this.shouldValueBeAlignedToEUR(eurGameLimits,
                currency,
                customToEURMultiplier,
                alignCurrencies) && eurGameLimits.stakeMin) {
                defaultLimits.stakeMin = this.exchanger.exchange(eurGameLimits.stakeMin,
                    currencyCode,
                    isSmartRounding,
                    customToEURMultiplier);
            } else {
                defaultLimits.stakeMin = Math.min(...defaultLimits.stakeAll);
            }
        }

        if (!defaultLimits.stakeMax) {
            if (defaultLimits.maxTotalStake) {
                defaultLimits.stakeMax = defaultLimits.maxTotalStake / totalBetMultiplier;
            } else {
                if (this.shouldValueBeAlignedToEUR(eurGameLimits,
                        currency,
                        customToEURMultiplier,
                        alignCurrencies) &&
                    (eurGameLimits.stakeMax || eurGameLimits.maxTotalStake)) {
                    if (eurGameLimits.stakeMax) {
                        defaultLimits.stakeMax = this.exchanger.exchange(eurGameLimits.stakeMax,
                            currencyCode,
                            isSmartRounding,
                            customToEURMultiplier);

                        if (!eurGameLimits.maxTotalStake) {
                            defaultLimits.maxTotalStake = defaultLimits.stakeMax * totalBetMultiplier;
                        } else {
                            defaultLimits.maxTotalStake = this.exchanger.exchange(
                                eurGameLimits.maxTotalStake,
                                currencyCode,
                                false,
                                customToEURMultiplier
                            );
                        }
                    } else {
                        defaultLimits.maxTotalStake = this.exchanger.exchange(
                            eurGameLimits.maxTotalStake,
                            currencyCode,
                            false,
                            customToEURMultiplier
                        );
                        defaultLimits.stakeMax = defaultLimits.maxTotalStake / totalBetMultiplier;
                    }
                }
            }
        } else {
            if (!defaultLimits.maxTotalStake) {
                defaultLimits.maxTotalStake = defaultLimits.stakeMax * totalBetMultiplier;
            }
        }

        if (!defaultLimits.stakeDef) {
            if (defaultLimits.defaultTotalStake) {
                defaultLimits.stakeDef = defaultLimits.defaultTotalStake / totalBetMultiplier;
            } else {
                if (this.shouldValueBeAlignedToEUR(eurGameLimits,
                        currency,
                        customToEURMultiplier,
                        alignCurrencies) &&
                    (eurGameLimits.stakeDef || eurGameLimits.defaultTotalStake)) {
                    if (eurGameLimits.stakeDef) {
                        defaultLimits.stakeDef = this.exchanger.exchange(eurGameLimits.stakeDef,
                            currencyCode,
                            isSmartRounding,
                            customToEURMultiplier);
                    } else {
                        const defaultTotalStake = this.exchanger.exchange(eurGameLimits.defaultTotalStake,
                            currencyCode, isSmartRounding, customToEURMultiplier);
                        defaultLimits.stakeDef = defaultTotalStake / totalBetMultiplier;
                    }
                }
            }
        }

        if (!defaultLimits.winMax && eurGameLimits && eurGameLimits.winMax) {
            if (!isForbiddenAlignLimits(GAME_TYPES.slot, currencyCode)) {
                const service = await getCurrencyExchange();
                defaultLimits.winMax = service.exchange(eurGameLimits.winMax, "EUR", currencyCode);
            } else {
                const toEURMultiplier = customToEURMultiplier ?
                                        customToEURMultiplier :
                                        Currencies.value(currencyCode)?.toEURMultiplier;
                if (toEURMultiplier === 1) {
                    defaultLimits.winMax = eurGameLimits.winMax;
                }
            }
        }

        if (!defaultLimits.coins && eurGameLimits && eurGameLimits.coins) {
            defaultLimits.coins = eurGameLimits.coins;
        }

        if (!defaultLimits.defaultCoin && eurGameLimits && eurGameLimits.defaultCoin) {
            defaultLimits.defaultCoin = eurGameLimits.defaultCoin;
        }

        await this.appendLowerStakes(defaultLimits,
            eurGameLimits,
            currencyCode,
            customToEURMultiplier,
            lowerStakesShouldBeAdded);
    }

    private async calculateCustomGameLimits(defaultLimits: SlotGameLimits,
                                            currencyGroupLimits: SlotGameLimits,
                                            eurGroupLimits: SlotGameLimits,
                                            currencyCode: string,
                                            totalBetMultiplier: number,
                                            isSmartRounding: boolean,
                                            customToEURMultiplier: number) {
        const currency = Currencies.value(currencyCode);
        const alignCurrencies = eurGroupLimits ? eurGroupLimits.alignCurrencies : false;

        let lowerStakesShouldBeAdded = false;

        if (!currencyGroupLimits.stakeAll) {
            if (this.shouldValueBeAlignedToEUR(eurGroupLimits,
                currency,
                customToEURMultiplier,
                alignCurrencies) && eurGroupLimits.stakeAll) {
                defaultLimits.stakeAll = [
                    ...this.exchanger.convertCoinBets(eurGroupLimits.stakeAll,
                        currencyCode,
                        isSmartRounding,
                        customToEURMultiplier)
                ];
                lowerStakesShouldBeAdded = true;
            }
        }

        if (!currencyGroupLimits.stakeMin) {
            if (this.shouldValueBeAlignedToEUR(eurGroupLimits,
                currency,
                customToEURMultiplier,
                alignCurrencies) && eurGroupLimits.stakeMin) {
                defaultLimits.stakeMin = this.exchanger.exchange(eurGroupLimits.stakeMin,
                    currencyCode,
                    isSmartRounding,
                    customToEURMultiplier);
            } else {
                if (currencyGroupLimits && Array.isArray(currencyGroupLimits.stakeAll) &&
                    currencyGroupLimits.stakeAll.length) {
                    defaultLimits.stakeMin = Math.min(...currencyGroupLimits.stakeAll);
                }
            }
        }

        if (!currencyGroupLimits.stakeMax) {
            if (currencyGroupLimits.maxTotalStake) {
                defaultLimits.stakeMax = currencyGroupLimits.maxTotalStake / totalBetMultiplier;
            } else {
                if (this.shouldValueBeAlignedToEUR(eurGroupLimits,
                        currency,
                        customToEURMultiplier,
                        alignCurrencies) &&
                    (eurGroupLimits.stakeMax || eurGroupLimits.maxTotalStake)) {
                    if (eurGroupLimits.stakeMax) {
                        defaultLimits.stakeMax = this.exchanger.exchange(eurGroupLimits.stakeMax,
                            currencyCode,
                            isSmartRounding,
                            customToEURMultiplier);

                        if (!eurGroupLimits.maxTotalStake) {
                            defaultLimits.maxTotalStake = defaultLimits.stakeMax * totalBetMultiplier;
                        } else {
                            defaultLimits.maxTotalStake = this.exchanger.exchange(
                                eurGroupLimits.maxTotalStake,
                                currencyCode,
                                false,
                                customToEURMultiplier
                            );
                        }
                    } else {
                        defaultLimits.maxTotalStake = this.exchanger.exchange(
                            eurGroupLimits.maxTotalStake,
                            currencyCode,
                            false,
                            customToEURMultiplier
                        );
                        defaultLimits.stakeMax = defaultLimits.maxTotalStake / totalBetMultiplier;
                    }
                }
            }
        } else {
            if (!currencyGroupLimits.maxTotalStake) {
                defaultLimits.maxTotalStake = currencyGroupLimits.stakeMax * totalBetMultiplier;
            }
        }

        if (!currencyGroupLimits.stakeDef) {
            if (currencyGroupLimits.defaultTotalStake) {
                defaultLimits.stakeDef = currencyGroupLimits.defaultTotalStake / totalBetMultiplier;
            } else {
                if (this.shouldValueBeAlignedToEUR(eurGroupLimits,
                        currency,
                        customToEURMultiplier,
                        alignCurrencies) &&
                    (eurGroupLimits.stakeDef || eurGroupLimits.defaultTotalStake)) {
                    if (eurGroupLimits.stakeDef) {
                        defaultLimits.stakeDef = this.exchanger.exchange(eurGroupLimits.stakeDef,
                            currencyCode,
                            isSmartRounding,
                            customToEURMultiplier);
                    } else {
                        const defaultTotalStake = this.exchanger.exchange(eurGroupLimits.defaultTotalStake,
                            currencyCode, isSmartRounding, customToEURMultiplier);
                        defaultLimits.stakeDef = defaultTotalStake / totalBetMultiplier;
                    }
                }
            }
        }

        if (!currencyGroupLimits.winMax && eurGroupLimits && eurGroupLimits.winMax) {
            if (!isForbiddenAlignLimits(GAME_TYPES.slot, currencyCode)) {
                const service = await getCurrencyExchange();
                defaultLimits.winMax = service.exchange(eurGroupLimits.winMax, "EUR", currencyCode);
            } else {
                const toEURMultiplier = customToEURMultiplier ?
                                        customToEURMultiplier :
                                        Currencies.value(currencyCode)?.toEURMultiplier;
                if (toEURMultiplier === 1) {
                    defaultLimits.winMax = eurGroupLimits.winMax;
                }
            }
        }

        if (!currencyGroupLimits.coins && eurGroupLimits && eurGroupLimits.coins) {
            defaultLimits.coins = eurGroupLimits.coins;
        }

        if (!currencyGroupLimits.defaultCoin && eurGroupLimits && eurGroupLimits.defaultCoin) {
            defaultLimits.defaultCoin = eurGroupLimits.defaultCoin;
        }

        await this.appendLowerStakes(defaultLimits,
            eurGroupLimits,
            currencyCode,
            customToEURMultiplier,
            lowerStakesShouldBeAdded);
    }

    private async appendLowerStakes(limits: SlotGameLimits,
                                    eurGameLimits: SlotGameLimits,
                                    currencyCode: string,
                                    customToEURMultiplier: number,
                                    lowerStakesShouldBeAdded: boolean) {
        const currency = Currencies.value(currencyCode);
        const alignCurrencies = eurGameLimits ? eurGameLimits.alignCurrencies : false;

        if (lowerStakesShouldBeAdded && this.shouldValueBeAlignedToEUR(eurGameLimits,
                currency,
                customToEURMultiplier,
                alignCurrencies) && eurGameLimits &&
            eurGameLimits.addLowerStakesOfAlignedCurrencies !== false) {
            const stakeRanges = await StakeRangeModel.findOne({ where: { currency: currencyCode } });

            if (stakeRanges && Array.isArray(stakeRanges.lowerStakes) && stakeRanges.lowerStakes.length) {
                limits.stakeAll = [...new Set([...stakeRanges.lowerStakes, ...limits.stakeAll])]
                    .sort((a, b) => a - b);
                limits.stakeMin = Math.min(...limits.stakeAll);
            }
        }
    }

    private async recalculateStakeAll(limits: SlotGameLimits, game: Game, totalBetMultiplier: number,
    ) {
        if (game.type === GAME_TYPES.slot || game.type === GAME_TYPES.external) {
            if (limits && Array.isArray(limits.stakeAll) && limits.stakeAll.length) {
                if (!limits.stakeMax || !limits.stakeDef) {
                    throw new Errors.ValidationError(
                        "Either max coin bet/max total bet and default coin bet/default total bet are mandatory");
                }

                limits.stakeAll = limits.stakeAll.sort((a, b) => a - b);

                if (!limits.stakeAll.includes(limits.stakeMax)) {
                    let maxCoin = Math.min(...limits.stakeAll);

                    limits.stakeAll.forEach(coin => {
                        if (limits.stakeMax >= coin) {
                            maxCoin = coin;
                        } else {
                            return;
                        }
                    });

                    limits.stakeMax = maxCoin;

                    if (!limits.maxTotalStake) {
                        limits.maxTotalStake = limits.stakeMax * totalBetMultiplier;
                    }
                }

                limits.stakeAll = limits.stakeAll.filter(item => item >= limits.stakeMin);
                limits.stakeAll = limits.stakeAll.filter(item => item <= limits.stakeMax);

                if (!limits.stakeAll.includes(limits.stakeDef)) {
                    let stakeDef = Math.min(...limits.stakeAll);

                    limits.stakeAll.forEach(coin => {
                        if (limits.stakeDef >= coin) {
                            stakeDef = coin;
                        } else {
                            return;
                        }
                    });

                    limits.stakeDef = stakeDef;
                }
            } else {
                throw new Errors.ValidationError("Default stake ranges not found");
            }
        }
    }

    private shouldValueBeAlignedToEUR(eurGameLimits: SlotGameLimits,
                                      currency: any,
                                      customToEURMultiplier: number,
                                      alignCurrencies = true): boolean {
        return eurGameLimits && alignCurrencies &&
            (customToEURMultiplier || (currency && currency.toEURMultiplier));
    }

    private async getDefaultCoinBetsFromConst(currencyCode: string): Promise<StakeRangeDBInstance> {
        return await StakeRangeModel.findOne({ where: { currency: { [Op.eq]: currencyCode } } });
    }

    private getLimitFeaturesToMaxTotalStake(entitySettings: EntitySettings, gameSettings: GameSettings = {}): boolean {
        if (gameSettings && "limitFeaturesToMaxTotalStake" in gameSettings) {
            return gameSettings.limitFeaturesToMaxTotalStake;
        }
        return entitySettings?.limitFeaturesToMaxTotalStake;
    }

    public applyGameGroupFilter(limitFilter: GameGroupFilter,
                                limits: Limits,
                                currencyCode: string,
                                options: GameGroupFilterOptions,
                                customToEURMultiplier?: number): SlotGameLimits {
        const slotLimits = limits as SlotGameLimits;

        if (!slotLimits) {
            throw new Errors.LimitsForCurrencyNotFound(currencyCode);
        }

        let stakeAll = slotLimits.stakeAll;
        const ignoreInvalid = limitFilter.ignoreInvalid;

        const currencyMultiplier = customToEURMultiplier || Currencies.value(currencyCode)?.toEURMultiplier;
        const { highestWin, totalBetMultiplier, limitFeaturesToMaxTotalStake } = options;

        if (limitFilter.maxExposure && highestWin) {
            const calculatedMTBFromExposure = limitFilter.maxExposure / highestWin;
            if (limitFilter.maxTotalBet) {
                limitFilter.maxTotalBet = Math.min(limitFilter.maxTotalBet, calculatedMTBFromExposure);
            } else {
                limitFilter.maxTotalBet = calculatedMTBFromExposure;
            }

        }

        if (limitFilter.minTotalBet) {
            const minCoin = (limitFilter.minTotalBet / totalBetMultiplier) * currencyMultiplier;
            const filteredCoins = stakeAll.filter(stake => stake >= minCoin);
            if (!filteredCoins.length) {
                if (!ignoreInvalid) {
                    throw new Errors.ValidationError("Limits have invalid filter for min total bet");
                }
            } else {
                stakeAll = filteredCoins;
            }
        }

        if (limitFilter.maxTotalBet) {
            const maxCoin = (limitFilter.maxTotalBet / totalBetMultiplier) * currencyMultiplier;
            const filteredCoins = stakeAll.filter(stake => stake <= maxCoin);
            if (!filteredCoins.length) {
                if (!ignoreInvalid) {
                    throw new Errors.ValidationError("Limits have invalid filter for max total bet");
                }
            } else {
                stakeAll = filteredCoins;
            }
        }

        let stakeDef = stakeAll.includes(slotLimits.stakeDef) ? slotLimits.stakeDef : Math.min(...stakeAll);
        if (limitFilter.defTotalBet) {
            const defCoin = (limitFilter.defTotalBet / totalBetMultiplier) * currencyMultiplier;
            const filteredCoins = stakeAll.filter(stake => stake <= defCoin);
            stakeDef = filteredCoins.length ? Math.max(...filteredCoins) : Math.min(...stakeAll);
        }

        const stakeMax = Math.max(...stakeAll);
        const stakeMin = Math.min(...stakeAll);
        const winMax = limitFilter.winCapping ? limitFilter.winCapping * currencyMultiplier : slotLimits.winMax;

        const maxTotalStake = this.calculateMaxTotalStake(limitFeaturesToMaxTotalStake,
            totalBetMultiplier,
            currencyCode,
            limitFilter,
            slotLimits,
            stakeMax,
            currencyMultiplier);

        return {
            stakeAll,
            stakeDef,
            stakeMax,
            stakeMin,
            maxTotalStake,
            winMax
        };
    }

    private calculateMaxTotalStake(limitFeaturesToMaxTotalStake: boolean,
                                   totalBetMultiplier: number,
                                   currencyCode: string,
                                   limitFilter: GameGroupFilter,
                                   slotLimits: SlotGameLimits,
                                   stakeMax: number,
                                   currencyMultiplier: number): number {
        if (limitFeaturesToMaxTotalStake) {
            if (limitFilter.maxTotalBet) {
                return limitFilter.maxTotalBet * currencyMultiplier;
            } else {
                const currency = Currencies.get(currencyCode);
                return currency.toMajorUnits(currency.toMinorUnits(stakeMax) * totalBetMultiplier);
            }
        } else {
            return slotLimits.maxTotalStake;
        }
    }

    public async applyJurisdictionFilter(jurisdiction: Jurisdiction,
                                         limits: Limits,
                                         currency: string,
                                         options: JurisdictionOptions): Promise<SlotGameLimits> {
        const maxTotalStake = await this.calculateDynamicMaxTotalStake(jurisdiction.settings, currency, options);
        const slotLimits = limits as SlotGameLimits;
        if (maxTotalStake >= slotLimits.maxTotalStake) {
            return {
                ...slotLimits,
                maxTotalStake: options.limitFeaturesToMaxTotalStake ? slotLimits.maxTotalStake : maxTotalStake
            };
        } else {
            const maxCoin = maxTotalStake / options.totalBetMultiplier;
            const stakeAll = slotLimits.stakeAll.filter(stake => stake <= maxCoin);
            if (!stakeAll.length) {
                throw new Errors.ValidationError("Limits have invalid regulation settings for max total stake");
            }

            const stakeMax = Math.max(...stakeAll);
            const stakeDef = stakeAll.includes(slotLimits.stakeDef) ?
                             slotLimits.stakeDef :
                             Math.min(...stakeAll);
            return {
                ...slotLimits,
                stakeAll,
                maxTotalStake,
                stakeMax,
                stakeDef
            };
        }
    }

    private async calculateDynamicMaxTotalStake(
        jrsdSettings: JurisdictionSettings,
        currency: string,
        options: JurisdictionOptions
    ): Promise<number> {
        const service = await getCurrencyExchange();
        if (jrsdSettings.dynamicMaxTotalBetLimitEnabled) {
            if (!jrsdSettings.dynamicMaxTotalBetLimit && options.dynamicMaxTotalBetLimit) {
                return options.dynamicMaxTotalBetLimit;
            }
            if (!options.dynamicMaxTotalBetLimit && jrsdSettings.dynamicMaxTotalBetLimit?.defaultTotalBet) {
                return service.exchange(
                    +jrsdSettings.dynamicMaxTotalBetLimit.defaultTotalBet,
                    jrsdSettings.dynamicMaxTotalBetLimit.currency || "EUR",
                    currency
                );
            }
            if (options.dynamicMaxTotalBetLimit) {
                if (jrsdSettings.dynamicMaxTotalBetLimit?.defaultMaxTotalBet) {
                    const exchangedDefaultMaxTotalBet = service.exchange(
                        +jrsdSettings.dynamicMaxTotalBetLimit.defaultMaxTotalBet,
                        jrsdSettings.dynamicMaxTotalBetLimit.currency || "EUR",
                        currency
                    );
                    if (options.dynamicMaxTotalBetLimit <= exchangedDefaultMaxTotalBet) {
                        return options.dynamicMaxTotalBetLimit;
                    }
                    if (options.dynamicMaxTotalBetLimit > exchangedDefaultMaxTotalBet) {
                        return exchangedDefaultMaxTotalBet;
                    }
                } else {
                    return options.dynamicMaxTotalBetLimit;
                }
            }
        }
        return service.exchange(+jrsdSettings.maxTotalStake, "EUR", currency);
    }

    public async applyOverrideDefaultIfNeeded(gameGroupLimits: GameGroupLimitDBInstance,
                                              defaultLimits: Limits,
                                              currencyCode: string): Promise<Limits> {

        if (!gameGroupLimits) {
            return defaultLimits;
        }
        const groupLimitsByCurrency = await gameGroupLimits.getLimits(currencyCode);
        const overrideDefault = gameGroupLimits.overrideDefault;

        if (groupLimitsByCurrency && overrideDefault) {
            const slotLimits = groupLimitsByCurrency as SlotGameLimits;
            const defaultSlotLimits = defaultLimits as SlotGameLimits;
            if (!slotLimits.winMax && defaultSlotLimits.winMax) {
                slotLimits.winMax = defaultSlotLimits.winMax;
            }

            return slotLimits;
        }

        return { ...defaultLimits, ...groupLimitsByCurrency };
    }

    public async update(entity: BaseEntity,
                        gameGroupName: string,
                        gameCode: string,
                        limits: LimitsByCurrencyCode,
                        overrideDefault: boolean = false): Promise<void> {
        try {
            validateSuspended(entity);
            const { entityGame, gameGroup } = await GameGroupLimitServiceImpl.gameGroupService
                .findGroupAndGame(entity, gameGroupName, gameCode);
            await validateLimits(entityGame.game.type, limits);

            const gameLimits = await GameGroupLimitModel.findOne({
                where: {
                    gamegroupId: { [Op.eq]: gameGroup.id },
                    gameId: { [Op.eq]: entityGame.gameId }
                }
            });

            if (!gameLimits) {
                return Promise.reject(new Errors.GameGroupLimitNotFound());
            }

            gameLimits.setDataValue("limits", limits);
            gameLimits.setDataValue("overrideDefault", overrideDefault);
            /**
             * Sequelize does not detect deep mutations. To avoid problems with save, you should treat each attribute
             * as immutable and only assign new values.
             * @link https://sequelize.org/docs/v6/other-topics/upgrade/#modelchanged
             */
            gameLimits.changed("limits", true);
            gameLimits.changed("overrideDefault", true);
            await gameLimits.save();
        } finally {
            GameGroupLimitServiceImpl.cache.reset(
                GameGroupLimitServiceImpl.createCacheKey(entity.path, gameGroupName, gameCode)
            );
        }
    }

    public async updateByCurrency(entity: BaseEntity,
                                  gameGroupName: string,
                                  gameCode: string,
                                  currencyCode: CurrencyCode,
                                  limits: Limits): Promise<void> {
        try {
            if (!Currencies.exists(currencyCode)) {
                return Promise.reject(new Errors.CurrencyNotFoundError(currencyCode));
            }

            validateSuspended(entity);
            const { gameGroup, entityGame } = await GameGroupLimitServiceImpl.gameGroupService
                .findGroupAndGame(entity, gameGroupName, gameCode);

            const gameLimits = await GameGroupLimitModel.findOne({
                where: {
                    gamegroupId: { [Op.eq]: gameGroup.id },
                    gameId: { [Op.eq]: entityGame.gameId }
                }
            });

            if (!gameLimits) {
                return Promise.reject(new Errors.GameGroupLimitNotFound());
            }

            const overrideDefault = gameLimits.overrideDefault;
            const sanitizedLimits = sanitizeCurrencyLimits(entityGame.game.type, limits, true, overrideDefault);
            const limitsFound: IsLimitsResponse = isLimits(entityGame.game.type, sanitizedLimits);
            if (!limitsFound.isValid) {
                return Promise.reject(
                    new Errors.LimitsIncorrect(entityGame.game.type, currencyCode, limitsFound.invalidField)
                );
            }

            const data = gameLimits.limits;
            data[currencyCode] = sanitizedLimits;
            gameLimits.setDataValue("limits", data);
            /**
             * Sequelize does not detect deep mutations. To avoid problems with save, you should treat each attribute
             * as immutable and only assign new values.
             * @link https://sequelize.org/docs/v6/other-topics/upgrade/#modelchanged
             */
            gameLimits.changed("limits", true);
            await gameLimits.save();
        } finally {
            GameGroupLimitServiceImpl.cache.reset(
                GameGroupLimitServiceImpl.createCacheKey(entity.path, gameGroupName, gameCode)
            );
        }
    }

    public async removeByCurrency(entity: BaseEntity,
                                  gameGroupName: string,
                                  gameCode: string,
                                  currencyCode: string): Promise<void> {
        try {
            if (!Currencies.exists(currencyCode)) {
                return Promise.reject(new Errors.CurrencyNotFoundError(currencyCode));
            }
            validateSuspended(entity);

            const groupAndGame = await GameGroupLimitServiceImpl.gameGroupService
                .findGroupAndGame(entity, gameGroupName, gameCode);

            const gameLimits = await GameGroupLimitModel.findOne({
                where: {
                    gamegroupId: { [Op.eq]: groupAndGame.gameGroup.id },
                    gameId: { [Op.eq]: groupAndGame.entityGame.gameId }
                }
            });

            if (!gameLimits) {
                return Promise.reject(new Errors.GameGroupLimitNotFound());
            }

            const data = gameLimits.get("limits");
            delete data[currencyCode];

            gameLimits.setDataValue("limits", data);
            /**
             * Sequelize does not detect deep mutations. To avoid problems with save, you should treat each attribute
             * as immutable and only assign new values.
             * @link https://sequelize.org/docs/v6/other-topics/upgrade/#modelchanged
             */
            gameLimits.changed("limits", true);

            await gameLimits.save();
        } finally {
            GameGroupLimitServiceImpl.cache.reset(
                GameGroupLimitServiceImpl.createCacheKey(entity.path, gameGroupName, gameCode)
            );
        }
    }

    public static createCacheKey(entityPath: string, gameGroupName: string, gameCode: string): string {
        return `${entityPath}:${gameGroupName}:${gameCode}`;
    }

    private getQuery(keysMapper: Map<string, string>, query: WhereOptions<any>): WhereOptions<any> {
        const keysToMap: string[] = [...keysMapper.keys()];
        const entityQuery: WhereOptions<any> = FilterService.parseFilter(query, keysToMap);

        for (const parameter in entityQuery) {
            if (entityQuery.hasOwnProperty(parameter)) {
                const key = keysToMap.find((keyToMap) => parameter.indexOf(keyToMap) > -1);

                if (key) {
                    const dbKey = keysMapper.get(key);
                    entityQuery[dbKey] = { ...(entityQuery[parameter] as WhereOptions<any>) };
                    delete entityQuery[parameter];
                }
            }
        }

        return entityQuery;
    }
}

class GameGroupGameServiceImpl implements GameGroupGameService {
    private static gameGroupService: GameGroupService = getGameGroupService();

    public async findAll(entity: BaseEntity,
                         gameGroupName: string): Promise<GameCodeInfo[]> {
        validateSuspended(entity);

        const settings: EntitySettings = await getEntitySettings(entity.path);
        const gameGroup: GameGroupDBInstance = await GameGroupGameServiceImpl.gameGroupService.findOne(
            entity,
            {
                name: gameGroupName
            }, true, settings.gameGroupsInheritance);

        const queryGame: WhereOptions<any> = {
            status: "available"
        };
        const queryGameProvider: WhereOptions<any> = {
            status: "normal"
        };

        const gameGroupLimits: GameGroupLimitDBInstance[] = await GameGroupLimitModel.findAll(
            {
                include: [
                    {
                        model: GameModel,
                        where: queryGame,
                        include: [
                            {
                                model: GameProviderModel,
                                where: queryGameProvider
                            }
                        ]
                    }
                ],
                where: {
                    gamegroupId: gameGroup.get("id")
                }
            });

        return gameGroupLimits.map((l) => new GameImpl(l.game).toCodeInfo());
    }

    public async add(entity: BaseEntity,
                     gameGroupName: string,
                     gameCode: string,
                     limits: LimitsByCurrencyCode,
                     overrideDefault: boolean = false): Promise<void> {
        validateSuspended(entity);
        const { entityGame, gameGroup } = await getGameGroupService()
            .findGroupAndGame(entity, gameGroupName, gameCode);
        await validateLimits(entityGame.game.type, limits);

        const alreadyExists = await GameGroupLimitModel.findOne({
            where: {
                gamegroupId: gameGroup.id,
                entityGameId: entityGame.id
            }
        });

        if (alreadyExists) {
            throw new Errors.GameAlreadyExistsInGameGroup(gameGroupName, gameCode);
        }

        try {
            await GameGroupLimitModel.create(
                {
                    gamegroupId: gameGroup.id,
                    entityGameId: entityGame.id,
                    gameId: entityGame.gameId,
                    limits,
                    overrideDefault
                });
        } finally {
            GameGroupLimitServiceImpl.cache.reset(
                GameGroupLimitServiceImpl.createCacheKey(entity.path, gameGroupName, gameCode)
            );
        }
    }

    public async delete(entity: BaseEntity, gameGroupName: string, gameCode: string) {
        validateSuspended(entity);

        const result = await GameGroupGameServiceImpl.gameGroupService
            .findGroupAndGame(entity, gameGroupName, gameCode);

        const item = await GameGroupLimitModel.findOne({
            where: {
                gamegroupId: result.gameGroup.id,
                gameId: result.entityGame.gameId
            }
        });

        if (!item) {
            return Promise.reject(new Errors.GameGroupLimitNotFound());
        }

        await item.destroy();

        GameGroupLimitServiceImpl.cache.reset(
            GameGroupLimitServiceImpl.createCacheKey(entity.path, gameGroupName, gameCode)
        );
    }
}
