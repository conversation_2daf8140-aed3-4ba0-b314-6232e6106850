import * as Errors from "../errors";
import { ResourceNotFoundError, ValidationError } from "../errors";
import { EntityGameImpl, GameImpl, validateGameFeatures, validateGameSettings } from "./game";
import { validateLimits } from "./limits";
import { GameDBInstance } from "../models/game";
import { GameProviderDBInstance } from "../models/gameprovider";
import { Op, Transaction, UniqueConstraintError, WhereOptions } from "sequelize";
import * as GameService from "../services/game";
import { BaseEntity, MasterEntity } from "../entities/entity";
import { GameProvider, GameProviderInfo } from "../entities/gameprovider";
import {
    EntityGame,
    FindOneGameOptions,
    Game,
    GameClientFeatures,
    GameDescription,
    GameDescriptionByLocale,
    GameFeatures,
    GameSettings,
    LiveTable,
    VirtualGameStatus
} from "../entities/game";
import { LabelCreateData } from "../entities/label";
import { LimitsByCurrencyCode } from "../entities/gamegroup";
import { createEntity, findMaster } from "./entity";
import { sequelize as db } from "../storage/db";
import { getEntityGameService } from "./entityGameService";
import { favoriteGamesCache } from "./gameCategory/gameCategoryGamesService";
import { gameRTPHistoryService } from "./gameRTPHistory";
import { getSchemaDefinitionService } from "./gameLimits/schemaDefinition";
import { DeploymentGroupRoute, getDeploymentGroupService } from "./deploymentGroup";
import { mergeLiveSettings } from "./games/liveGame";
import { GAME_TYPES } from "../utils/common";
import { UrlPlaceholders } from "./gameUrl/urlPlaceholders";
import { uniqBy } from "lodash";
import * as LobbyCache from "../cache/lobby";
import { Models } from "../models/models";
import { encodeId } from "../utils/publicid";

const GameModel = Models.GameModel;
const GameProviderModel = Models.GameProviderModel;
const SchemaDefinitionModel = Models.SchemaDefinitionModel;
const EntityModel = Models.EntityModel;
const EntityGameModel = Models.EntityGameModel;

export interface RegisterGameData extends UpdateGameData {
    providerId: number;
    providerGameCode: string;
    gameCode: string;
    historyRenderType?: number;
}

export interface UpdateGameData {
    type?: string;
    title?: string;
    url?: string;
    defaultInfo?: GameDescription;
    info?: GameDescriptionByLocale;
    limits?: LimitsByCurrencyCode;
    labels?: Array<LabelCreateData>;
    comment?: string;
    settings?: GameSettings;
    features?: GameFeatures;
    clientFeatures?: GameClientFeatures;
    historyRenderType?: number;
    providerGameCode?: string;
    historyUrl?: string;
    limitsGroup?: string;
    countries?: string[];
    totalBetMultiplier?: number;
    schemaDefinitionId?: number;
    defaultClientVersion?: string;
    physicalTableId?: string;
    isMergeLiveSettings?: boolean;
    providerId?: number;
}

export interface CreateProviderData {
    user: string;
    code: string;
    title: string;
    secret: string;
    isTest?: boolean;
    mustStoreExtHistory?: boolean;
}

export interface ChangeSecretData {
    secret: string;
}

export class GameProviderImpl implements GameProvider {
    public id: number;
    public code: string;
    public user: string;
    public title: string;
    public secret: string;
    public status: string;
    public isTest: boolean;
    public mustStoreExtHistory: boolean;
    private version: number = 0;

    constructor(item?: GameProviderDBInstance) {
        if (!item) {
            return;
        }

        this.id = item.get("id");
        this.user = item.get("user");
        this.code = item.get("code");
        this.title = item.get("title");
        this.secret = item.get("secret");
        this.status = item.get("status");
        this.isTest = item.get("isTest");
        this.version = item.get("version");
        this.mustStoreExtHistory = item.get("mustStoreExtHistory");
    }

    public isSuspended(): boolean {
        return this.status === "suspended";
    }

    public async save(): Promise<this> {
        const oldVersion = this.version;
        this.version = oldVersion + 1;

        try {
            const updateResult: [number] = await GameProviderModel.update(this as any, {
                where: {
                    id: this.id,
                    version: oldVersion,
                },
            });
            if (updateResult[0] !== 1) {
                return Promise.reject(new Errors.OptimisticLockException());
            }
        } catch (err) {
            this.version = oldVersion;
            return Promise.reject(err);
        }

        return this;
    }

    public toInfo(): GameProviderInfo {
        return {
            id: encodeId(this.id),
            code: this.code,
            title: this.title,
            status: this.status,
            isTest: this.isTest ? true : undefined,
            mustStoreExtHistory: this.mustStoreExtHistory
        };
    }
}

/**
 * register - function to register new game
 */
export async function register(data: RegisterGameData): Promise<Game> {
    const provider: GameProvider = await findOne({ id: data.providerId });

    if (provider.isSuspended()) {
        return Promise.reject(new Errors.GameProviderSuspendedError(provider.code));
    }

    if (data.schemaDefinitionId) {
        await getSchemaDefinitionService().retrieve(data.schemaDefinitionId);
    }

    try {
        validateGameClientVersionPlaceholder(data);
        return await createGame(provider, data);
    } catch (err) {
        if (err instanceof UniqueConstraintError) {
            return Promise.reject(new Errors.GameAlreadyExistsError());
        }
        return Promise.reject(err);
    } finally {
        await LobbyCache.reset();
        await favoriteGamesCache.reset();
    }
}

/**
 * update - function to update the game
 */
export async function update(gameCode: string, data: UpdateGameData): Promise<Game> {
    const game = await GameService.findOne({
        code: gameCode
    });
    if (game.status === VirtualGameStatus.UNAVAILABLE) {
        return Promise.reject(new Errors.GameNotFoundError(gameCode));
    }

    validateGameClientVersionPlaceholder(data, game);

    const schemaDefinitionId = data?.schemaDefinitionId;
    if (schemaDefinitionId) {
        const schemaDefinition = await SchemaDefinitionModel.findByPk(+schemaDefinitionId);
        if (!schemaDefinition) {
            return Promise.reject(
                new ResourceNotFoundError(`Schema Definition not found by ID (${encodeId(schemaDefinitionId)})`)
            );
        }
    }

    const provider: GameProvider = await findOne({ id: game.providerId });
    if (provider.isSuspended()) {
        return Promise.reject(new Errors.GameProviderSuspendedError(provider.code));
    }

    if (data.providerId) {
        const newProvider = await findOne({ id: data.providerId });
        if (newProvider.isSuspended()) {
            return Promise.reject(new Errors.GameProviderSuspendedError(provider.code));
        }
        game.providerId = newProvider.id;
    }

    const existingGame = { ...game };
    // update game
    game.title = data.title || game.title;
    game.url = data.url || game.url;
    game.defaultInfo = data.defaultInfo || game.defaultInfo;
    game.info = data.info || game.info;
    game.type = data.type || game.type;

    if (data.limits) {
        game.limits = await validateLimits(game.type, data.limits);
    }
    if (data.features) {
        const features = await validateGameFeatures(
            game.type,
            data.features,
            game.features?.isExternalJackpotSupported
        );
        if (data.isMergeLiveSettings && game.type === GAME_TYPES.live) {
            features.live = mergeLiveSettings(features, game.features);
        }
        game.features = features;
    }

    if (data.historyRenderType !== undefined) {
        game.historyRenderType = data.historyRenderType;
    }

    if (data.providerGameCode) {
        game.providerGameCode = data.providerGameCode;
    }

    if (data.historyUrl !== undefined) {
        game.historyUrl = data.historyUrl;
    }
    if (data.limitsGroup !== undefined) {
        game.limitsGroup = data.limitsGroup;
    }

    if (data.countries !== undefined) {
        game.countries = data.countries !== null ? data.countries : [];
    }

    if (data.totalBetMultiplier !== undefined) {
        game.totalBetMultiplier = data.totalBetMultiplier;
    }

    if (schemaDefinitionId !== undefined) {
        game.schemaDefinitionId = schemaDefinitionId;
    }

    if (data.clientFeatures !== undefined) {
        game.clientFeatures = data.clientFeatures;
    }

    if (data.defaultClientVersion !== undefined) {
        game.defaultClientVersion = data.defaultClientVersion;
    }
    game.physicalTableId = data?.physicalTableId;

    let updatedGame: Game;
    await db.transaction(async (transaction: Transaction) => {
        if (data.settings) {
            const master = await findMaster();
            const newSettings = { settings: await validateGameSettings(game, data.settings, false) };
            const entityService = getEntityGameService(master);
            await entityService.update(game.code, newSettings, transaction);
        }

        updatedGame = await game.save(transaction);
    });

    gameRTPHistoryService.get().logGame(updatedGame, existingGame);

    return updatedGame;
}

export async function getGame(gameCode: string, options: FindOneGameOptions = {}): Promise<Game> {
    const game = await findGame(gameCode, options);
    if (game.status === VirtualGameStatus.UNAVAILABLE) {
        throw new Errors.GameNotFoundError(gameCode);
    }
    return game;
}

export async function disableGame(gameCode: string, removeEntityGames = false): Promise<Game> {
    const game = await findGame(gameCode);
    if (game.status === VirtualGameStatus.UNAVAILABLE) {
        throw new Errors.GameNotFoundError(gameCode);
    }
    game.status = VirtualGameStatus.UNAVAILABLE;
    if (!removeEntityGames) {
        return game.save();
    }
    const items = await EntityGameModel.findAll({
        where: {
            gameId: game.id
        },
        include: [
            {
                model: EntityModel,
                required: true
            }
        ]
    });
    const entities = items
        .map(item => item.get("entity"))
        .filter(Boolean)
        .map<BaseEntity>(createEntity)
        .filter(entity => entity.isMaster() === false)
        .sort((a, b) => b.id - a.id);

    const services = uniqBy(entities, "id").map(getEntityGameService);
    return await db.transaction(async (transaction) => {
        const isLive = game.type === GAME_TYPES.live;
        for (const service of services) {
            await service.delete(gameCode, isLive, true, transaction);
        }
        return game.save(transaction);
    });
}

export async function enableGame(gameCode: string): Promise<Game> {
    const game = await findGame(gameCode);
    if (game.status === VirtualGameStatus.AVAILABLE) {
        throw new Errors.GameNotFoundError(gameCode);
    }
    game.status = VirtualGameStatus.AVAILABLE;
    return game.save();
}

async function findGame(code: string, options: FindOneGameOptions = {}): Promise<Game> {
    const game = await GameService.findOne({ code }, options);
    if (game.gameProvider && game.gameProvider.isSuspended()) {
        throw new Errors.GameProviderSuspendedError(game.gameProvider.code);
    }
    return game;
}

export async function create(entity: BaseEntity, data: CreateProviderData): Promise<GameProviderInfo> {
    await checkMaster(entity);

    const provider: GameProvider = new GameProviderImpl();
    provider.user = data.user;
    provider.code = data.code;
    provider.title = data.title;
    provider.secret = data.secret;
    provider.status = "normal";
    provider.isTest = data.isTest || false;
    provider.mustStoreExtHistory = data.mustStoreExtHistory || false;

    try {
        const item: GameProviderDBInstance = await GameProviderModel.create(provider);
        return new GameProviderImpl(item).toInfo();
    } catch (err) {
        if (err instanceof UniqueConstraintError) {
            return Promise.reject(new Errors.GameProviderAlreadyExistsError());
        }
        return Promise.reject(err);
    }
}

export async function changeSecret(entity: BaseEntity,
                                   providerId: number,
                                   data: ChangeSecretData): Promise<GameProviderInfo> {
    await checkMaster(entity);
    const provider = await findOne({ id: providerId });
    if (provider.isSuspended()) {
        return Promise.reject(new Errors.GameProviderSuspendedError(provider.code));
    }
    provider.secret = data.secret;

    const result = await provider.save();
    return result.toInfo();
}

export async function suspend(entity: BaseEntity, providerId: number): Promise<GameProviderInfo> {
    return changeStatus(entity, providerId, "suspended");
}

export async function restore(entity: BaseEntity, providerId: number): Promise<GameProviderInfo> {
    return changeStatus(entity, providerId, "normal");
}

async function changeStatus(entity: BaseEntity, providerId: number, status: string): Promise<GameProviderInfo> {
    await checkMaster(entity);
    const provider = await findOne({ id: providerId });
    provider.status = status;
    const result = await provider.save();
    return result.toInfo();
}

function checkMaster(entity: BaseEntity): MasterEntity {
    if (!entity.isMaster()) {
        throw new Errors.NotMasterEntityError();
    }
    if (entity.isSuspended()) {
        throw new Errors.ParentSuspendedError();
    }
    return entity as MasterEntity;
}

/**
 * search - Get all available optionally filtered Game providers
 */
export async function search(query?: WhereOptions<any>): Promise<GameProviderInfo[]> {
    const list = await GameProviderModel.findAll({ where: query });
    return list.map(item => new GameProviderImpl(item).toInfo());
}

export async function findOne(query: WhereOptions<any>): Promise<GameProvider> {
    const item: GameProviderDBInstance = await
        GameProviderModel.findOne({
            where: query,
        });
    if (!item) {
        return Promise.reject(new Errors.GameProviderNotFoundError());
    }
    return new GameProviderImpl(item);
}

async function createGame(provider: GameProvider, data: RegisterGameData): Promise<Game> {
    const game: Game = new GameImpl();
    // TODO: SWS-218 Add gameCode to entity game;
    // TODO: return provider.code + data.providerGameCode;
    game.code = data.gameCode;
    game.title = data.title;
    game.type = data.type;
    game.status = VirtualGameStatus.AVAILABLE;
    game.providerId = provider.id;
    game.providerGameCode = data.providerGameCode;
    game.url = data.url;
    game.defaultInfo = data.defaultInfo;
    game.info = data.info;
    game.limits = await validateLimits(game.type, data.limits);
    game.features = await validateGameFeatures(game.type, data.features, data.features?.isExternalJackpotSupported);
    game.clientFeatures = data.clientFeatures;

    if (data.historyRenderType !== undefined) {
        game.historyRenderType = data.historyRenderType;
    }

    if (data.historyUrl !== undefined) {
        game.historyUrl = data.historyUrl;
    }
    game.limitsGroup = data.limitsGroup;
    game.countries = data.countries;
    game.totalBetMultiplier = data.totalBetMultiplier;
    game.schemaDefinitionId = data.schemaDefinitionId;
    game.defaultClientVersion = data.defaultClientVersion;
    game.physicalTableId = data?.physicalTableId;

    let gameImpl: GameImpl;
    await db.transaction(async (transaction) => {
        const item: GameDBInstance = await GameModel.create(game, { transaction: transaction });
        gameImpl = new GameImpl(item);
        gameRTPHistoryService.get().logGame(gameImpl);

        // enable game for master entity
        const master: MasterEntity = await findMaster();
        const entityGame: EntityGame = new EntityGameImpl();
        entityGame.entityId = master.id;
        entityGame.gameId = gameImpl.id;
        entityGame.game = gameImpl;
        entityGame.settings = await validateGameSettings(game, data.settings);
        entityGame.status = "normal";

        await GameService.addEntityGame(master, entityGame, transaction);

        if (gameImpl.isLiveGame()) {
            await getDeploymentGroupService()
                .assignDeploymentGroupToGame(gameImpl.code, DeploymentGroupRoute.live, transaction);
        }
    });

    return gameImpl;
}

function validateGameClientVersionPlaceholder(data: UpdateGameData, game?: Game) {
    const hasDefaultClientVersion = !!data.defaultClientVersion || !!(game && game.defaultClientVersion);

    if (data.url && data.url.includes(UrlPlaceholders.KEY_CLIENT_VERSION) && !hasDefaultClientVersion) {
        throw new ValidationError("\"defaultClientVersion\" field is required when url contains" +
            `${UrlPlaceholders.KEY_CLIENT_VERSION} placeholder.`);
    }
}

export async function updateAllLiveGamesVideoSetting(physicalTableId: number, videoSettings: any) {
    const games = (await GameModel.findAll({ where: { physicalTableId } }))
        .map(game => new GameImpl(game));
    for (const game of games) {
        (game.features.live as LiveTable).providerSettings = {
            ...(game.features.live as LiveTable).providerSettings,
            ...videoSettings
        };
        await game.save();
    }
    return games.map(game => game.code);
}

export async function updateLiveGamesSetting(gameCodes: string[], settings: any) {
    const games = (await GameModel.findAll({ where: { code: { [Op.in]: gameCodes } } }))
        .map(game => new GameImpl(game));
    for (const game of games) {
        const players = (game.features.live as LiveTable).providerSettings.players;
        (game.features.live as LiveTable).providerSettings = settings;
        (game.features.live as LiveTable).providerSettings["players"] = players;
        await game.save();
    }
}

export async function removeTableIdInGames(physicalTableId: number) {
    const games = (await GameModel.findAll({ where: { physicalTableId } }))
        .map(game => new GameImpl(game));
    for (const game of games) {
        game.physicalTableId = null;
        await game.save();
    }
    return games.map(game => game.code);
}
