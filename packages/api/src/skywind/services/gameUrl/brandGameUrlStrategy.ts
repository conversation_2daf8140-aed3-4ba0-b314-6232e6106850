import { EntityGame } from "../../entities/game";
import { BrandEntity } from "../../entities/brand";
import { PlayMode, StartGameTokenData } from "@skywind-group/sw-wallet-adapter-core";
import { BaseGameUrlStrategy, InnerStartGameTokenData, InnerStartGameURLInfo } from "./baseGameUrlStrategy";
import { EntityHelper } from "./entityHelper";
import { getBrandPlayerValidator } from "../brandPlayerValidator";
import { EntitySettings } from "../../entities/settings";
import { CountrySource, getIpCountrySource } from "../../utils/countrySource";
import { PlayerShortLoginTokenData } from "../../utils/token";
import { BrandTokenPayload, ClientPayload } from "./getGameURLInfo";
import { createPlayerSessionFacade } from "../player/playerSessionFacade";
import { getPlayerInfoService } from "../playerInfo";

export class BrandGameUrlStrategy extends BaseGameUrlStrategy {
    constructor(entityGame: EntityGame,
                brand: BrandEntity,
                entitySettings: EntitySettings,
                isLobby: boolean,
                private readonly player: PlayerShortLoginTokenData) {
        super(entityGame, brand, entitySettings, isLobby);
    }

    protected async validateBonusCoinsAvailable(tokenData: StartGameTokenData | undefined, playMode: PlayMode) {
        const currency = this.getCurrency(tokenData);
        if (playMode === PlayMode.BNS) {
            await getBrandPlayerValidator().validateBonusCoinsAvailable(
                this.brand,
                this.player.code,
                currency,
                this.entityGame.game
            );
        }
    }

    protected async getTokenData(payload: BrandTokenPayload): Promise<InnerStartGameURLInfo> {
        const game = this.entityGame.game;
        const tokenData: InnerStartGameTokenData = {
            playerCode: this.player.code,
            brandId: this.brand.id,
            gameCode: game.code,
            providerCode: game.gameProvider.code,
            providerGameCode: game.providerGameCode,
            currency: this.player.currency,
            playmode: payload.playmode || payload.playMode || PlayMode.REAL,
            test: this.player.isTest || this.brand.isTest,
            referrer: payload.referrer,
            gameGroup: this.player.gameGroup || undefined,
        };
        if (this.launchInsideLobby) {
            const session = await createPlayerSessionFacade().find({
                playerCode: this.player.code,
                brandId: this.brand.id
            }, false);
            tokenData.sessionId = session.sessionId;
        }
        return {
            tokenData,
            urlParams: {}
        } as any;
    }

    protected getCurrency(tokenData: StartGameTokenData | undefined) {
        return this.player.currency;
    }

    protected getLanguage(tokenData: StartGameTokenData | undefined, payload: ClientPayload) {
        return EntityHelper.getAvailableLanguage(this.brand, payload.language, this.player.language);
    }

    protected async getCountrySource(tokenData: | undefined, ip?: string): Promise<CountrySource> {
        return getIpCountrySource({
            entitySettings: this.entitySettings,
            playerInfo: await getPlayerInfoService().getPlayerInfo(this.player.code, this.brand.id),
            ip
        });
    }
}
