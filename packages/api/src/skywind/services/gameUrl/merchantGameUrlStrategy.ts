import { EntityGame } from "../../entities/game";
import { BrandEntity } from "../../entities/brand";
import { Merchant } from "../../entities/merchant";
import { COUNTRIES } from "../../utils/common";
import {
    MerchantGameInitRequest,
    MerchantGameURLInfo,
    PlayMode,
    StartGameTokenData
} from "@skywind-group/sw-wallet-adapter-core";
import { EntityHelper } from "./entityHelper";
import { getBrandPlayerValidator } from "../brandPlayerValidator";
import { EntitySettings } from "../../entities/settings";
import logger from "../../utils/logger";
import { CountrySource, getIpCountrySource, getMerchantCountrySource } from "../../utils/countrySource";
import { BaseGameUrlStrategy, InnerStartGameURLInfo } from "./baseGameUrlStrategy";
import { ClientPayload } from "./getGameURLInfo";
import config from "../../config";
import { getPlayerInfoService } from "../playerInfo";
import { getEntitySettings } from "../settings";

const log = logger("get-merchant-game-url");

export class MerchantGameUrlStrategy extends BaseGameUrlStrategy {
    constructor(entityGame: EntityGame,
                brand: BrandEntity,
                entitySettings: EntitySettings,
                isLobby: boolean,
                private readonly merchant: Merchant) {
        super(entityGame, brand, entitySettings, isLobby);
    }

    protected async createGameUrl(payload: MerchantGameInitRequest): Promise<MerchantGameURLInfo> {
        const { urlParams, tokenData } = await this.merchant.createGameUrl(
            this.entityGame.game.code,
            this.entityGame,
            payload
        );

        if (tokenData) {
            tokenData.playmode = tokenData.playmode || PlayMode.REAL;
            tokenData.referrer = payload.referrer;
        }

        return { urlParams, tokenData };
    }

    protected async getTokenData(payload: MerchantGameInitRequest): Promise<InnerStartGameURLInfo> {
        const { urlParams, tokenData } = await this.createGameUrl(payload);
        if (tokenData) {
            tokenData.operatorCountry = this.validateOperatorCountry(tokenData.operatorCountry || tokenData.country);
            tokenData.country = await this.validateCountry(tokenData.country, payload.ip);
            tokenData.currency = this.getCurrency(tokenData);
        }
        return { urlParams, tokenData };
    }

    protected validateOperatorCountry(country?: string): string | undefined {
        if (!country) {
            log.info(`Merchant [${this.merchant.type}-${this.merchant.code}] did not indicate the country`);
            return;
        }
        if (!COUNTRIES[country]) {
            log.info(`Merchant [${this.merchant.type}-${this.merchant.code}] uses invalid ISO country code ${country}`);
            return;
        }
        return country;
    }

    protected async validateCountry(operatorCountry?: string, ip?: string): Promise<string | undefined | null> {
        const useIp = config.merchantGameRestrictionsUseIp || this.merchant.params?.gameRestrictionsUseIp;
        if (!useIp && operatorCountry && COUNTRIES[operatorCountry]) {
            return operatorCountry;
        }
        if (!ip) {
            return "";
        }
        const { code } = await getIpCountrySource({ entitySettings: this.entitySettings, ip });
        const isWhitelisted = !code;
        if (isWhitelisted) {
            const entitySettings = await getEntitySettings(this.brand.path);
            return EntityHelper.getDefaultCountry(this.brand, entitySettings);
        }
        return code;
    }

    protected async validateBonusCoinsAvailable(tokenData: StartGameTokenData | undefined, playMode: PlayMode) {
        const currency = this.getCurrency(tokenData);
        if (playMode === PlayMode.BNS) {
            await getBrandPlayerValidator().validateBonusCoinsAvailable(
                this.brand,
                tokenData.playerCode,
                currency,
                this.entityGame.game
            );
        }
    }

    protected getCurrency(tokenData: StartGameTokenData | undefined) {
        return EntityHelper.getAvailableCurrency(this.brand, tokenData?.currency);
    }

    protected getLanguage(tokenData: StartGameTokenData | undefined, payload: ClientPayload) {
        return EntityHelper.getAvailableLanguage(this.brand, payload.language, this.brand.defaultLanguage);
    }

    protected async getCountrySource(tokenData: StartGameTokenData | undefined,
                                     ip?: string): Promise<CountrySource> {
        const playerInfo = tokenData?.playerCode && await getPlayerInfoService().getPlayerInfo(
            tokenData.playerCode,
            this.brand.id
        );
        return getMerchantCountrySource({
            entitySettings: this.entitySettings,
            merchantParams: this.merchant.params,
            playerInfo,
            countryCode: tokenData?.operatorCountry,
            ip
        });
    }
}
