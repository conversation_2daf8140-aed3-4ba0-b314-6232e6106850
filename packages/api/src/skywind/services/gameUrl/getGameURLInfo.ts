import { ClientPlatform, EntityGame, PlayerGameURLInfo } from "../../entities/game";
import { BrandEntity } from "../../entities/brand";
import { Merchant } from "../../entities/merchant";
import { getLauncherGameUrl } from "./getLauncherGameUrl";
import { EntitySettings } from "../../entities/settings";
import { PlayerShortLoginTokenData } from "../../utils/token";
import { isWebSiteWhitelistedCheckAvailable } from "../entity";
import { GameUrlStrategy } from "./gameUrlStrategy";
import { MerchantGameInitRequest, PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { LobbyMerchantGameUrlStrategy } from "./lobbyMerchantGameUrlStrategy";
import { MerchantGameUrlStrategy } from "./merchantGameUrlStrategy";
import { BrandGameUrlStrategy } from "./brandGameUrlStrategy";
import { GAME_TYPES } from "../../utils/common";
import { UrlPlaceholders } from "./urlPlaceholders";
import config from "../../config";

export type ClientPayload = (MerchantGameInitRequest | BrandTokenPayload) & {
    ip?: string;
    platform?: ClientPlatform;
    lobby?: string;
    cashier?: string;
    lobbySessionId?: string;
    isExternalLogin?: boolean; // Used for brand-merchant
    isLiveLobby?: boolean; // ISB flag
    providerGameCode?: string;
    isLive?: boolean;
};

export interface BrandTokenPayload {
    playMode?: PlayMode;
    playmode?: PlayMode;
    language?: string;
    referrer?: string;
}

interface GetGamesUrlOptions {
    entityGame: EntityGame;
    brand: BrandEntity;
    entitySettings: EntitySettings;
    merchant?: Merchant;
    player?: PlayerShortLoginTokenData;
    disableLauncher: boolean;
    ignoreWebSiteWhitelistedCheck?: boolean;
    isLobby?: boolean;
    request: ClientPayload;
}

export async function getGameURLInfo({
                                         entityGame,
                                         brand,
                                         entitySettings,
                                         disableLauncher,
                                         ignoreWebSiteWhitelistedCheck,
                                         merchant,
                                         player,
                                         isLobby,
                                         request
                                     }: GetGamesUrlOptions): Promise<PlayerGameURLInfo> {
    const launcher = !disableLauncher && !ignoreWebSiteWhitelistedCheck && isWebSiteWhitelistedCheckAvailable(brand);
    if (launcher) {
        return getLauncherGameUrl(entityGame, brand, entitySettings, player, isLobby, request);
    }
    let strategy: GameUrlStrategy;
    if (merchant) {
        if (isLobby) {
            strategy = new LobbyMerchantGameUrlStrategy(entityGame, brand, entitySettings, isLobby, merchant, player);
        } else {
            strategy = new MerchantGameUrlStrategy(entityGame, brand, entitySettings, isLobby, merchant);
        }
    } else {
        strategy = new BrandGameUrlStrategy(entityGame, brand, entitySettings, isLobby, player);
    }
    const { dynamicDomainHost, staticDomainHost, ...gameUrlInfo } = await strategy.getUrl(request);
    if (entityGame.game?.type === GAME_TYPES.external && entityGame.settings?.forwardToWrapper) {
        gameUrlInfo.url = UrlPlaceholders.replace(config.wrapperLauncherUrlTemplate, {
            dynamicDomain: dynamicDomainHost,
            staticDomain: staticDomainHost,
            externalGameUrl: gameUrlInfo.url,
            wrapperLauncherVersion: entitySettings.wrapperLauncherVersion || "latest"
        });
        return gameUrlInfo;
    }
    return gameUrlInfo;
}
