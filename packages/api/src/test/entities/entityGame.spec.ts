import { assert, expect, should, use } from "chai";
import * as sinon from "sinon";
import * as GameService from "../../skywind/services/game";
import * as GameProviderService from "../../skywind/services/gameprovider";
import * as EntityService from "../../skywind/services/entity";
import * as TokenUtils from "../../skywind/utils/token";
import {
    BY_IP,
    CN_IP,
    complexStructure,
    createComplexStructure,
    createGameProvider,
    createRandomGameProvider,
    registerGame,
    registerRandomGame,
    resetDynamicDomain,
    setDynamicDomain,
    truncate,
    US_IP
} from "./helper";
import { BaseEntity, ChildEntity, Entity, ENTITY_TYPE, EntityStatus } from "../../skywind/entities/entity";
import { EntityGame, EntityGameInfo, GameInfo, LiveGameInfo } from "../../skywind/entities/game";
import { BrandEntity } from "../../skywind/entities/brand";
import * as Errors from "../../skywind/errors";
import { getAvailableOperations } from "../../skywind/services/security";
import { ValidationError } from "sequelize";
import { LimitsByCurrencyCode, StakedLimits } from "../../skywind/entities/gamegroup";
import { publicId } from "@skywind-group/sw-utils";
import { GameProviderInfo } from "../../skywind/entities/gameprovider";
import EntitySettingsService from "../../skywind/services/settings";
import { getJPNServer } from "../../skywind/services/jpnserver";
import EntityLanguageService from "../../skywind/services/entityLanguage";
import EntityCountryService from "../../skywind/services/entityCountry";
import getEntityFactory, { CreateData } from "../../skywind/services/entityFactory";
import getPlayerService from "../../skywind/services/brandPlayer";
import { getEntityGameService } from "../../skywind/services/entityGameService";
import * as UrlManager from "../../skywind/services/urlManager";
import { getDomainService } from "../../skywind/services/domain";
import { getEntityDomainService } from "../../skywind/services/entityDomainService";
import { DOMAIN_TYPE, ENTITY_GAME_STATUS, GAME_TYPES } from "../../skywind/utils/common";
import { liveManager } from "../../skywind/services/live";
import { getConfigWithLevels, getDefinitionWithSets, getRandomNumber } from "../helper";
import { usingDb } from "../../skywind/storage/redis";
import { DomainRouting } from "@skywind-group/sw-domain-routing";
import { PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { factory } from "factory-girl";
import { FACTORY } from "../factories/common";
import { getSchemaDefinitionService } from "../../skywind/services/gameLimits/schemaDefinition";
import { getSegmentCRUDService } from "../../skywind/services/gameLimits/segment";
import { getEntityGame } from "../../skywind/models/game";
import { validateCountriesRestrictions } from "../../skywind/api/entityGame";
import { DeploymentGroupRoute } from "../../skywind/services/deploymentGroup";
import { DynamicDomain } from "../../skywind/entities/domain";
import { GameType, Provider, TableStatus } from "@skywind-group/sw-live-core";

const chaiAsPromise = require("chai-as-promised");

should();
use(chaiAsPromise);

describe("Game", () => {
    const limitsData: LimitsByCurrencyCode = {
        USD: {
            maxTotalStake: 100,
            stakeAll: [ 0.1, 0.5, 1, 2, 3, 5 ],
            stakeDef: 1,
            stakeMax: 5,
            stakeMin: 0.1,
            winMax: 2000
        }
    };

    const jackpotInstancesDataMock = [
        {
            id: "jp1",
            type: "jp_type1"
        },
        {
            id: "jp2",
            type: "jp_type2"
        },
        {
            id: "jp4",
            type: "jp_type2"
        },
        {
            id: "id1",
            type: "jp_type1"
        },
        {
            id: "id2",
            type: "jp_type2"
        },
        {
            id: "id3",
            type: "jp_type1"
        },
        {
            id: "id4",
            type: "jp_type2"
        }
    ];

    const jackpotTypesDataMock: any = [
        {
            name: "jp_type1"
        },
        {
            name: "jp_type2"
        }
    ];

    const expectedGame = (provider: GameProviderInfo, game: GameInfo, labels?) => ({
        code: game.code,
        type: "slot",
        title: game.title,
        defaultInfo: {
            "description": "Aztak slot is …",
            "name": "Aztak Slot"
        },
        info: {
            EN: {
                "description": "Aztak slot is …",
                "name": "Aztak Slot"
            },
            ZH: {
                "description": "...",
                "name": "..."
            }
        },
        limits: limitsData,
        features: {},
        clientFeatures: {},
        labels: labels,
        limitsGroup: null,
        countries: null,
        historyRenderType: game.historyRenderType,
        historyUrl: null,
        providerCode: provider.code,
        providerTitle: provider.title,
        status: "normal",
        schemaDefinitionId: null,
        totalBetMultiplier: null,
        providerGameCode: game.providerGameCode,
        limitFiltersWillBeApplied: false,
        url: undefined,
        physicalTableId: undefined,
    });

    const createExpectedPagingInfo = (total) => ({
        limit: 0,
        offset: 0,
        total
    });

    let master;
    let getJackpotsMock;
    let getJackpotTypesMock;

    before(async () => {
        process.env.NODE_ENV = "test";
        await truncate();
        master = await createComplexStructure();

        getJackpotsMock = sinon.stub(getJPNServer(), "getJackpots").resolves(jackpotInstancesDataMock);

        getJackpotTypesMock = sinon.stub(getJPNServer(), "getJackpotTypes").resolves(jackpotTypesDataMock);
        await factory.create(FACTORY.DEPLOYMENT_GROUP_GAME, {}, { route: DeploymentGroupRoute.live });
    });

    after(async () => {
        getJackpotsMock.restore();
        getJackpotTypesMock.restore();
    });
    afterEach(async () => {
        const tle2: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2.key });
        const childEntity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2ent1.key });
        await usingDb(async (db) => {
            await db.del(`suspendedGames:${tle2.id}`);
            await db.del(`suspendedGames:${childEntity.id}`);
        });
    });

    it("Get game List by master", async () => {
        const p1 = await createRandomGameProvider();
        const p2 = await createRandomGameProvider();
        const game1 = await registerRandomGame(p1.code);
        const game2 = await registerRandomGame(p2.code);

        let gameInfoList: EntityGameInfo[] = await GameService.getAllGames(master);
        gameInfoList.sort((a, b) => a.code.localeCompare(b.code));
        gameInfoList = gameInfoList.map(game => {
            delete game.releaseDate;
            delete game.rtpInfo;
            return game;
        });

        expect(gameInfoList.length).equal(2);
        expect(gameInfoList[0]).deep.equal(expectedGame(p1, game1, []));
        expect(gameInfoList[1]).deep.equal(expectedGame(p2, game2, []));
    });

    it("Get game List by master - game provider is suspended", async () => {
        const p1 = await createRandomGameProvider();
        const p2 = await createRandomGameProvider();
        await registerRandomGame(p1.code);
        const game2 = await registerRandomGame(p2.code);

        await GameProviderService.suspend(master, publicId.instance.decode(p1.id));

        const providerGames = await GameService.getAllGames(master,
            undefined, { providerId: publicId.instance.decode(p2.id) });
        expect(providerGames.length).equal(1);
        delete providerGames[0].releaseDate;
        delete providerGames[0].rtpInfo;
        expect(providerGames[0]).deep.equal(expectedGame(p2, game2, []));

        const emptyProviderGames = await GameService.getAllGames(master, undefined,
            { providerId: publicId.instance.decode(p1.id) });
        expect(emptyProviderGames.length).equal(0);
    });

    it("Adding a game to an entity", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);

        const gameCodeInfo1 = await GameService.addGameToEntity(
            master,
            { path: ":TLE1:" },
            game.code, false);
        expect(gameCodeInfo1.code).equal(game.code);

        const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        const gameCodeInfo2 = await GameService.addGameToEntity(tle1,
            { path: ":TLE1:ENT1:" }, game.code, false);
        expect(gameCodeInfo2.code).equal(game.code);

        const ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
        await getEntityFactory(ent1).createEntity({
            type: ENTITY_TYPE.BRAND,
            name: "BRAND1",
            description: "BRAND1 description",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM",
            webSiteUrl: "http://hello-1.com"
        });

        const gameCodeInfo3 = await GameService.addGameToEntity(ent1, { path: ":TLE1:ENT1:BRAND1:" },
            game.code, false, { status: "normal", urlParams: { qwerty: 1 } as any });
        expect(gameCodeInfo3.urlParams).deep.eq({ qwerty: 1 });
        expect(gameCodeInfo3.code).equal(game.code);
    });

    it("Fail to add games to brand when it doesn't exist in parent", async () => {
        const provider = await createRandomGameProvider();
        const game1 = await registerRandomGame(provider.code);
        const game2 = await registerRandomGame(provider.code);

        const ent: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
        await GameService.addGamesToEntity(ent as ChildEntity, { codes: [ game1.code, game2.code ] }, false)
            .should.eventually.rejectedWith(Errors.GamesNotFound);
    });

    it("Success to add games to brand", async () => {
        const provider = await createRandomGameProvider();
        let features: { currenciesSupport?: string[] } = {
            "currenciesSupport": [
                "USD"
            ]
        };
        let game1 = await registerGame(
            provider.code,
            "SUCCESS_GAME_TEST_TEST",
            "SUCCESS_GAME_TEST_TEST",
            undefined,
            undefined,
            features
        );

        let ent: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        let result = await GameService.addGamesToEntity(ent as ChildEntity, { codes: [ game1.code ] }, false);
        expect(result[0].code).eq("SUCCESS_GAME_TEST_TEST");

        features = {};
        game1 = await registerGame(
            provider.code,
            "SUCCESS_GAME_TEST_TEST1",
            "SUCCESS_GAME_TEST_TEST1",
            undefined,
            undefined,
            features
        );

        ent = await EntityService.findOne({ key: complexStructure.tle1.key });
        result = await GameService.addGamesToEntity(ent as ChildEntity, { codes: [ game1.code ] }, false);
        expect(result[0].code).eq("SUCCESS_GAME_TEST_TEST1");
    });

    it("Fail to add games to brand when it doesn't support currency", async () => {
        const provider = await createRandomGameProvider();
        let features = {
            "currenciesSupport": [
                "EUR"
            ]
        };
        let game1 = await registerGame(
            provider.code,
            "GAME_TEST_TEST",
            "GAME_TEST_TEST",
            undefined,
            undefined,
            features
        );

        let ent: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        await GameService.addGamesToEntity(ent as ChildEntity, { codes: [ game1.code ] }, false)
            .should.eventually.rejectedWith(Errors.ValidationError)
            .then(err => {
                expect(err.message).to.be.equal(
                    "Validation error: Game \"GAME_TEST_TEST\" " +
                    "doesn't support \"USD\" currencies from the operator list."
                );
            });

        features = {
            "currenciesSupport": []
        };
        game1 = await registerGame(
            provider.code,
            "GAME_TEST_TEST1",
            "GAME_TEST_TEST1",
            undefined,
            undefined,
            features
        );

        ent = await EntityService.findOne({ key: complexStructure.tle1.key });
        await GameService.addGamesToEntity(ent as ChildEntity, { codes: [ game1.code ] }, false)
            .should.eventually.rejectedWith(Errors.ValidationError)
            .then(err => {
                expect(err.message).to.be.equal(
                    "Validation error: Game \"GAME_TEST_TEST1\" " +
                    "doesn't support \"USD\" currencies from the operator list."
                );
            });
    });

    it("Adds games to entity", async () => {
        const provider = await createRandomGameProvider();
        const game1 = await registerRandomGame(provider.code);
        const game2 = await registerRandomGame(provider.code);

        const tle2: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2.key });
        const info = await GameService.addGamesToEntity(tle2 as ChildEntity,
            { codes: [ game1.code, game2.code ] },
            false);
        expect(info).deep.equal([
            {
                "code": game2.code,
                "status": "normal"
            },
            {
                "code": game1.code,
                "status": "normal"
            }
        ]);
    });

    it("Adds game to entity hierarchy", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);

        const tle2: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2.key });
        const info = await GameService.addGamesToAllEntities(tle2 as Entity, { codes: [ game.code ] });
        expect(info).deep.equal({ addedGamesCount: 3 });
    });

    it("Adds game to entity hierarchy for hidden game", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);
        await getEntityGameService(master).update(game.code, { status: ENTITY_GAME_STATUS.HIDDEN });
        const tle2: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2.key });
        await GameService.addGamesToAllEntities(tle2 as Entity, { codes: [ game.code ] });
        const gameInfo = await GameService.getOneGame(tle2, game.code);
        expect(gameInfo.status).equal(ENTITY_GAME_STATUS.HIDDEN);
    });

    it("Adds games to entity hierarchy", async () => {
        const provider = await createRandomGameProvider();
        const games: GameInfo[] = [];
        for (let i = 0; i < 3; i++) {
            games.push(await registerRandomGame(provider.code));
        }
        const codes = games.map(g => g.code);
        const tle2: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2.key });
        const info = await GameService.addGamesToAllEntities(tle2 as Entity, { codes });
        expect(info).deep.equal({ addedGamesCount: 9 });
    });

    it("Fail to add game to brand when it doesn't support currency", async () => {
        const provider = await createRandomGameProvider();
        let features = {
            "currenciesSupport": [
                "EUR"
            ]
        };
        let game = await registerGame(
            provider.code,
            "GAME_TEST_TEST_test",
            "GAME_TEST_TEST_test",
            undefined,
            undefined,
            features
        );

        let ent: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        await GameService.addGameToEntity(ent as ChildEntity, {}, game.code, false)
            .should.eventually.rejectedWith(Errors.ValidationError)
            .then(err => {
                expect(err.message).to.be.equal(
                    "Validation error: Game \"GAME_TEST_TEST_test\" " +
                    "doesn't support \"USD\" currencies from the operator list."
                );
            });

        features = {
            "currenciesSupport": []
        };
        game = await registerGame(
            provider.code,
            "GAME_TEST_TEST_test1",
            "GAME_TEST_TEST_test1",
            undefined,
            undefined,
            features
        );

        ent = await EntityService.findOne({ key: complexStructure.tle1.key });
        await GameService.addGameToEntity(ent as ChildEntity, {}, game.code, false)
            .should.eventually.rejectedWith(Errors.ValidationError)
            .then(err => {
                expect(err.message).to.be.equal(
                    "Validation error: Game \"GAME_TEST_TEST_test1\" " +
                    "doesn't support \"USD\" currencies from the operator list."
                );
            });
    });

    it("Adds game to entity hierarchy if already added", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);

        const tle2: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2.key });

        await GameService.addGameToEntity(master, { key: complexStructure.tle2.key },
            game.code, false, { status: "suspended" });

        const info = await GameService.addGamesToAllEntities(tle2 as Entity, { codes: [ game.code ] });
        expect(info).deep.equal({ addedGamesCount: 2 });
    });

    it("Check if status of child game is suspend when parent is suspended", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);

        const tle2: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2.key });

        await GameService.addGameToEntity(master, { key: complexStructure.tle2.key },
            game.code, false);

        await GameService.addGamesToAllEntities(tle2 as Entity, { codes: [ game.code ] });

        const entityService = getEntityGameService(tle2);
        await entityService.suspend(game.code, false);
        const parentGame = await GameService.getOneGame(
            tle2,
            game.code
        );
        expect(parentGame.status).equal("suspended");
        const childEntity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2ent1.key });

        const childGame = await GameService.getOneGame(
            childEntity,
            game.code
        );

        expect(childGame.status).equal("suspended");
        await usingDb(async (db) => {
            const games = await db.smembers(`suspendedGames:${tle2.id}`);
            expect(games).deep.equal([ game.code ]);
            const childGames = await db.smembers(`suspendedGames:${childEntity.id}`);
            expect(childGames).deep.equal([ game.code ]);
        });
    });

    it("Patch entity game", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);

        const tle2: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2.key });

        await GameService.addGameToEntity(master, { key: complexStructure.tle2.key },
            game.code, false);

        const entityService = getEntityGameService(tle2);
        await entityService.update(game.code, { status: "suspended", urlParams: 1 } as any)
            .should.eventually.rejectedWith(Errors.ValidationError);

        const result = await entityService
            .update(game.code, { status: "suspended", urlParams: { new: "newParameter" } as any });

        expect(result.status).equal("suspended");

        await usingDb(async (db) => {
            const games = await db.smembers(`suspendedGames:${tle2.id}`);
            expect(games).deep.equal([ game.code ]);
        });
    });

    describe("jackpot", () => {

        let getJackpotTickers;
        let game;
        let brandEntity;
        const jackpotTickers = [
            {
                "jackpotId": "jp2",
                "jackpotType": "test",
                "jackpotBaseType": "baseType",
                "pools": {
                    "amount": 5
                }
            }
        ];

        before(async () => {
            const provider = await createRandomGameProvider();
            game = await registerRandomGame(provider.code);

            game.features = {
                jackpotTypes: [ "jp_type1", "jp_type2" ],
                isFreebetSupported: true,
                isGRCGame: false,
                transferEnabled: true
            };

            await GameProviderService.update(game.code, game);
            await GameService.addGameToEntity(
                master,
                { path: ":TLE1:" },
                game.code,
                false,
                { status: "normal" }
            );
            const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
            await GameService.addGameToEntity(tle1,
                { path: ":TLE1:ENT1:" },
                game.code,
                false,
                { status: "normal" });
            const ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const brandName = "BRAND_" + game.code;
            brandEntity = await getEntityFactory(ent1).createEntity({
                type: ENTITY_TYPE.BRAND,
                name: brandName,
                description: "BRAND description",
                defaultCurrency: "USD",
                defaultCountry: "US",
                defaultLanguage: "en",
                jurisdictionCode: "COM",
                webSiteUrl: "http://hello-11.com"
            });

            await GameService.addGameToEntity(ent1, { path: `:TLE1:ENT1:${brandName}:` },
                game.code,
                false,
                {
                    settings: { jackpotId: { jp_type1: "jp1", jp_type2: "jp2" } },
                    status: "normal"
                });

            getJackpotTickers = sinon.stub(getJPNServer(), "getJackpotTickers");
            getJackpotTickers.returns(jackpotTickers);
        });

        after(() => {
            getJackpotTickers.restore();
        });

        it("Fails to add jackpot game to an entity hierarchy if settings missing", async () => {
            const tle2: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2.key });

            await expect(GameService.addGamesToAllEntities(tle2 as Entity, { codes: [ game.code ] }))
                .to.be.rejectedWith(Errors.ValidationError);
        });

        it("Adds jackpot game to entity hierarchy", async () => {
            const entityService = getEntityGameService(master);
            await entityService.update(game.code, {
                settings: {
                    jackpotId: {
                        "jp_type1": "jp1",
                        "jp_type2": "jp2"
                    }
                }
            });

            const tle2: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2.key });

            const info = await GameService.addGamesToAllEntities(tle2 as Entity, { codes: [ game.code ] });
            expect(info).deep.equal({ addedGamesCount: 3 });
        });

        it("Get a game with jackpot", async () => {
            const gameInfo = await GameService.getGameWithJackpot(brandEntity, game.code);
            expect(gameInfo).to.have.property("jackpots");
            expect(gameInfo.jackpots.jp1).to.be.undefined;
            expect(gameInfo.jackpots.jp2).to.deep.equal({
                "currency": undefined,
                "id": "jp2",
                "pools": {
                    "amount": 5
                }
            });

            const allJackpotGames = await GameService.getAllGames(master, undefined,
                { jackpots: "true", jackpotTypes: "true" });
            expect(allJackpotGames.length).to.equal(1);
            expect(allJackpotGames[0].code).to.equal(game.code);
        });

        it("Get not jackpot games", async () => {
            const allGames = await GameService.getAllGames(master, undefined,
                { jackpots: "false", jackpotTypes: "false" });
            const gameCodes = allGames.map((item) => item.code);
            expect(gameCodes).to.not.include(game.code);
        });

        it("Get only jackpot games", async () => {
            const allJackpotGames = await GameService.getAllGames(master, undefined,
                { jackpots: "true", jackpotTypes: "true" });
            expect(allJackpotGames.length).to.equal(1);
            expect(allJackpotGames[0].code).to.equal(game.code);
        });

        it("Get only jackpot games using features filter", async () => {
            const allJackpotGames = await GameService.getAllGames(master, undefined,
                { features: "{\"jackpotTypes\": []}" });
            expect(allJackpotGames.length).to.equal(1);
            expect(allJackpotGames[0].code).to.equal(game.code);
        });

        it("Get only jackpot games using features filter and jackpot type filter", async () => {
            const allJackpotGames = await GameService.getAllGames(master, undefined,
                {
                    jackpots: "true", jackpotTypes: "true",
                    features: "{\"jackpotTypes\": [], \"isGRCGame\": false}"
                });
            expect(allJackpotGames.length).to.equal(1);
            expect(allJackpotGames[0].code).to.equal(game.code);
        });

        it("Get jackpot games with jackpot type", async () => {
            let jackpotGames = await GameService.getAllGames(master, undefined,
                { jackpots: "true", jackpotTypes: "jp_type1,jp_type2" });
            expect(jackpotGames.length).to.equal(1);

            jackpotGames = await GameService.getAllGames(master, undefined,
                { jackpots: "true", jackpotTypes: "jp_type1" });
            expect(jackpotGames.length).to.equal(1);

            jackpotGames = await GameService.getAllGames(master, undefined,
                { jackpots: "true", jackpotTypes: "jp_type2" });
            expect(jackpotGames.length).to.equal(1);

            jackpotGames = await GameService.getAllGames(master, undefined,
                { jackpots: "true", jackpotTypes: "unknown" });
            expect(jackpotGames.length).to.equal(0);

            jackpotGames = await GameService.getAllGames(master, undefined,
                { jackpots: "true", jackpotTypes: "" });
            expect(jackpotGames.length).to.equal(0);
        });

        it("Get jackpot games with jackpot type using feature filter ", async () => {
            let jackpotGames = await GameService.getAllGames(master, undefined,
                {
                    jackpots: "true", jackpotTypes: "jp_type1,jp_type2",
                    features: "{\"jackpotTypes\": [\"jp_type1\", \"jp_type2\"]}"
                });
            expect(jackpotGames.length).to.equal(1);

            jackpotGames = await GameService.getAllGames(master, undefined,
                { features: "{\"jackpotTypes\": [\"jp_type1\"]}" });
            expect(jackpotGames.length).to.equal(1);

            jackpotGames = await GameService.getAllGames(master, undefined,
                {
                    jackpots: "true",
                    features: "{\"jackpotTypes\": [\"jp_type2\"], \"isFreebetSupported\": true}"
                });
            expect(jackpotGames.length).to.equal(1);

            jackpotGames = await GameService.getAllGames(master, undefined,
                { jackpots: "true", features: "{\"jackpotTypes\": [\"unknown\"]}" });
            expect(jackpotGames.length).to.equal(0);

            jackpotGames = await GameService.getAllGames(master, undefined,
                { jackpots: "true", features: "{\"jackpotTypes\": \"\"}" });
            expect(jackpotGames.length).to.equal(0);
        });

        it("Get games by boolean flags using feature filter", async () => {
            let jackpotGames = await GameService.getAllGames(master, undefined,
                { features: "{\"isFreebetSupported\": true}" });
            expect(jackpotGames.length).to.equal(1);

            jackpotGames = await GameService.getAllGames(master, undefined,
                { features: "{\"isFreebetSupported\": true, \"isGRCGame\": false }" });
            expect(jackpotGames.length).to.equal(1);

            jackpotGames = await GameService.getAllGames(master, undefined,
                { features: "{\"isFreebetSupported\": true, \"isGRCGame\": false, \"transferEnabled\": true }" });
            expect(jackpotGames.length).to.equal(1);

            jackpotGames = await GameService.getAllGames(master, undefined,
                { features: "{\"isFreebetSupported\": false}" });
            expect(jackpotGames.length).to.equal(0);

            jackpotGames = await GameService.getAllGames(master, undefined,
                { features: "{\"isFreebetSupported\": true, \"isGRCGame\": true, \"transferEnabled\": true }" });
            expect(jackpotGames.length).to.equal(0);

            jackpotGames = await GameService.getAllGames(master, undefined,
                { features: "{\"isFreebetSupported\": true, \"isGRCGame\": true, \"transferEnabled\": true, \"customFeature\": false }" });
            expect(jackpotGames.length).to.equal(0);
        });
    });

    describe("live", () => {

        let getLiveTables: sinon.SinonStub;
        let brandEntity;
        const table: LiveGameInfo = {
            isMaintenance: true,
            dealer: {
                "name": "Mock",
                "picture": "http://picture.com/mock.jpeg"
            },
            id: "mock-0-1",
            provider: Provider.MOCK,
            status: TableStatus.ONLINE,
            type: GameType.BACCARAT,
            scoreboard: "",
            settings: {}
        };
        const liveInfo: Map<string, LiveGameInfo> = new Map([[`${table.provider}:${table.id}`, table]]);
        let liveGame;

        before(async () => {
            getLiveTables = sinon.stub(liveManager, "getTables");
            getLiveTables.resolves(liveInfo);

            const provider = await createRandomGameProvider();
            await registerRandomGame(provider.code);
            liveGame = await registerRandomGame(provider.code, GAME_TYPES.live);

            liveGame.features = {
                live: {
                    provider: "mock",
                    tableId: "mock-0-1"
                }
            };

            await GameProviderService.update(liveGame.code, liveGame);
            await GameService.addGameToEntity(
                master,
                { path: ":TLE1:" },
                liveGame.code,
                true,
                { status: "normal" }
            );
            const ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const brandName = "BRAND_" + liveGame.code;
            brandEntity = await getEntityFactory(ent1).createEntity({
                type: ENTITY_TYPE.BRAND,
                name: brandName,
                description: "BRAND description",
                defaultCurrency: "USD",
                defaultCountry: "US",
                defaultLanguage: "en",
                jurisdictionCode: "COM",
                webSiteUrl: "http://test-221.com",
            });

            await GameService.addGamesToAllEntities(await EntityService.findOne<ChildEntity>(
                    { key: complexStructure.tle1.key }),
                { codes: [ liveGame.code ] });
        });

        after(() => {
            getLiveTables.restore();
        });

        it("Get games with live", async () => {
            const gamesWithLive = await GameService.getAllGames(brandEntity, {}, { includeLive: "true" });
            expect(gamesWithLive.length).to.equal(1);
            expect(gamesWithLive[0].live).to.exist;
            expect(gamesWithLive[0].live).to.deep.equal(table);

            const gamesInfo = await GameService.getAllGames(brandEntity, {}, { includeLive: "false" });
            expect(gamesInfo.length).to.equal(1);
            expect(gamesInfo[0].live).to.not.exist;
        });

        it("Get only live games", async () => {
            const liveGames = await GameService.getAllGames(master, undefined,
                { includeLive: "true", live: "true" });
            expect(liveGames.length).to.equal(1);
            expect(liveGames[0].code).to.equal(liveGame.code);
        });

        it("Get only live games using feature filter", async () => {
            let liveGames = await GameService.getAllGames(master, undefined,
                {
                    includeLive: "true", live: "true",
                    features: "{\"live\": {}}"
                });
            expect(liveGames.length).to.equal(1);
            expect(liveGames[0].code).to.equal(liveGame.code);

            liveGames = await GameService.getAllGames(master, undefined,
                { features: "{\"live\": {}}" });
            expect(liveGames.length).to.equal(1);
            expect(liveGames[0].code).to.equal(liveGame.code);
        });

        it("Get not live games", async () => {
            const allGames = await GameService.getAllGames(master, undefined,
                { includeLive: "false", live: "false" });
            const gameCodes = allGames.map((item) => item.code);
            expect(gameCodes).to.not.include(liveGame.code);
        });

        it("Get live games with provider", async () => {
            const liveGames = await GameService.getAllGames(master, undefined,
                { includeLive: "true", live: "{\"provider\":\"mock\"}" });
            expect(liveGames.length).to.equal(1);
            expect(liveGames[0].code).to.equal(liveGame.code);
        });

        it("Get live games with provider using feature filter", async () => {
            let liveGames = await GameService.getAllGames(master, undefined,
                {
                    includeLive: "true", live: "{\"provider\":\"mock\"}",
                    features: "{\"live\": {\"provider\":\"mock\"}}"
                });
            expect(liveGames.length).to.equal(1);
            expect(liveGames[0].code).to.equal(liveGame.code);

            liveGames = await GameService.getAllGames(master, undefined,
                { features: "{\"live\": {\"provider\":\"mock\"}}" });
            expect(liveGames.length).to.equal(1);
            expect(liveGames[0].code).to.equal(liveGame.code);
        });
    });

    it("Update game for the entity", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);

        await GameService.addGameToEntity(
            master,
            { path: ":TLE1:" },
            game.code,
            false, { status: "normal" }
        );
        const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        await GameService.addGameToEntity(tle1,
            { path: ":TLE1:ENT1:" },
            game.code,
            false, { status: "normal" });
        const ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
        const brandName = "BRAND_" + game.code;
        await getEntityFactory(ent1).createEntity({
            type: ENTITY_TYPE.BRAND,
            name: brandName,
            description: "BRAND description",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM",
            webSiteUrl: "http://hello-2.com"
        });
        await GameService.addGameToEntity(ent1, { path: `:TLE1:ENT1:${brandName}:` },
            game.code, false, { settings: {}, status: "normal" });

        const updatedGameInfo1 = await (getEntityGameService(master.find({ path: ":TLE1:" }))).update(
            game.code, { status: "normal" });
        expect(updatedGameInfo1).deep.equal({ code: game.code, status: "normal", settings: {} });
        const updatedGameInfo2 = await (getEntityGameService(tle1.find({ path: ":TLE1:ENT1:" })))
            .update(game.code);
        expect(updatedGameInfo2).deep.equal({ code: game.code, status: "normal", settings: {} });
        const updatedGameInfo3 = await (getEntityGameService(ent1.find({ path: `:TLE1:ENT1:${brandName}:` }))
        )
            .update(game.code, { settings: { custom: true } });
        expect(updatedGameInfo3).deep.equal({ code: game.code, settings: { custom: true }, status: "normal" });
    });

    it("Update game for the entity - change status", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);

        await GameService.addGameToEntity(master, { path: ":TLE1:" }, game.code,
            false, { status: "normal" });
        const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        await GameService.addGameToEntity(tle1,
            { path: ":TLE1:ENT1:" },
            game.code,
            false, { status: "normal" });
        const ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
        const brandName = "BRAND_" + game.code;
        await getEntityFactory(ent1).createEntity({
            type: ENTITY_TYPE.BRAND,
            name: brandName,
            description: "BRAND description",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM",
            webSiteUrl: "http://hello-3.com"
        });
        await GameService.addGameToEntity(ent1, { path: `:TLE1:ENT1:${brandName}:` },
            game.code, false, { settings: {}, status: "normal" });

        const updatedGameInfo1 = await (getEntityGameService(master.find({ path: ":TLE1:" })))
            .update(game.code);
        expect(updatedGameInfo1).deep.equal({ code: game.code, status: "normal", settings: {} });
        const updatedGameInfo2 = await (getEntityGameService(tle1.find({ path: ":TLE1:ENT1:" })))
            .update(game.code);
        expect(updatedGameInfo2).deep.equal({ code: game.code, status: "normal", settings: {} });
        const updatedGameInfo3 = await (
            getEntityGameService(ent1.find({ path: `:TLE1:ENT1:${brandName}:` }))
        )
            .update(game.code, { settings: { custom: true }, status: "normal" });
        expect(updatedGameInfo3).deep.equal(
            { code: game.code, settings: { custom: true }, status: "normal" });
    });

    it("Update game limitFilters for operator", async () => {
        const provider = await createRandomGameProvider();
        const game: GameInfo = await registerRandomGame(provider.code);

        await GameService.addGameToEntity(master, { path: ":TLE1:" }, game.code,
            false, { status: "normal" });

        const currency = Object.keys(game.limits)[getRandomNumber(0, Object.keys(game.limits).length - 1)];
        const stakeLimits: StakedLimits = game.limits[currency] as StakedLimits;
        const entity = master.find({ path: ":TLE1:" });
        const service = getEntityGameService(entity);

        const newCurrencyLimits = {
            stakeMax: stakeLimits.stakeAll[stakeLimits.stakeAll.length - 2],
            stakeMin: stakeLimits.stakeAll[1]
        };

        await service.updateLimits(game.code, {
            [currency]: newCurrencyLimits
        });

        const entityGame: EntityGame = await GameService.findOneEntityGame(entity, game.code);
        expect(entityGame).deep.equal({
            ...entityGame,
            limitFilters: {
                [currency]: newCurrencyLimits
            }
        });
    });

    it("Bulk update game limitFilters for operator", async () => {
        const provider = await createRandomGameProvider();
        const games: GameInfo[] = [];
        const gamesCount = 5;

        for (let i = 0; i < gamesCount; i++) {
            const game: GameInfo = await registerRandomGame(provider.code);
            await GameService.addGameToEntity(master, { path: ":TLE1:" }, game.code,
                false, { status: "normal" });
            games.push(game);
        }

        const gameCodes: string[] = games.map(game => game.code);

        const currency = Object.keys(games[0].limits)[getRandomNumber(0, Object.keys(games[0].limits).length - 1)];
        const stakeLimits: StakedLimits = games[0].limits[currency] as StakedLimits;
        const entity = master.find({ path: ":TLE1:" });
        const service = getEntityGameService(entity);

        const newCurrencyLimits = {
            stakeMax: stakeLimits.stakeAll[stakeLimits.stakeAll.length - 2],
            stakeMin: stakeLimits.stakeAll[1]
        };

        await service.bulkUpdateLimits({
            codes: gameCodes,
            limitFilters: {
                [currency]: newCurrencyLimits
            }
        });

        for (const game of games) {
            const entityGame: EntityGame = await GameService.findOneEntityGame(entity, game.code);
            expect(entityGame).deep.equal({
                ...entityGame,
                limitFilters: {
                    [currency]: newCurrencyLimits
                }
            });
        }
    });

    it("Bulk update game limitFilters for operator and childs with deep merge", async () => {
        const provider = await createRandomGameProvider();
        const games: GameInfo[] = [];
        const gamesCount = 5;
        const currency = "USD";
        for (let i = 0; i < gamesCount; i++) {
            const game: GameInfo = await registerRandomGame(provider.code);
            await GameService.addGameToEntity(master, { path: ":TLE1:" }, game.code,
                false, { status: "normal", limitFilters: { [currency]: { winMax: 10 } } });
            await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, game.code,
                false, { status: "normal", limitFilters: { [currency]: { winMax: 20 } } });
            games.push(game);
        }

        const gameCodes: string[] = games.map(game => game.code);

        const stakeLimits: StakedLimits = games[0].limits[currency] as StakedLimits;
        const entity = master.find({ path: ":TLE1:" });
        const child = master.find({ path: ":TLE1:ENT1:" });

        const service = getEntityGameService(entity);

        const newCurrencyLimits = {
            stakeMax: stakeLimits.stakeAll[stakeLimits.stakeAll.length - 2],
            stakeMin: stakeLimits.stakeAll[1]
        };

        await service.bulkUpdateLimits({
            codes: gameCodes,
            limitFilters: {
                [currency]: newCurrencyLimits
            },
            merge: true
        });

        for (const game of games) {
            const entityGame: EntityGame = await GameService.findOneEntityGame(entity, game.code);
            expect(entityGame).deep.equal({
                ...entityGame,
                limitFilters: {
                    [currency]: { ...newCurrencyLimits, winMax: 10 }
                }
            });

            const childEntityGame: EntityGame = await GameService.findOneEntityGame(child, game.code);
            expect(childEntityGame).deep.equal({
                ...childEntityGame,
                limitFilters: {
                    [currency]: { ...newCurrencyLimits, winMax: 20 }
                }
            });
        }
    });
    it("Fail to bulk update game limitFilters for operator", async () => {
        const provider = await createRandomGameProvider();
        const game: GameInfo = await registerRandomGame(provider.code);
        await GameService.addGameToEntity(master, { path: ":TLE1:" }, game.code,
            false, { status: "normal" });

        const currency = "invalid";
        const stakeLimits: StakedLimits = game.limits.USD as StakedLimits;
        const entity = master.find({ path: ":TLE1:" });
        const service = getEntityGameService(entity);

        const newCurrencyLimits = {
            stakeMax: stakeLimits.stakeAll[stakeLimits.stakeAll.length - 2],
            stakeMin: stakeLimits.stakeAll[1]
        };

        await service.bulkUpdateLimits({
            codes: [ game.code ],
            limitFilters: {
                [currency]: newCurrencyLimits
            }
        })
            .catch((err) => {
                expect(err.message).eq(`Limits for game ${game.code} are incorrect. ` +
                    `Currency ${currency} not found`);
            });

    });

    it("Get inherited limitFilters for operator", async () => {
        const provider = await createRandomGameProvider();
        const game: GameInfo = await registerRandomGame(provider.code);

        await GameService.addGameToEntity(master, { path: ":TLE1:" }, game.code,
            false, { status: "normal" });

        const currency = Object.keys(game.limits)[getRandomNumber(0, Object.keys(game.limits).length - 1)];
        const stakeLimits: StakedLimits = game.limits[currency] as StakedLimits;
        const parentEntity = master.find({ path: ":TLE1:ENT1:" });
        const childEntity = master.find({ path: ":TLE1:ENT1:" });

        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, game.code,
            false, { status: "normal" });

        const newCurrencyLimits = {
            stakeMax: stakeLimits.stakeAll[stakeLimits.stakeAll.length - 2],
            stakeMin: stakeLimits.stakeAll[1]
        };

        const service = getEntityGameService(parentEntity);
        await service.updateLimits(game.code, {
            [currency]: newCurrencyLimits
        });

        const entityGame: EntityGame = await GameService.findOneEntityGame(childEntity, game.code);
        expect(entityGame).deep.equal({
            ...entityGame,
            limitFilters: {
                [currency]: newCurrencyLimits
            }
        });
    });

    it("Get inherited title for operator", async () => {
        const provider = await createRandomGameProvider();
        const game: GameInfo = await registerRandomGame(provider.code);

        const childEntity = master.find({ path: ":TLE1:" });

        await GameService.addGameToEntity(master, { path: ":TLE1:" }, game.code,
            false, { title: "qwerty" });

        const entityGame: EntityGame = await GameService.findOneEntityGame(childEntity, game.code);
        expect(entityGame.title).equal("qwerty");
    });

    describe("Validates game", () => {
        let provider;
        let providerId;
        before(async () => {
            provider = await createRandomGameProvider();
            providerId = publicId.instance.decode(provider.id);
        });

        it("Data Incorrect", async () => {
            await GameProviderService.register({
                type: "slot",
                title: "Game 1",
                url: "invalid",
                gameCode: "!@#$%%^",
                providerGameCode: "!@#$%%^",
                defaultInfo: null,
                info: null,
                limits: limitsData,
                providerId: providerId
            }).should.eventually.rejectedWith(ValidationError);
        });

        it("Limits Incorrect - bad currency", async () => {
            await GameProviderService.register({
                type: "slot",
                title: "Game 1",
                url: "invalid",
                gameCode: "!@#$%%^",
                providerGameCode: "!@#$%%^",
                defaultInfo: null,
                info: null,
                providerId: providerId,
                limits: {
                    "WRONG": {
                        maxTotalStake: 1000,
                        stakeAll: [ 0.1, 0.5, 1, 2, 3, 5 ],
                        stakeDef: 1,
                        stakeMax: 10,
                        stakeMin: 1,
                        winMax: 200
                    }
                }
            }).should.eventually.rejectedWith(Errors.CurrencyNotFoundError);
        });

        it("Limits Incorrect - without stakeMax", async () => {
            const data = {
                type: "slot",
                title: "Game 1",
                url: "invalid",
                gameCode: "!@#$%%^",
                providerGameCode: "!@#$%%^",
                defaultInfo: null,
                info: null,
                providerId: providerId,
                limits: {
                    "MYR": {
                        maxTotalStake: 1000,
                        stakeAll: [ 0.1, 0.5, 1, 2, 3, 5 ],
                        stakeMax: 0.01,
                        stakeDef: 1,
                        stakeMin: 1,
                        winMax: 200
                    }
                }
            };
            await GameProviderService.register(data).should.eventually.rejectedWith(Errors.LimitsIncorrect);
            try {
                await GameProviderService.register(data);
            } catch (err) {
                expect(err.message).equal("Limits for game type slot and currency MYR incorrect." +
                    "Please check stakeMax");
            }
        });

        it("Limits Incorrect - is empty", async () => {
            await GameProviderService.register({
                type: "slot",
                title: "Game 1",
                url: "http://www.test.com/",
                gameCode: "g1",
                providerGameCode: "g1",
                defaultInfo: {
                    name: "g1",
                    description: "g1"
                },
                info: {
                    "EN": {
                        name: "g1",
                        description: "g1"
                    }
                },
                limits: {
                    "MYR": {
                        maxTotalStake: 1000,
                        stakeAll: [],
                        stakeMax: 5,
                        stakeDef: 1,
                        stakeMin: 1,
                        winMax: 200
                    }
                },
                providerId: providerId
            }).should.eventually.rejectedWith(Errors.LimitsIncorrect);
        });

        it("Limits Incorrect - stakeAll is bad", async () => {
            await GameProviderService.register({
                type: "slot",
                title: "Game 1",
                url: "http://www.test.com/",
                gameCode: "g1",
                providerGameCode: "g1",
                defaultInfo: {
                    name: "g1",
                    description: "g1"
                },
                info: {
                    "EN": {
                        name: "g1",
                        description: "g1"
                    }
                },
                limits: {
                    "MYR": {
                        maxTotalStake: 1000,
                        stakeAll: [ null ],
                        stakeMax: 5,
                        stakeDef: 1,
                        stakeMin: 1,
                        winMax: 200
                    }
                },
                providerId: providerId
            }).should.eventually.rejectedWith(Errors.LimitsIncorrect);
        });

        it("Limits Incorrect - empty limits", async () => {
            await GameProviderService.register({
                type: "slot",
                title: "Game 1",
                url: "http://www.test.com/",
                gameCode: "g1",
                providerGameCode: "g1",
                defaultInfo: {
                    name: "g1",
                    description: "g1"
                },
                info: {
                    "EN": {
                        name: "g1",
                        description: "g1"
                    }
                },
                limits: {},
                providerId: providerId
            }).should.eventually.rejectedWith(Errors.CurrencyNotFoundError);
        });

        it("Limits Incorrect - limits is null", async () => {
            await GameProviderService.register({
                type: "slot",
                title: "Game 1",
                url: "http://www.test.com/",
                gameCode: "!@#$%%^",
                providerGameCode: "!@#$%%^",
                defaultInfo: null,
                info: null,
                limits: null,
                providerId: providerId
            }).should.eventually.rejectedWith(Errors.CurrencyNotFoundError);
        });

        it("Limits Incorrect - coinsRate is negative", async () => {
            await GameProviderService.register({
                type: "action",
                title: "Game 1",
                url: "http://www.test.com/",
                gameCode: "!@#$%%^",
                providerGameCode: "!@#$%%^",
                defaultInfo: null,
                info: null,
                limits: {
                    "USD": {
                        "coinsRate": -123
                    }
                },
                providerId: providerId
            }).should.eventually.rejectedWith(Errors.LimitsIncorrect);
        });
    });

    it("Adding a game to an entity - game already exists - negative", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);

        await GameService.addGameToEntity(
            master,
            { path: ":TLE1:" },
            game.code, false, { status: "normal" });
        const tre1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        await GameService.addGameToEntity(tre1,
            { path: ":TLE1:ENT1:" }, game.code, false, { status: "normal" });
        await GameService.addGameToEntity(tre1,
            { path: ":TLE1:ENT1:" }, game.code, false, { status: "normal" })
            .should.eventually.rejectedWith(Errors.GameAlreadyExistsError);
    });

    it("Adding a game to suspended entity - success", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);

        await GameService.addGameToEntity(master,
            { path: ":TLE1:" }, game.code, false, { status: "normal" });
        const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        const entityName = "ENT_" + game.code;
        await getEntityFactory(tle1).createEntity({
            name: entityName,
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM"
        });
        await EntityService.suspend(master, { path: `:TLE1:${entityName}:` });
        const entity = await EntityService.findOne({ path: `:TLE1:${entityName}:` });
        await GameService.addGameToEntity(entity,
            { path: `:TLE1:${entityName}:` }, game.code, false, { status: "normal" })
            .should.eventually.deep.equal({ code: game.code, status: "normal" });
    });

    it("Removing a game to an entity which does not exists - negative", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);

        await GameService.addGameToEntity(master,
            { path: ":TLE1:" }, game.code, false, { status: "normal" });
        const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        await GameService.addGameToEntity(tle1,
            { path: ":TLE1:ENT25:" }, game.code, false, { status: "normal" })
            .should.eventually.rejectedWith(Errors.EntityCouldNotBeFound);
    });

    it("Removing a game from an entity with force", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);

        await GameService.addGameToEntity(master,
            { path: ":TLE1:" }, game.code, false, { status: "normal" });
        const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        await GameService.addGameToEntity(tle1,
            { path: ":TLE1:ENT1:" }, game.code, false, { status: "normal" });

        const ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });

        const brandName = "BRAND_" + game.code;
        const data: CreateData = {
            name: brandName,
            description: "brand description",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM",
            webSiteUrl: "http://hello-4.com"
        };
        await getEntityFactory(ent1).createBrand(data);
        const ent1WithBrand: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
        await GameService.addGameToEntity(ent1WithBrand,
            { path: `:TLE1:ENT1:${brandName}:` }, game.code, false, { status: "normal" });
        const brand = await EntityService.findOne({ path: `:TLE1:ENT1:${brandName}:` });
        const brandGame = await getEntityGame().findOne({ where: { entityId: brand.id } });
        await getSegmentCRUDService().create({
            entityGameId: brandGame.get("id"),
            segment: { currency: [ "USD" ], vipLevel: [ "5" ] }
        });

        const gameInfo = await getEntityGameService(ent1WithBrand).delete(game.code, false, true);
        expect(gameInfo).to.be.deep.equal({
            code: game.code,
            status: "normal"
        });

        await GameService.findOneEntityGame(ent1WithBrand, game.code)
            .should.eventually.rejectedWith(Errors.EntityGameNotFound);
    });

    it("Removing a game from an entity with force when game is in group with limits", async () => {
        const provider = await createRandomGameProvider();
        const gameInfo = await registerRandomGame(provider.code);
        const game = await GameService.findOne({
            code: gameInfo.code
        });

        await GameService.addGameToEntity(master, { path: ":TLE1:" }, gameInfo.code, false, { status: "normal" });

        const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        await GameService.addGameToEntity(tle1, { path: ":TLE1:ENT1:" }, gameInfo.code, false, { status: "normal" });

        const ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });

        const brandName = "BRAND_" + gameInfo.code;
        const data: CreateData = {
            name: brandName,
            description: "brand description",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM",
            webSiteUrl: "http://hello-5.com"
        };
        const brand = await getEntityFactory(ent1).createBrand(data);
        await GameService.addGameToEntity(ent1,
            { path: `:TLE1:ENT1:${brandName}:` },
            gameInfo.code,
            false,
            { status: "normal" });
        const brandEntityGame = await GameService.findOneEntityGame(brand, gameInfo.code);

        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, { brandId: brand.id });
        await factory.create(FACTORY.GAME_GROUP_LIMITS, {}, {
            entityGameId: brandEntityGame.id,
            gameId: game.id,
            gamegroupId: gameGroup.id,
            limits: { "USD": {} }
        });

        await getEntityGameService(ent1).delete(gameInfo.code, false)
            .should.eventually.rejectedWith(Errors.FailedToDeleteEntityGameNeedForce);

        await getEntityGameService(brand).delete(gameInfo.code, false)
            .should.eventually.rejectedWith(Errors.ValidationError);

        await getEntityGameService(ent1).delete(game.code, false, true);

        await GameService.findOneEntityGame(brand, gameInfo.code)
            .should.eventually.rejectedWith(Errors.EntityGameNotFound);
    });

    it("Removing a game from an entity with force when game is in game limits config", async () => {
        const provider = await createRandomGameProvider();
        const gameInfo = await registerRandomGame(provider.code);
        const game = await GameService.findOne({
            code: gameInfo.code
        });

        await GameService.addGameToEntity(master, { path: ":TLE1:" }, gameInfo.code, false, { status: "normal" });

        const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        await GameService.addGameToEntity(tle1, { path: ":TLE1:ENT1:" }, gameInfo.code, false, { status: "normal" });

        const ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });

        const brandName = "BRAND_" + gameInfo.code;
        const data: CreateData = {
            name: brandName,
            description: "brand description",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM",
            webSiteUrl: "http://hello-6.com"
        };
        const brand = await getEntityFactory(ent1).createBrand(data);
        await GameService.addGameToEntity(ent1,
            { path: `:TLE1:ENT1:${brandName}:` },
            gameInfo.code,
            false,
            { status: "normal" });
        const brandEntityGame = await GameService.findOneEntityGame(brand, gameInfo.code);

        const definitionWithSets = await getSchemaDefinitionService()
            .create({ ...getDefinitionWithSets(), name: "unique name" } as any);
        const [ high, low ] = await factory.createMany(FACTORY.LIMIT_LEVEL, 2);
        await factory.create(FACTORY.GAME_LIMITS_CONFIGURATION, {}, {
            schemaDefinitionId: definitionWithSets.id,
            entityId: brand.id,
            entityGameId: brandEntityGame.id,
            schemaConfigurationId: null,
            gameCode: gameInfo.code,
            // entityGameBuildOptions: { gameCode: "sw_al", entityId: brand.id },
            gameLimits: {
                CNY: getConfigWithLevels(publicId.instance.encode(high.id),
                    publicId.instance.encode(low.id)).CNY
            },
            segment: {
                priority: 2,
                segment: {
                    clientPlatform: [ "web" ]
                }
            },
            segmentBuildOptions: {}
        });

        await getEntityGameService(ent1).delete(gameInfo.code, false)
            .should.eventually.rejectedWith(Errors.FailedToDeleteEntityGameNeedForce);
        await getEntityGameService(brand).delete(gameInfo.code, false)
            .should.eventually.rejectedWith(Errors.ValidationError);

        await getEntityGameService(brand).delete(game.code, false, true);

        await GameService.findOneEntityGame(brand, gameInfo.code)
            .should.eventually.rejectedWith(Errors.EntityGameNotFound);
    });

    it("Removing a game from an entity - need force - negative", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);

        await GameService.addGameToEntity(master,
            { path: ":TLE1:" }, game.code, false, { status: "normal" });
        const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        await GameService.addGameToEntity(tle1,
            { path: ":TLE1:ENT1:" }, game.code, false, { status: "normal" });
        await getEntityGameService(tle1).delete(game.code, false)
            .should.eventually.rejectedWith(Errors.FailedToDeleteEntityGameNeedForce);
    });

    it("Removing a game from an entity", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);
        const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        await GameService.addGameToEntity(master,
            { path: ":TLE1:" }, game.code, false, { status: "normal" });

        const gameInfo = await getEntityGameService(tle1).delete(game.code, false, true);
        expect(gameInfo).to.be.deep.equal({ code: game.code, status: "normal" });

        await GameService.findOneEntityGame(tle1, game.code)
            .should.eventually.rejectedWith(Errors.EntityGameNotFound);
    });

    it("Removing a suspended game from an entity", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);
        const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        await GameService.addGameToEntity(master,
            { path: ":TLE1:" }, game.code, false, { status: "normal" });

        const service = getEntityGameService(tle1);
        await service.suspend(game.code, false);

        const gameInfo = await service.delete(game.code, false, true);
        expect(gameInfo).to.be.deep.equal({ code: game.code, status: "suspended" });

        await GameService.findOneEntityGame(tle1, game.code)
            .should.eventually.rejectedWith(Errors.EntityGameNotFound);
    });

    it("Forbidden to restore entity game if parent game is still suspended and allowed with flag", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);
        const tle1ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
        await GameService.addGameToEntity(master,
            { path: ":TLE1:" }, game.code, false, { status: "suspended" });
        await GameService.addGameToEntity(master,
            { path: ":TLE1:ENT1:" }, game.code, false, { status: "suspended" });

        const tle1ent1Game = await GameService.findOneEntityGame(tle1ent1, game.code);
        expect(tle1ent1Game.status).to.eq("suspended");

        const service = getEntityGameService(tle1ent1);
        await service.restore(game.code, false).should.eventually.rejectedWith(Errors.ParentSuspendedError);

        const gameInfo = await service.restore(game.code, false, true);
        expect(gameInfo).to.be.deep.equal({ code: game.code, status: "normal" });
    });

    it("Removing a game from an entity - game not found - negative", async () => {
        const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        await getEntityGameService(tle1).delete("WHLSTestProviderGAME0015", false, true)
            .should.eventually.rejectedWith(Errors.EntityGameNotFound);
    });

    // TODO: SWS-813 fix test
    it("Get game by entity", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);

        await GameService.addGameToEntity(master, { path: ":TLE1:" }, game.code, false, { status: "normal" });
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, game.code, false, { status: "normal" });
        const ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });

        const ent1GameInfo = await GameService.getOneGame(ent1, game.code);
        delete ent1GameInfo.releaseDate;
        delete ent1GameInfo.rtpInfo;
        ent1GameInfo.url = undefined;
        expect(ent1GameInfo).deep.equal(expectedGame(provider, game, []));
    });

    it("Create game with default status", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);

        await GameService.addGameToEntity(master, { path: ":TLE1:" }, game.code, false);
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, game.code, false);
        const ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });

        const gameInfo = await GameService.getOneGame(ent1, game.code);
        expect(gameInfo.status).equal("normal");
    });

    it("Get game with real limits for brand ", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code,
            GAME_TYPES.slot,
            { ...limitsData, CAD: { ...limitsData["USD"] } });

        const brand = await getEntityFactory(await EntityService.findOne({ path: ":TLE1:ENT1:" })).createBrand({
            name: "BRAND_NEW",
            description: "BRAND_NEW description",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM",
            webSiteUrl: "http://web-site.com"
        });

        await GameService.addGameToEntity(master,
            { path: ":TLE1:" }, game.code, false, { status: "normal" });
        await GameService.addGameToEntity(master,
            { path: ":TLE1:ENT1:" }, game.code, false, { status: "normal" });
        await GameService.addGamesToEntity(brand, { codes: [ game.code ] }, false);

        let entityGameInfo: EntityGameInfo = await GameService.getOneGame(brand,
            game.code,
            { addAggregatedFinalLimits: true }
        );
        expect(entityGameInfo.limits).to.be.deep.equal({ ...limitsData, CAD: { ...limitsData["USD"] } });

        entityGameInfo = await GameService.getOneGame(brand,
            game.code,
            { addAggregatedFinalLimits: true, currency: "USD" }
        );

        expect(entityGameInfo.limits).to.be.deep.equal(limitsData);
    });

    it("Get game by entity - game provider is suspended", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);

        await GameService.addGameToEntity(master,
            { path: ":TLE1:" }, game.code, false, { status: "normal" });
        await GameService.addGameToEntity(master,
            { path: ":TLE1:ENT1:" }, game.code, false, { status: "normal" });

        await GameProviderService.suspend(master, publicId.instance.decode(provider.id));
        const ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });

        await GameService.getOneGame(ent1, game.code).should.eventually.rejectedWith(Errors.GameNotFoundError);
    });

    it("Get game by master - game provider is suspended", async () => {
        const provider = await createRandomGameProvider();
        const game = await registerRandomGame(provider.code);

        await GameProviderService.suspend(master, publicId.instance.decode(provider.id));
        await GameService.getOneGame(master, game.code).should.eventually.rejectedWith(Errors.GameNotFoundError);
    });

    it("Get game by master - game not found - negative", async () => {
        await GameService.getOneGame(master, "WHLSTestProviderGAME0015")
            .should.eventually.rejectedWith(Errors.GameNotFoundError);
    });

    it("Get game by entity - game not found - negative", async () => {
        const ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
        await GameService.getOneGame(ent1, "WHLSTestProviderGAME0015")
            .should.eventually.rejectedWith(Errors.GameNotFoundError);
    });

    it("Get game list by entity", async () => {
        const p1 = await createRandomGameProvider();
        const p2 = await createRandomGameProvider();
        const game2 = await registerRandomGame(p2.code);
        const game1 = await registerRandomGame(p1.code);

        await GameService.addGameToEntity(master, { path: ":TLE1:" }, game1.code, false);
        await GameService.addGameToEntity(master, { path: ":TLE1:" }, game2.code, false);
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, game1.code, false, { status: "normal" });
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, game2.code, false, { status: "normal" });
        const ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });

        let ent1GameInfoList = await GameService.getAllGames(ent1);
        ent1GameInfoList.sort((a, b) => b.code.localeCompare(a.code));
        ent1GameInfoList = ent1GameInfoList.map(game => {
            delete game.releaseDate;
            delete game.rtpInfo;
            return game;
        });
        expect(ent1GameInfoList[0]).deep.equal(expectedGame(p1, game1, []));
        expect(ent1GameInfoList[1]).deep.equal(expectedGame(p2, game2, []));
    });

    it("Get game list by entity with limits filtered by currency", async () => {
        const p1 = await createRandomGameProvider();
        const p2 = await createRandomGameProvider();
        const game2 = await registerRandomGame(p2.code);
        const game1 = await registerRandomGame(p1.code);

        await GameService.addGameToEntity(master, { path: ":TLE1:" }, game1.code, false);
        await GameService.addGameToEntity(master, { path: ":TLE1:" }, game2.code, false);
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, game1.code, false, { status: "normal" });
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, game2.code, false, { status: "normal" });
        const ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });

        let ent1GameInfoList = await GameService.getAllGames(ent1, undefined, { currency: "USD" });
        ent1GameInfoList.sort((a, b) => b.code.localeCompare(a.code));
        ent1GameInfoList = ent1GameInfoList.map(game => {
            delete game.releaseDate;
            delete game.rtpInfo;
            return game;
        });
        expect(ent1GameInfoList[0]).deep.equal(expectedGame(p1, game1, []));
        expect(ent1GameInfoList[1]).deep.equal(expectedGame(p2, game2, []));
    });

    it("Get game list by entity with no limits that match currency", async () => {
        const p1 = await createRandomGameProvider();
        const p2 = await createRandomGameProvider();
        const game2 = await registerRandomGame(p2.code);
        const game1 = await registerRandomGame(p1.code);

        await GameService.addGameToEntity(master, { path: ":TLE1:" }, game1.code, false);
        await GameService.addGameToEntity(master, { path: ":TLE1:" }, game2.code, false);
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, game1.code, false, { status: "normal" });
        await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, game2.code, false, { status: "normal" });
        const ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });

        let ent1GameInfoList = await GameService.getAllGames(ent1, undefined, { currency: "EUR" });
        ent1GameInfoList.sort((a, b) => b.code.localeCompare(a.code));
        ent1GameInfoList = ent1GameInfoList.map(game => {
            delete game.releaseDate;
            return game;
        });
        expect(ent1GameInfoList[0].limits).to.not.exist;
        expect(ent1GameInfoList[1].limits).to.not.exist;
    });

    it("Get game list by entity - game provider is suspended", async () => {
        const p1 = await createRandomGameProvider();
        const p2 = await createRandomGameProvider();
        const game1 = await registerRandomGame(p1.code);
        const game2 = await registerRandomGame(p2.code);

        await GameService.addGameToEntity(master,
            { path: ":TLE1:" }, game1.code, false);
        await GameService.addGameToEntity(master,
            { path: ":TLE1:" }, game2.code, false);
        await GameService.addGameToEntity(master,
            { path: ":TLE1:ENT1:" }, game1.code, false, { status: "normal" });
        await GameService.addGameToEntity(master,
            { path: ":TLE1:ENT1:" }, game2.code, false, { status: "normal" });
        const ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });

        await GameProviderService.suspend(master, publicId.instance.decode(p1.id));

        const ent1GameInfoList = await GameService.getAllGames(ent1, undefined,
            { providerId: publicId.instance.decode(p2.id) });
        delete ent1GameInfoList[0].releaseDate;
        delete ent1GameInfoList[0].rtpInfo;

        const expectedGameInfoList = [ expectedGame(p2, game2, []) ] as any;
        expectedGameInfoList.PAGING_INFO = createExpectedPagingInfo(1);
        expect(ent1GameInfoList).deep.equal(expectedGameInfoList);

        const emptyGameInfoList = await GameService.getAllGames(ent1, undefined,
            { providerId: publicId.instance.decode(p1.id) });
        expect(emptyGameInfoList.length).equal(0);
    });

    it("Get games list for suspended entity - negative", async () => {
        const p1 = await createRandomGameProvider();
        const p2 = await createRandomGameProvider();
        await registerRandomGame(p1.code);
        await registerRandomGame(p2.code);
        const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
        const entityName = "ENT_SUSPENDED_LIST";
        await getEntityFactory(tle1).createEntity({
            name: entityName,
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            jurisdictionCode: "COM"
        });
        await EntityService.suspend(master, { path: `:TLE1:${entityName}:` });
        const suspendedEntity = await EntityService.findOne({ path: `:TLE1:${entityName}:` });
        await GameService.getAllGames(suspendedEntity, { path: `:TLE1:${entityName}:` })
            .should.eventually.rejectedWith(Errors.ParentSuspendedError);
    });

    it("Get games list for an entity which does not exists - negative", async () => {
        const ent1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
        await GameService.getAllGames(ent1, { path: ":TLE1:ENT15:" })
            .should.eventually.rejectedWith(Errors.EntityCouldNotBeFound);
    });

    describe("Get game url for player", () => {

        let gameInfo;
        let brand: BrandEntity;
        let brandSettingsService: EntitySettingsService;
        let player;
        const providerUser = "casino";
        const providerCode = "CA";
        const providerTitle = "Provider";
        const providerSecret = "providerSecret1";

        before(async () => {
            await createGameProvider(providerUser, providerCode, providerTitle, providerSecret);
            const provider = await GameProviderService.findOne({ user: providerUser });
            gameInfo = await GameProviderService.register({
                type: "slot",
                title: "Game 1",
                url: "http://www.test.com/g1?token={startGameToken}",
                gameCode: "g1",
                providerGameCode: "g1",
                defaultInfo: {
                    name: "g1",
                    description: "g1"
                },
                info: {
                    "EN": {
                        name: "g1",
                        description: "g1"
                    }
                },
                limits: limitsData,
                providerId: provider.id
            });
            const code = gameInfo.providerGameCode;
            await GameService.addGameToEntity(master,
                { path: ":TLE1:" }, code, false);
            await GameService.addGameToEntity(master,
                { path: ":TLE1:ENT1:" }, code, false);

            const tle1 = await EntityService.findOne({ key: complexStructure.tle1.key });

            const tle1CountryService = new EntityCountryService(tle1);
            await tle1CountryService.add([ "US", "CN" ]);

            const tle1LanguageService = new EntityLanguageService(tle1);
            await tle1LanguageService.add("el");

            const ent1 = await EntityService.findOne({ key: complexStructure.tle1ent1.key });

            const ent1CountryService = new EntityCountryService(ent1);
            await ent1CountryService.add([ "US", "CN" ]);

            const ent1LanguageService = new EntityLanguageService(ent1);
            await ent1LanguageService.add("el");

            const data: CreateData = {
                name: "BRAND_FOR_GAME_URL",
                description: "brand description",
                defaultCurrency: "USD",
                defaultCountry: "US",
                defaultLanguage: "en",
                jurisdictionCode: "COM",
                webSiteUrl: "http://hello-7.com"
            };

            brand = await getEntityFactory(ent1).createBrand(data);
            brand.status = EntityStatus.NORMAL;
            brand.addCountry("CN");
            brand.addCountry("US");
            brand.addLanguage("el");
            await brand.save();

            brandSettingsService = new EntitySettingsService(brand);
            await brandSettingsService.patch({
                restrictions: {
                    countries: {
                        "CN": [ "CNY" ]
                    }
                }
            });

            const ent1WithBrand: BaseEntity = await EntityService.findOne(
                { key: complexStructure.tle1ent1.key });
            await GameService.addGameToEntity(ent1WithBrand,
                { path: ":TLE1:ENT1:BRAND_FOR_GAME_URL:" }, code, false, { status: "normal" });

            player = await getPlayerService().create(brand,
                {
                    code: "PL00001",
                    password: "ACAB!Area51",
                    firstName: "f_name",
                    lastName: "l_name",
                    email: "<EMAIL>",
                    country: "US",
                    currency: "USD",
                    language: "en"
                });
        });

        after(async () => {
            const settings = await brandSettingsService.get();
            settings.urlParams = null; // to ensure we don't affect other suits
            await brandSettingsService.patch(settings);
        });

        it("Gets game url for player", async () => {
            const gameCode = gameInfo.providerGameCode;
            const playMode = PlayMode.REAL;
            const response = await UrlManager.getPlayerGameURL({
                keyEntity: brand,
                playerCode: player.code,
                gameCode,
                playMode,
                ip: US_IP
            });

            const expectedUrl = "http://www.test.com/g1?token=" + response.token + "&language=en";
            expect(expectedUrl).equal(response.url);
            const playerTokenData = await TokenUtils.verifyStartGameToken(
                response.token);
            const expectedTokenData = {
                playerCode: player.code,
                gameCode: gameCode,
                brandId: brand.id
            };

            expect(playerTokenData).contain(expectedTokenData);
        });

        it("Gets game url with lang and currency", async () => {
            const provider = await GameProviderService.findOne({ user: providerUser });
            const game = await GameProviderService.register({
                type: "slot",
                title: "Game 2",
                url: "http://www.test.com/g1?token={startGameToken}&lang={lang}&currency={currency}",
                gameCode: "g2",
                providerGameCode: "g2",
                defaultInfo: {
                    name: "g2",
                    description: "g2"
                },
                info: {
                    "EN": {
                        name: "g2",
                        description: "g2"
                    }
                },
                limits: limitsData,
                providerId: provider.id
            });
            const code = game.providerGameCode;
            await GameService.addGameToEntity(master, { path: ":TLE1:" }, code, false);
            await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, code, false);
            const ent1WithBrand: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            await GameService.addGameToEntity(ent1WithBrand,
                { path: ":TLE1:ENT1:BRAND_FOR_GAME_URL:" }, code, false, { status: "normal" });
            const playMode = PlayMode.REAL;

            const response = await UrlManager.getPlayerGameURL({
                keyEntity: brand,
                playerCode: player.code,
                gameCode: code,
                playMode,
                ip: US_IP
            });

            const expectedUrl = "http://www.test.com/g1?token=" + response.token +
                "&lang=en&currency=USD&language=en";
            expect(expectedUrl).equal(response.url);
            const playerTokenData = await TokenUtils.verifyStartGameToken(
                response.token);
            const expectedTokenData = {
                playerCode: player.code,
                gameCode: code,
                brandId: brand.id
            };

            expect(playerTokenData).contain(expectedTokenData);
        });

        it("Gets game url for player under entity", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const playMode = PlayMode.REAL;
            const response = await UrlManager.getPlayerGameURL({
                keyEntity: entity,
                playerCode: player.code,
                gameCode: gameInfo.code,
                playMode,
                ip: US_IP,
                options: { path: ":TLE1:ENT1:BRAND_FOR_GAME_URL:" }
            });

            const expectedUrl = "http://www.test.com/g1?token=" + response.token + "&language=en";
            expect(expectedUrl).equal(response.url);
            const playerTokenData = await TokenUtils.verifyStartGameToken(
                response.token);
            const expectedTokenData = {
                playerCode: player.code,
                gameCode: gameInfo.code,
                brandId: brand.id
            };

            expect(playerTokenData).contain(expectedTokenData);
        });

        it("Gets game url for player without playmode under entity", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const playMode = undefined;
            const response = await UrlManager.getPlayerGameURL({
                keyEntity: entity,
                playerCode: player.code,
                gameCode: gameInfo.code,
                playMode,
                ip: US_IP,
                options: { path: ":TLE1:ENT1:BRAND_FOR_GAME_URL:" }
            });

            const expectedUrl = "http://www.test.com/g1?token=" + response.token + "&language=en";
            expect(expectedUrl).equal(response.url);
            const playerTokenData = await TokenUtils.verifyStartGameToken(
                response.token);
            const expectedTokenData = {
                playerCode: player.code,
                gameCode: gameInfo.code,
                brandId: brand.id,
                playmode: PlayMode.REAL
            };

            expect(playerTokenData).contain(expectedTokenData);
        });

        it("Fails to get game url for player under entity which does not exists", async () => {
            const entity: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            const playMode = PlayMode.REAL;
            await UrlManager.getPlayerGameURL({
                keyEntity: entity,
                playerCode: player.code,
                gameCode: gameInfo.code,
                playMode,
                ip: US_IP,
                options: { path: ":UNKNOWN:" }
            }).should.eventually.rejectedWith(Errors.EntityCouldNotBeFound);
        });

        it("Fails to get game url for player for suspended entity", async () => {
            const playMode = PlayMode.REAL;
            const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
            const brandName = "BRAND_SUSPENDED";
            const brandEntity = await getEntityFactory(tle1).createBrand({
                name: brandName,
                defaultCurrency: "USD",
                defaultCountry: "US",
                defaultLanguage: "en",
                jurisdictionCode: "COM",
                webSiteUrl: "http://hello-9.com"
            });
            await GameService.addGameToEntity(tle1,
                { path: `:TLE1:${brandName}:` }, gameInfo.code, false, { status: "normal" });
            const playerS = await getPlayerService().create(brandEntity, {
                code: "PL00001"
            });
            await EntityService.suspend(master, { path: `:TLE1:${brandName}:` });
            const suspended = await EntityService.findOne({ path: `:TLE1:${brandName}:` });
            await UrlManager.getPlayerGameURL({
                keyEntity: suspended,
                playerCode: playerS.code,
                gameCode: gameInfo.code,
                playMode
            }).should.eventually.rejectedWith(Errors.ParentSuspendedError);
        });

        it("Fails to get game url for suspended player", async () => {
            const playerS = await getPlayerService().create(brand, {
                code: "PL00001_SUSPENDED"
            });
            playerS.status = "suspended";
            await playerS.save();
            const playMode = PlayMode.REAL;
            await UrlManager.getPlayerGameURL({
                keyEntity: brand,
                playerCode: playerS.code,
                gameCode: gameInfo.code,
                playMode
            }).should.eventually.rejectedWith(Errors.PlayerIsSuspended);
        });

        it("Fails to get game url for player for player which does not exists", async () => {
            const playMode = PlayMode.REAL;
            await UrlManager.getPlayerGameURL({
                keyEntity: brand,
                playerCode: "unknown",
                gameCode: gameInfo.code,
                playMode
            }).should.eventually.rejectedWith(Errors.PlayerNotFoundError);
        });

        it("Fails to get game url with unknown ip", async () => {
            const playMode = PlayMode.REAL;
            await expect(UrlManager.getPlayerGameURL({
                keyEntity: brand,
                playerCode: player.code,
                gameCode: gameInfo.code,
                playMode,
                ip: "unknown"
            })).to.be.rejectedWith(Errors.UnknownIpAddress);
        });

        it("Fails to get game url for restricted country", async () => {
            const playMode = PlayMode.REAL;
            await expect(UrlManager.getPlayerGameURL({
                keyEntity: brand,
                playerCode: player.code,
                gameCode: gameInfo.code,
                playMode,
                ip: BY_IP
            })).to.be.rejectedWith(Errors.CountryIsRestricted);
        });

        it("Fails to get game url for restricted currency", async () => {
            const playMode = PlayMode.REAL;
            await expect(UrlManager.getPlayerGameURL({
                keyEntity: brand,
                playerCode: player.code,
                gameCode: gameInfo.code,
                playMode,
                ip: CN_IP
            })).to.be.rejectedWith(Errors.CurrencyIsRestricted);
        });

        it("Gets game url with custom url params", async () => {
            const settings = await brandSettingsService.get();
            settings.urlParams = { param1: "value1", param2: "value2" };
            await brandSettingsService.patch(settings);

            const provider = await GameProviderService.findOne({ user: providerUser });
            const game = await GameProviderService.register({
                type: "slot",
                title: "Game 3",
                url: "http://www.test.com/g1?token={startGameToken}&lang={lang}&currency={currency}",
                gameCode: "g3",
                providerGameCode: "g3",
                defaultInfo: {
                    name: "g3",
                    description: "g3"
                },
                info: {
                    "EN": {
                        name: "g3",
                        description: "g3"
                    }
                },
                limits: limitsData,
                providerId: provider.id
            });
            const code = game.providerGameCode;
            await GameService.addGameToEntity(master,
                { path: ":TLE1:" },
                code,
                false,
                { urlParams: { balance_ping: 15 } });
            await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, code, false);
            const ent1WithBrand: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            await GameService.addGameToEntity(ent1WithBrand,
                { path: ":TLE1:ENT1:BRAND_FOR_GAME_URL:" }, code, false, { status: "normal" });
            const playMode = PlayMode.REAL;

            const response = await UrlManager.getPlayerGameURL({
                keyEntity: brand,
                playerCode: player.code,
                gameCode: code,
                playMode,
                ip: US_IP,
                language: "el"
            });

            const expectedUrl = "http://www.test.com/g1?token=" + response.token +
                "&lang=el&currency=USD&param1=value1&param2=value2&balance_ping=15&language=el";
            expect(expectedUrl).equal(response.url);
            const playerTokenData = await TokenUtils.verifyStartGameToken(
                response.token);
            const expectedTokenData = {
                playerCode: player.code,
                gameCode: code,
                brandId: brand.id
            };

            expect(playerTokenData).contain(expectedTokenData);
        });

        it("Gets game url with custom url params only", async () => {
            const settings = await brandSettingsService.get();
            settings.urlParams = { param1: "value1", param2: "value2" };
            await brandSettingsService.patch(settings);

            const provider = await GameProviderService.findOne({ user: providerUser });
            const game = await GameProviderService.register({
                type: "slot",
                title: "Game 4",
                url: "http://www.test.com/g1",
                gameCode: "g4",
                providerGameCode: "g4",
                defaultInfo: {
                    name: "g4",
                    description: "g4"
                },
                info: {
                    "EN": {
                        name: "g4",
                        description: "g4"
                    }
                },
                limits: limitsData,
                providerId: provider.id
            });
            const code = game.providerGameCode;
            await GameService.addGameToEntity(master, { path: ":TLE1:" }, code, false);
            await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, code, false);
            const ent1WithBrand: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            await GameService.addGameToEntity(ent1WithBrand,
                { path: ":TLE1:ENT1:BRAND_FOR_GAME_URL:" }, code, false, { status: "normal" });
            const playMode = PlayMode.REAL;

            const response = await UrlManager.getPlayerGameURL({
                keyEntity: brand,
                playerCode: player.code,
                gameCode: code,
                playMode,
                ip: US_IP,
                language: "RU"
            });

            const expectedUrl = "http://www.test.com/g1?param1=value1&param2=value2&language=en";
            expect(expectedUrl).equal(response.url);
            const playerTokenData = await TokenUtils.verifyStartGameToken(
                response.token);
            const expectedTokenData = {
                playerCode: player.code,
                gameCode: code,
                brandId: brand.id
            };

            expect(playerTokenData).contain(expectedTokenData);
        });

        describe("Gets game url with entity domain", async () => {

            let entitySettings;
            let gameCode;

            before(async () => {
                entitySettings = await brandSettingsService.get(true);
                await brandSettingsService.patch({
                    restrictions: {
                        countries: {
                            "CN": [ "CNY", "USD" ]
                        }
                    },
                    urlParams: null,
                    dynamicRoutingEnabled: true
                });
                const dynamicDomain = await getDomainService().create({
                    domain: "gameserver.skywindgroup.com",
                    environment: "test"
                });
                await setDynamicDomain(brand, dynamicDomain as DynamicDomain);
                const staticDomain = await getDomainService(DOMAIN_TYPE.STATIC).create({
                    domain: "game5.skywindgroup.com"
                });
                await getEntityDomainService(DOMAIN_TYPE.STATIC).set(brand, staticDomain.id);

                // game with domain
                const provider = await GameProviderService.findOne({ user: providerUser });
                const game = await GameProviderService.register({
                    type: "slot",
                    title: "Game 5",
                    url: "http://{staticDomain}/game5?" +
                        "url=http://{dynamicDomain}&history_url=http://{dynamicDomain}/history",
                    gameCode: "g5",
                    providerGameCode: "g5",
                    defaultInfo: {
                        name: "g5",
                        description: "g5"
                    },
                    info: {
                        "EN": {
                            name: "g5",
                            description: "g5"
                        }
                    },
                    limits: limitsData,
                    providerId: provider.id
                });
                gameCode = game.providerGameCode;
                await GameService.addGameToEntity(master, { path: ":TLE1:" }, gameCode, false);
                await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, gameCode, false);
                const ent1WithBrand: BaseEntity = await EntityService.findOne(
                    { key: complexStructure.tle1ent1.key });
                await GameService.addGameToEntity(ent1WithBrand,
                    { path: ":TLE1:ENT1:BRAND_FOR_GAME_URL:" }, gameCode, false, { status: "normal" });

                await getDomainService().create({
                    domain: "super.mega.fast.skywindgroup.com",
                    environment: "test"
                });
                await new DomainRouting().store(1, [
                    {
                        route: {
                            countryCode: "CN",
                            regionCode: "GD",
                            environment: "test"
                        },
                        domain: "super.mega.fast.skywindgroup.com"
                    }
                ]);
            });

            after(async () => {
                await brandSettingsService.patch(entitySettings);
                await resetDynamicDomain(brand);
                await getEntityDomainService(DOMAIN_TYPE.STATIC).reset(brand);
            });

            it("Gets game url with entity domain", async () => {
                const playMode = PlayMode.REAL;

                const response = await UrlManager.getPlayerGameURL({
                    keyEntity: brand,
                    playerCode: player.code,
                    gameCode: gameCode,
                    playMode,
                    ip: US_IP,
                    language: "RU"
                });

                const expectedUrl = "http://game5.skywindgroup.com/game5?" +
                    "url=http%3A%2F%2Fgameserver.skywindgroup.com&" +
                    "history_url=http%3A%2F%2Fgameserver.skywindgroup.com%2Fhistory&" +
                    "language=en";
                expect(expectedUrl).equal(response.url);
                const playerTokenData = await TokenUtils.verifyStartGameToken(
                    response.token);
                const expectedTokenData = {
                    playerCode: player.code,
                    gameCode: gameCode,
                    brandId: brand.id
                };

                expect(playerTokenData).contain(expectedTokenData);
            });

            it.skip("Gets game url with dynamic domain routing", async () => {
                await brandSettingsService.patch({
                    restrictions: {
                        countries: {
                            "CN": [ "CNY", "USD" ]
                        }
                    },
                    urlParams: null,
                    dynamicRoutingEnabled: true
                });
                const playMode = PlayMode.REAL;

                const response = await UrlManager.getPlayerGameURL({
                    keyEntity: brand,
                    playerCode: player.code,
                    gameCode: gameCode,
                    playMode,
                    ip: CN_IP,
                    language: "RU"
                });

                const expectedUrl = "http://game5.skywindgroup.com/game5?" +
                    "url=http%3A%2F%2Fsuper.mega.fast.skywindgroup.com&" +
                    "history_url=http%3A%2F%2Fsuper.mega.fast.skywindgroup.com%2Fhistory&" +
                    "language=en";
                expect(expectedUrl).equal(response.url);
                const playerTokenData = await TokenUtils.verifyStartGameToken(
                    response.token);
                const expectedTokenData = {
                    playerCode: player.code,
                    gameCode: gameCode,
                    brandId: brand.id
                };

                expect(playerTokenData).contain(expectedTokenData);
            });

            it("Gets game url with history_url", async () => {
                const playMode = PlayMode.REAL;

                await GameProviderService.update("g5", {
                    url: "http://{staticDomain}/game5?" +
                        "url=http://{dynamicDomain}"
                });

                await brandSettingsService.patch({
                    urlParams: {
                        history_url: "http://gameserver.skywindgroup.com/history"
                    }
                });

                const response = await UrlManager.getPlayerGameURL({
                    keyEntity: brand,
                    playerCode: player.code,
                    gameCode: gameCode,
                    playMode,
                    ip: US_IP,
                    language: "RU"
                });

                const expectedUrl = "http://game5.skywindgroup.com/game5?" +
                    "url=http%3A%2F%2Fgameserver.skywindgroup.com&" +
                    "history_url=http%3A%2F%2Fgameserver.skywindgroup.com%2Fhistory&" +
                    "language=en";
                expect(expectedUrl).equal(response.url);
                const playerTokenData = await TokenUtils.verifyStartGameToken(
                    response.token);
                const expectedTokenData = {
                    playerCode: player.code,
                    gameCode: gameCode,
                    brandId: brand.id
                };

                expect(playerTokenData).contain(expectedTokenData);
            });

            it("Gets game url with history2_url", async () => {
                const playMode = PlayMode.REAL;

                await GameProviderService.update("g5", {
                    url: "http://{staticDomain}/game5?" +
                        "url=http://{dynamicDomain}",
                    historyRenderType: 3
                });

                await brandSettingsService.patch({
                    urlParams: {
                        history_url: "http://gameserver.skywindgroup.com/history",
                        history2_url: "http://gameserver.skywindgroup.com/history2"
                    }
                });

                const response = await UrlManager.getPlayerGameURL({
                    keyEntity: brand,
                    playerCode: player.code,
                    gameCode: gameCode,
                    playMode,
                    ip: US_IP,
                    language: "RU"
                });

                const expectedUrl = "http://game5.skywindgroup.com/game5?" +
                    "url=http%3A%2F%2Fgameserver.skywindgroup.com&" +
                    "history_url=http%3A%2F%2Fgameserver.skywindgroup.com%2Fhistory2&" +
                    "language=en";
                expect(expectedUrl).equal(response.url);
                const playerTokenData = await TokenUtils.verifyStartGameToken(
                    response.token);
                const expectedTokenData = {
                    playerCode: player.code,
                    gameCode: gameCode,
                    brandId: brand.id
                };

                expect(playerTokenData).contain(expectedTokenData);
            });
        });

        it("Fails to get game url without entity static domain", async () => {
            const provider = await GameProviderService.findOne({ user: providerUser });
            const game = await GameProviderService.register({
                type: "slot",
                title: "Game 6",
                url: "http://{staticDomain}/game6",
                gameCode: "g6",
                providerGameCode: "g6",
                defaultInfo: {
                    name: "g6",
                    description: "g6"
                },
                info: {
                    "EN": {
                        name: "g6",
                        description: "g6"
                    }
                },
                limits: limitsData,
                providerId: provider.id
            });
            const code = game.providerGameCode;
            await GameService.addGameToEntity(master, { path: ":TLE1:" }, code, false);
            await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, code, false);
            const ent1WithBrand: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            await GameService.addGameToEntity(ent1WithBrand,
                { path: ":TLE1:ENT1:BRAND_FOR_GAME_URL:" }, code, false, { status: "normal" });
            const playMode = PlayMode.REAL;

            await expect(UrlManager.getPlayerGameURL({
                keyEntity: brand,
                playerCode: player.code,
                gameCode: code,
                playMode,
                ip: US_IP,
                language: "RU"
            })).to.be.rejectedWith(Errors.StaticDomainNotDefined);
        });

        it("Fails to get game url without entity dynamic domain", async () => {
            const provider = await GameProviderService.findOne({ user: providerUser });
            const game = await GameProviderService.register({
                type: "slot",
                title: "Game 7",
                url: "http://game.skywindgroup.com/game7?url=http://{dynamicDomain}",
                gameCode: "g7",
                providerGameCode: "g7",
                defaultInfo: {
                    name: "g7",
                    description: "g7"
                },
                info: {
                    "EN": {
                        name: "g7",
                        description: "g7"
                    }
                },
                limits: limitsData,
                providerId: provider.id
            });
            const code = game.providerGameCode;
            await GameService.addGameToEntity(master, { path: ":TLE1:" }, code, false);
            await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, code, false);
            const ent1WithBrand: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            await GameService.addGameToEntity(ent1WithBrand,
                { path: ":TLE1:ENT1:BRAND_FOR_GAME_URL:" }, code, false, { status: "normal" });
            const playMode = PlayMode.REAL;

            await expect(UrlManager.getPlayerGameURL({
                keyEntity: brand,
                playerCode: player.code,
                gameCode: code,
                playMode,
                ip: US_IP,
                language: "RU"
            })).to.be.rejectedWith(Errors.DynamicDomainNotDefined);
        });

        it("Fails to get game url for restricted country by game", async () => {
            const provider = await GameProviderService.findOne({ user: providerUser });
            const game = await GameProviderService.register({
                type: "slot",
                title: "Game 70",
                url: "http://game.skywindgroup.com/game7",
                gameCode: "g70",
                providerGameCode: "g70",
                defaultInfo: {
                    name: "g70",
                    description: "g70"
                },
                info: {
                    "EN": {
                        name: "g70",
                        description: "g70"
                    }
                },
                limits: limitsData,
                providerId: provider.id,
                countries: [ "BY" ]
            });
            const code = game.providerGameCode;
            await GameService.addGameToEntity(master, { path: ":TLE1:" }, code, false);
            await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, code, false);
            const ent1WithBrand: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            await GameService.addGameToEntity(ent1WithBrand,
                { path: ":TLE1:ENT1:BRAND_FOR_GAME_URL:" }, code, false, { status: "normal" });
            const playMode = PlayMode.REAL;

            await expect(UrlManager.getPlayerGameURL({
                keyEntity: brand,
                playerCode: player.code,
                gameCode: code,
                playMode,
                ip: US_IP,
                language: "RU"
            })).to.be.rejectedWith(Errors.CountryIsRestricted);
        });

        it("Fails to get game url for restricted country by entity game [whitelist]", async () => {
            const provider = await GameProviderService.findOne({ user: providerUser });
            const game = await GameProviderService.register({
                type: "slot",
                title: "Game 70",
                url: "http://game.skywindgroup.com/game7",
                gameCode: "g70111",
                providerGameCode: "g70111",
                defaultInfo: {
                    name: "g70",
                    description: "g70"
                },
                info: {
                    "EN": {
                        name: "g70",
                        description: "g70"
                    }
                },
                limits: limitsData,
                providerId: provider.id
            });
            const code = game.providerGameCode;
            await GameService.addGameToEntity(master, { path: ":TLE1:" }, code, false);
            await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, code, false);
            const ent1WithBrand: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            await GameService.addGameToEntity(ent1WithBrand,
                { path: ":TLE1:ENT1:BRAND_FOR_GAME_URL:" },
                code,
                false,
                { status: "normal", settings: { countries: [ "US" ] } });
            const playMode = PlayMode.REAL;

            await expect(UrlManager.getPlayerGameURL({
                keyEntity: brand,
                playerCode: player.code,
                gameCode: code,
                playMode,
                ip: BY_IP,
                language: "RU"
            })).to.be.rejectedWith(Errors.CountryIsRestricted);
        });

        it("Fails to get game url for restricted country by entity game [blacklist]", async () => {
            const provider = await GameProviderService.findOne({ user: providerUser });
            const game = await GameProviderService.register({
                type: "slot",
                title: "Game 70",
                url: "http://game.skywindgroup.com/game7",
                gameCode: "g701112",
                providerGameCode: "g701112",
                defaultInfo: {
                    name: "g70",
                    description: "g70"
                },
                info: {
                    "EN": {
                        name: "g70",
                        description: "g70"
                    }
                },
                limits: limitsData,
                providerId: provider.id
            });
            const code = game.providerGameCode;
            await GameService.addGameToEntity(master, { path: ":TLE1:" }, code, false);
            await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, code, false);
            const ent1WithBrand: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            await GameService.addGameToEntity(ent1WithBrand,
                { path: ":TLE1:ENT1:BRAND_FOR_GAME_URL:" },
                code,
                false,
                { status: "normal", settings: { countries: [ "!US", "!BY" ] } });
            const playMode = PlayMode.REAL;

            await expect(UrlManager.getPlayerGameURL({
                keyEntity: brand,
                playerCode: player.code,
                gameCode: code,
                playMode,
                ip: BY_IP,
                language: "RU"
            })).to.be.rejectedWith(Errors.CountryIsRestricted);
        });

        it("Allow to get game url for allowed country by entity game [blacklist]", async () => {
            const provider = await GameProviderService.findOne({ user: providerUser });
            const game = await GameProviderService.register({
                type: "slot",
                title: "Game 70",
                url: "http://game.skywindgroup.com/game7",
                gameCode: "g701113",
                providerGameCode: "g701113",
                defaultInfo: {
                    name: "g70",
                    description: "g70"
                },
                info: {
                    "EN": {
                        name: "g70",
                        description: "g70"
                    }
                },
                limits: limitsData,
                providerId: provider.id
            });
            const code = game.providerGameCode;
            await GameService.addGameToEntity(master, { path: ":TLE1:" }, code, false);
            await GameService.addGameToEntity(master, { path: ":TLE1:ENT1:" }, code, false);
            const ent1WithBrand: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
            await GameService.addGameToEntity(ent1WithBrand,
                { path: ":TLE1:ENT1:BRAND_FOR_GAME_URL:" },
                code,
                false,
                { status: "normal", settings: { countries: [ "!BY" ] } });
            const playMode = PlayMode.REAL;

            expect(await UrlManager.getPlayerGameURL({
                keyEntity: brand,
                playerCode: player.code,
                gameCode: code,
                playMode,
                ip: US_IP,
                language: "RU"
            })).is.not.equal(undefined);
        });

        it("Gets game url for player with custom game splash", async () => {
            const settings = await brandSettingsService.get();
            settings.urlParams = { splash: "commonSplash" };
            settings.gameSplashes = { [gameInfo.providerGameCode]: "customSplash" };
            await brandSettingsService.update(settings);

            const gameCode = gameInfo.providerGameCode;
            const playMode = PlayMode.REAL;

            const response = await UrlManager.getPlayerGameURL({
                keyEntity: brand,
                playerCode: player.code,
                gameCode,
                playMode,
                ip: US_IP
            });

            const expectedUrl = "http://www.test.com/g1?token=" + response.token + "&splash=customSplash&language=en";
            expect(response.url).equal(expectedUrl);
            const playerTokenData = await TokenUtils.verifyStartGameToken(
                response.token);
            const expectedTokenData = {
                playerCode: player.code,
                gameCode: gameCode,
                brandId: brand.id
            };

            expect(playerTokenData).contain(expectedTokenData);
        });

        it("Fails to get game url for hidden game", async () => {
            const provider = await createRandomGameProvider();
            const game = await registerRandomGame(provider.code);

            await GameService.addGameToEntity(master,
                { path: ":TLE1:" }, game.code, false, { status: "normal" });
            await GameService.addGameToEntity(master,
                { path: ":TLE1:ENT1:" }, game.code, false, { status: "normal" });

            const ent1WithBrand: BaseEntity = await EntityService.findOne(
                { key: complexStructure.tle1ent1.key });
            await GameService.addGameToEntity(ent1WithBrand,
                { path: ":TLE1:ENT1:BRAND_FOR_GAME_URL:" }, game.code, false, { status: ENTITY_GAME_STATUS.HIDDEN });

            await expect(UrlManager.getPlayerGameURL({
                keyEntity: brand,
                playerCode: player.code,
                gameCode: game.code,
                playMode: PlayMode.REAL,
                ip: US_IP
            })).to.be.rejectedWith(Errors.GameSuspendedError);
        });
    });

    it("Get available operations", async () => {
        const operations = await getAvailableOperations({
            grantedPermissions: [ "keyentity:game" ]
        });

        expect(operations).deep.equal([
            {
                "description": "Gets games for entity, filtered by labels or category. "
                    + "Case sortBy == categoryList returns games from included list of category before others.",
                "id": "GET /games",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:view"
                ]
            },
            {
                "description": "Update rtp deduction for games",
                "id": "PUT /games/rtp-deduction",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:change-state"
                ]
            },
            {
                "description": undefined,
                "id": "GET /games/{gameCode}",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:view"
                ]
            },
            {
                "description": "Updates settings and status of game for key entity by code",
                "id": "PATCH /games/{gameCode}",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:change-state"
                ]
            },
            {
                "description": "Method returns base game information by code " +
                    "(title, type, labels, provider information and so on)",
                "id": "GET /games/{gameCode}/info",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:view"
                ]
            },
            {
                "description": "Search games for key entity by labels, codes, titles, providers or category " +
                    "and return array of base games' information",
                "id": "GET /games/info/search",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:view"
                ]
            },
            {
                "description": "Method returns game information with jackpots, " +
                    "game setting should contain array or string 'jackpotId'",
                "id": "GET /games/{gameCode}/jackpot",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:view"
                ]
            },
            {
                "description": "Method returns list of live table games info",
                "id": "GET /games/live/{provider}/live-info",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:view"
                ]
            },
            {
                "description": undefined,
                "id": "PUT /games/{gameCode}/suspended",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:change-state",
                    "keyentity:game:change-state:disabled"
                ]
            },
            {
                "description": undefined,
                "id": "DELETE /games/{gameCode}/suspended",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:change-state",
                    "keyentity:game:change-state:enabled"
                ]
            },
            {
                "description": undefined,
                "id": "POST /games/group/status",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:change-state"
                ]
            },
            {
                "description": undefined,
                "id": "POST /games/group/limits",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:limits"
                ]
            },
            {
                "description": undefined,
                "id": "GET /players/{playerCode}/games/{gameCode}",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:url"
                ]
            },
            {
                "description": undefined,
                "id": "GET /fun/games/{gameCode}",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:url"
                ]
            },
            {
                "description": "The method returns game history items for the key entity. Default output is limited to 20 entries per page. Max output is 100 entries per page. This method will return data for a limited time only (default = 3 months). This restriction does not work if you have 'report-without-limit' permission. Note: if round id is in query all other filters are ignored.",
                "id": "GET /history/game",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:history"
                ]
            },
            {
                "description": undefined,
                "id": "GET /history/game/{roundId}",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:history"
                ]
            },
            {
                "description": undefined,
                "id": "GET /history/events/{roundId}",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:history"
                ]
            },
            {
                "description": undefined,
                "id": "GET /history/game/{roundId}/sm-result",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:history"
                ]
            },
            {
                "description": undefined,
                "id": "GET /history/game/{roundId}/image",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:history"
                ]
            },
            {
                "description": undefined,
                "id": "GET /history/game/{roundId}/details/{spinNumber}",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:history"
                ]
            },
            {
                "description": undefined,
                "id": "GET /history/game/{roundId}/details/{spinNumber}/image",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:history"
                ]
            },
            {
                "description": "The method returns unfinished game rounds for the key entity.",
                "id": "GET /history/unfinished/game",
                "permissions": [
                    "keyentity:game",
                    "keyentity:game:unfinished"
                ]
            },
        ]);
    });

    describe("Rush game", () => {
        it("Create rush game", async () => {
            const p1 = await createRandomGameProvider();
            const table1 = "100";
            const table2 = "101";
            let features: any = {
                live: {
                    provider: "romania",
                    tableId: "TEST"
                }
            };
            const gameInfo = await registerRandomGame(
                p1.code,
                GAME_TYPES.live,
                undefined,
                table1,
                features
            );
            features = {
                live: {
                    type: "roulette",
                    tables: [
                        {
                            provider: "romania",
                            tableId: "TEST",
                            gameCode: gameInfo.code,
                            rushOrder: 0
                        }
                    ]
                }
            };
            await registerRandomGame(p1.code, GAME_TYPES.live, undefined, table2, features);

            const ent1GameInfoList = await GameService.getAllLiveGames(master, { "physicalTableId__in": "100, 101" });
            expect(ent1GameInfoList.length).equal(2);
        });

        it("validate rush game -> game code is not exist", async () => {
            const p1 = await createRandomGameProvider();
            const table2 = "2";
            const features = {
                live: {
                    type: "roulette",
                    tables: [
                        {
                            provider: "romania",
                            tableId: "TEST",
                            rushOrder: 1
                        }
                    ]
                }
            };
            await registerRandomGame(
                p1.code,
                GAME_TYPES.live,
                undefined,
                table2,
                features
            ).should.eventually.rejectedWith(Errors.ValidationError)
                .then(err => {
                    expect(err.message).to.be.equal(
                        "Validation error: live.tables[TEST].gameCode is empty"
                    );
                });
        });

        it("validate rush game -> game code is wrong", async () => {
            const p1 = await createRandomGameProvider();
            const table2 = "2";
            const features = {
                live: {
                    type: "roulette",
                    tables: [
                        {
                            provider: "romania",
                            tableId: "TEST",
                            gameCode: "test",
                            rushOrder: 1
                        }
                    ]
                }
            };
            await registerRandomGame(
                p1.code,
                GAME_TYPES.live,
                undefined,
                table2,
                features
            ).should.eventually.rejectedWith(Errors.ValidationError)
                .then(err => {
                    expect(err.message).to.be.equal(
                        "Validation error: live.tables[TEST].gameCode is not valid"
                    );
                });
        });

        it("validate rush game -> game code contains wrong tableId", async () => {
            const p1 = await createRandomGameProvider();
            const table1 = "1";
            const table2 = "2";
            let features: any = {
                live: {
                    provider: "romania",
                    tableId: "TEST"
                }
            };
            const gameInfo = await registerRandomGame(
                p1.code,
                GAME_TYPES.live,
                undefined,
                table1,
                features
            );
            features = {
                live: {
                    type: "roulette",
                    tables: [
                        {
                            provider: "romania",
                            tableId: "TEST1",
                            gameCode: gameInfo.code,
                            rushOrder: 0
                        }
                    ]
                }
            };
            await registerRandomGame(
                p1.code,
                GAME_TYPES.live,
                undefined,
                table2,
                features
            ).should.eventually.rejectedWith(Errors.ValidationError)
                .then(err => {
                    expect(err.message).to.be.equal(
                        "Validation error: live.tables[TEST1].gameCode is not valid"
                    );
                });
        });

        it("validate rush game -> rush order is not valid", async () => {
            const p1 = await createRandomGameProvider();
            const table1 = "1";
            const table2 = "2";
            let features: any = {
                live: {
                    provider: "romania",
                    tableId: "TEST"
                }
            };
            const gameInfo = await registerRandomGame(
                p1.code,
                GAME_TYPES.live,
                undefined,
                table1,
                features
            );
            features = {
                live: {
                    type: "roulette",
                    tables: [
                        {
                            provider: "romania",
                            tableId: "TEST",
                            gameCode: gameInfo.code
                        }
                    ]
                }
            };
            await registerRandomGame(
                p1.code,
                GAME_TYPES.live,
                undefined,
                table2,
                features
            ).should.eventually.rejectedWith(Errors.ValidationError)
                .then(err => {
                    expect(err.message).to.be.equal(
                        "Validation error: live.tables[TEST].rushOrder should be a number"
                    );
                });
        });
    });

    it("Get game list exclude inactive", async () => {
        const provider = await createRandomGameProvider();
        const game1 = await registerRandomGame(provider.code);
        const game2 = await registerRandomGame(provider.code);
        const game3 = await registerRandomGame(provider.code);
        const game4 = await registerRandomGame(provider.code);
        await getEntityGameService(master).update(game2.code, { status: ENTITY_GAME_STATUS.HIDDEN });
        await getEntityGameService(master).update(game4.code, { status: ENTITY_GAME_STATUS.SUSPENDED });

        const gameInfoList = await GameService.getAllGames(master, undefined, {
            excludeInactiveGames: true,
            providerId: publicId.instance.decode(provider.id)
        });
        expect(gameInfoList.length).to.equal(2);
        expect(gameInfoList.map(g => g.code)).deep.equal([ game1.code, game3.code ]);
    });

    describe("Game settings", () => {

        let provider;
        let game;
        let entity;

        before(async () => {
            provider = await createRandomGameProvider();
            game = await registerRandomGame(provider.code);
            game.features = {
                jackpotTypes: [ "jp_type1", "jp_type2" ]
            };
            await GameProviderService.update(game.code, game);

            await GameService.addGameToEntity(master,
                { path: ":TLE1:" }, game.code, false, { status: "normal" });
            await GameService.addGameToEntity(master,
                { path: ":TLE1:ENT1:" }, game.code, false, { status: "normal" });
            entity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
        });

        beforeEach(async () => {

            const topEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
            await (await getEntityGameService(master)).update(game.code, {
                settings: {
                    setting1: null,
                    jackpotId: null
                }
            });
            await (await getEntityGameService(topEntity)).update(game.code, {
                settings: {
                    setting1: null,
                    jackpotId: null
                }
            });
            await (getEntityGameService(entity)).update(game.code, {
                settings: {
                    setting1: null,
                    jackpotId: null
                }
            });
        });

        it("upsert games rtpDeduction not set settings", async () => {
            const provider1 = await createRandomGameProvider();
            const game1 = await registerRandomGame(provider1.code);
            const game2 = await registerRandomGame(provider1.code);

            const newRtpDeductionGame1 = 10.5;
            const newRtpDeductionGame2 = 20;

            const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
            await GameService.addGameToEntity(
                master,
                { path: ":TLE1:" },
                game1.code,
                false,
                { status: "normal" }
            );
            await GameService.addGameToEntity(
                master,
                { path: ":TLE1:" },
                game2.code,
                false,
                { status: "normal", settings: { key2: "val2" } }
            );

            try {
                await GameService.upsertGamesRtpDeduction(tle1, [
                    { gameCode: game1.code, newRtpDeduction: newRtpDeductionGame1 },
                    { gameCode: game2.code, newRtpDeduction: newRtpDeductionGame2 }
                ]);
            } catch (e) {
                assert.fail(e);
                return;
            }

            const result1Find = await GameService.findOneEntityGame(tle1, game1.code);
            expect(result1Find.settings.rtpConfigurator.rtpDeduction).to.be.equal(newRtpDeductionGame1);

            const result2Find = await GameService.findOneEntityGame(tle1, game2.code);
            expect(result2Find.settings.rtpConfigurator.rtpDeduction).to.be.equal(newRtpDeductionGame2);
        });

        it("upsert games rtpDeduction set settings", async () => {
            const provider1 = await createRandomGameProvider();
            const game1 = await registerRandomGame(provider1.code);
            const game2 = await registerRandomGame(provider1.code);

            const newRtpDeductionGame1 = 15;
            const newRtpDeductionGame2 = 25;

            const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
            await GameService.addGameToEntity(
                master,
                { path: ":TLE1:" },
                game1.code,
                false,
                { status: "normal" }
            );
            await GameService.addGameToEntity(
                master,
                { path: ":TLE1:" },
                game2.code,
                false,
                { status: "normal", settings: { rtpConfigurator: { rtpDeduction: 5 } } }
            );

            try {
                await GameService.upsertGamesRtpDeduction(tle1, [
                    { gameCode: game1.code, newRtpDeduction: newRtpDeductionGame1 },
                    { gameCode: game2.code, newRtpDeduction: newRtpDeductionGame2 }
                ]);
            } catch (e) {
                assert.fail(e);
                return;
            }

            const result1Find = await GameService.findOneEntityGame(tle1, game1.code);
            expect(result1Find.settings.rtpConfigurator.rtpDeduction).to.be.equal(newRtpDeductionGame1);

            const result2Find = await GameService.findOneEntityGame(tle1, game2.code);
            expect(result2Find.settings.rtpConfigurator.rtpDeduction).to.be.equal(newRtpDeductionGame2);
        });

        it("upsert games rtpDeduction not found game code", async () => {
            const provider1 = await createRandomGameProvider();
            const game1 = await registerRandomGame(provider1.code);

            const newRtpDeductionGame1 = 15;

            const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
            await GameService.addGameToEntity(
                master,
                { path: ":TLE1:" },
                game1.code,
                false,
                { status: "normal" }
            );

            try {
                await GameService.upsertGamesRtpDeduction(tle1, [
                    { gameCode: game1.code, newRtpDeduction: newRtpDeductionGame1 },
                    { gameCode: "some_game_code", newRtpDeduction: 10 }
                ]);
                assert.fail("Must be an error");
            } catch (e) {
                expect(e.code).to.be.equal(240);
            }

            const result1Find = await GameService.findOneEntityGame(tle1, game1.code);
            expect(result1Find.settings).to.be.equal(null);
        });

        it("upsert games rtpDeduction not found entity", async () => {
            const provider1 = await createRandomGameProvider();
            const game1 = await registerRandomGame(provider1.code);

            const newRtpDeductionGame1 = 15;

            const tle1: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
            await GameService.addGameToEntity(
                master,
                { path: ":TLE1:" },
                game1.code,
                false,
                { status: "normal" }
            );

            try {
                await GameService.upsertGamesRtpDeduction(tle1, [
                    { gameCode: game1.code, newRtpDeduction: newRtpDeductionGame1 },
                    { gameCode: "some_game_code", newRtpDeduction: 10 }
                ]);
                assert.fail("Must be an error");
            } catch (e) {
                expect(e.code).to.be.equal(240);
            }

            const result1Find = await GameService.findOneEntityGame(tle1, game1.code);
            expect(result1Find.settings).to.be.equal(null);
        });

        it("Settings are not defined", async () => {
            const gameInfo = await GameService.getOneGame(entity, game.code);
            expect(gameInfo.settings).to.be.deep.equal({});
        });

        it("Settings are defined for entity", async () => {
            await (getEntityGameService(entity)).update(game.code, {
                settings: {
                    setting1: [ "set1", "set2" ]
                }
            });
            const gameInfo = await GameService.getOneGame(entity, game.code);
            expect(gameInfo.settings).to.deep.equal({
                setting1: [ "set1", "set2" ]
            });
        });

        it("Settings are inherited from top entity", async () => {
            const topEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
            await (getEntityGameService(topEntity)).update(game.code, {
                settings: {
                    setting1: [ "set3", "set4" ]
                }
            });
            const gameInfo = await GameService.getOneGame(entity, game.code);
            expect(gameInfo.settings).to.deep.equal({
                setting1: [ "set3", "set4" ]
            });
        });

        it("Settings are inherited from master", (async () => {
            await (getEntityGameService(master)).update(game.code, {
                settings: {
                    setting1: [ "set5", "set6" ]
                }
            });
            const gameInfo = await GameService.getOneGame(entity, game.code);
            expect(gameInfo.settings).to.deep.equal({
                setting1: [ "set5", "set6" ]
            });
        }));

        it("Settings are merged", async () => {
            await (getEntityGameService(master)).update(game.code, {
                settings: {
                    setting1: [ "set7", "set8" ]
                }
            });
            const topEntity = await EntityService.findOne({ key: complexStructure.tle1.key });
            await (getEntityGameService(topEntity)).update(game.code, {
                settings: {
                    jackpotId: { jp_type1: "jp1" }
                }
            });
            await (getEntityGameService(entity)).update(game.code, {
                settings: {
                    jackpotId: { jp_type2: "jp4" }
                }
            });
            const gameTopInfo = await GameService.getOneGame(topEntity, game.code);
            expect(gameTopInfo.settings).to.deep.equal({
                setting1: [ "set7", "set8" ],
                jackpotId: { jp_type1: "jp1" }
            });
            const gameInfo = await GameService.getOneGame(entity, game.code);
            expect(gameInfo.settings).to.deep.equal({
                setting1: [ "set7", "set8" ],
                jackpotId: { jp_type1: "jp1", jp_type2: "jp4" }
            });
        });

        it("Create game with settings", async () => {
            const gameProvider = await createRandomGameProvider();
            const game1 = await registerRandomGame(gameProvider.code);

            const topEntityGame = await GameService.addGameToEntity(master,
                { path: ":TLE1:" }, game1.code, false,
                { status: "normal", settings: { setting1: [ "set1" ] } });
            expect(topEntityGame.settings).to.deep.equal({ setting1: [ "set1" ] });
            const entGame = await GameService.addGameToEntity(master,
                { path: ":TLE1:ENT1:" }, game1.code, false,
                { status: "normal", settings: { setting1: [ "set2" ] } });
            expect(entGame.settings).to.deep.equal({ setting1: [ "set2" ] });
        });

        it("Get all games without settings", async () => {
            const gameProvider = await createRandomGameProvider();
            const game1 = await registerRandomGame(gameProvider.code);
            const game2 = await registerRandomGame(gameProvider.code);

            await GameService.addGameToEntity(master,
                { path: ":TLE1:" }, game1.code, false, { status: "normal" });
            await GameService.addGameToEntity(master,
                { path: ":TLE1:ENT1:" }, game1.code, false, { status: "normal" });
            await GameService.addGameToEntity(master,
                { path: ":TLE1:" }, game2.code, false, { status: "normal" });
            await GameService.addGameToEntity(master,
                { path: ":TLE1:ENT1:" }, game2.code, false, { status: "normal" });

            let games: EntityGameInfo[] = await GameService.getAllGames(
                entity, undefined, { providerId: publicId.instance.decode(gameProvider.id) });
            games.sort((a, b) => a.code.localeCompare(b.code));
            games = games.map(entityGame => {
                delete entityGame.releaseDate;
                delete entityGame.rtpInfo;
                return entityGame;
            });
            expect(games.length).to.equal(2);
            expect(games[0]).to.deep.equal(expectedGame(gameProvider, game1, []));
            expect(games[1]).to.deep.equal(expectedGame(gameProvider, game2, []));
        });

        it("Get all games with settings", async () => {
            const gameProvider = await createRandomGameProvider();
            const game1 = await registerRandomGame(gameProvider.code);
            const game2 = await registerRandomGame(gameProvider.code);
            const game3 = await registerRandomGame(gameProvider.code);

            // settings not defined
            await GameService.addGameToEntity(master,
                { path: ":TLE1:" }, game1.code, false, { status: "normal" });
            await GameService.addGameToEntity(master,
                { path: ":TLE1:ENT1:" }, game1.code, false, { status: "normal" });
            // settings override
            await GameService.addGameToEntity(master,
                { path: ":TLE1:" }, game2.code,
                false,
                { status: "normal", settings: { setting1: true } });
            await GameService.addGameToEntity(master,
                { path: ":TLE1:ENT1:" }, game2.code,
                false,
                { status: "normal", settings: { setting1: false } });
            // settings inherited
            await GameService.addGameToEntity(master,
                { path: ":TLE1:" }, game3.code,
                false,
                { status: "normal", settings: { setting1: true } });
            await GameService.addGameToEntity(master,
                { path: ":TLE1:ENT1:" }, game3.code,
                false,
                { status: "normal" });

            const games: EntityGameInfo[] = await GameService.getAllGames(
                entity, undefined, { providerId: publicId.instance.decode(gameProvider.id) });
            games.sort((a, b) => a.code.localeCompare(b.code));

            expect(games.length).to.equal(3);
            // settings not defined
            expect(games[0].code).to.equal(game1.code);
            expect(games[0].settings).to.be.undefined;
            // settings override
            expect(games[1].code).to.equal(game2.code);
            expect(games[1].settings).to.deep.equal(
                { setting1: false });
            // settings inherited
            expect(games[2].code).to.equal(game3.code);
            expect(games[2].settings).to.deep.equal(
                { setting1: true });
        });

        it("Jackpots defined for non-jackpot game", async () => {
            const gameProvider = await createRandomGameProvider();
            const game1 = await registerRandomGame(gameProvider.code);

            await expect(GameService.addGameToEntity(master,
                { path: ":TLE1:" }, game1.code,
                false,
                { status: "normal", settings: { jackpotId: { "some": "id" } } }))
                .to.be.rejectedWith(Errors.ValidationError);
        });

        it("Jackpots defined for brand", async () => {
            const brandName = "BRAND_" + game.code;
            await getEntityFactory(entity).createEntity({
                type: ENTITY_TYPE.BRAND,
                name: brandName,
                description: "BRAND description",
                defaultCurrency: "USD",
                defaultCountry: "US",
                defaultLanguage: "en",
                jurisdictionCode: "COM",
                webSiteUrl: "http://te2st-221.com",
            });

            // missing jackpotId
            await expect(GameService.addGameToEntity(entity, { path: `:TLE1:ENT1:${brandName}:` },
                game.code, false, { settings: {}, status: "normal" })).to.be.rejectedWith(Errors.ValidationError);

            // non-exists jackpotId
            await expect(GameService.addGameToEntity(entity, { path: `:TLE1:ENT1:${brandName}:` },
                game.code, false, { settings: { jp_type1: "non-existing-id" }, status: "normal" }))
                .to.be.rejectedWith(Errors.ValidationError);

            // non-exists marketing jackpotId
            await expect(GameService.addGameToEntity(entity, { path: `:TLE1:ENT1:${brandName}:` },
                game.code, false, {
                    settings: {
                        marketing: {
                            contributions: [
                                { jackpotId: "non-existing-id", contribution: 0.5 }
                            ]
                        }
                    }, status: "normal"
                }))
                .to.be.rejectedWith(Errors.ValidationError);

            // incorrect type
            await expect(GameService.addGameToEntity(entity, { path: `:TLE1:ENT1:${brandName}:` },
                game.code, false, { settings: { typeIncorrect: "jp1" }, status: "normal" }))
                .to.be.rejectedWith(Errors.ValidationError);

            await GameService.addGameToEntity(entity, { path: `:TLE1:ENT1:${brandName}:` },
                game.code, false, {
                    settings: {
                        jackpotId: { "jp_type1": "id1", "jp_type2": "id2" },
                        marketing: {
                            contributions: [
                                { jackpotId: "id3", contribution: 0.5 }
                            ]
                        }
                    }, status: "normal"
                });
        });

        it("Jackpots updated for brand", async () => {
            const brandName = "BRAND_UPDATED_" + game.code;
            const brand = await getEntityFactory(entity).createEntity({
                type: ENTITY_TYPE.BRAND,
                name: brandName,
                description: "BRAND description",
                defaultCurrency: "USD",
                defaultCountry: "US",
                defaultLanguage: "en",
                jurisdictionCode: "COM",
                webSiteUrl: "http://te2st-222.com",
            });

            await GameService.addGameToEntity(entity, { path: `:TLE1:ENT1:${brandName}:` },
                game.code, false, { settings: {} });

            const service = getEntityGameService(brand);
            // missing jackpots
            await expect(service.restore(game.code, false))
                .to.be.rejectedWith(Errors.ValidationError);

            // missing jackpots
            await expect(service.bulkUpdateStatus({ codes: [game.code], status: "normal" }))
                .to.be.rejectedWith(Errors.ValidationError);

            // missing jackpots
            await expect(service.update(
                game.code, { settings: {}, status: "normal" })).to.be.rejectedWith(Errors.ValidationError);

            // jackpots defined
            await service.update(
                game.code, { settings: { jackpotId: { "jp_type1": "id1", "jp_type2": "id2" } }, status: "normal" });
            let gameInfo = await GameService.getOneGame(brand, game.code);
            expect(gameInfo.settings).to.deep.equal({
                jackpotId: { "jp_type1": "id1", "jp_type2": "id2" }
            });

            // jackpots updated
            await service.update(
                game.code, { settings: { jackpotId: { "jp_type1": "id3", "jp_type2": "id4" } }, status: "normal" });
            gameInfo = await GameService.getOneGame(brand, game.code);
            expect(gameInfo.settings).to.deep.equal({
                jackpotId: { "jp_type1": "id3", "jp_type2": "id4" }
            });

            // jackpots defined
            await service.suspend(game.code, false);
            await service.restore(game.code, false);
            await usingDb(async (db) => {
                const games = await db.smembers(`suspendedGames:${brand.id}`);
                expect(games).deep.equal([]);
            });
        });

        it("Jackpots defined in top level entity", async () => {
            const entityName = "ENT_" + game.code;
            const topEntity = await getEntityFactory(entity).createEntity({
                type: ENTITY_TYPE.ENTITY,
                name: entityName,
                description: "BRAND description",
                defaultCurrency: "USD",
                defaultCountry: "US",
                defaultLanguage: "en",
                jurisdictionCode: "COM",
                webSiteUrl: "http://te2st-223.com",
            });
            const brandName = "BRAND_" + game.code;
            const brand = await getEntityFactory(topEntity).createEntity({
                type: ENTITY_TYPE.BRAND,
                name: brandName,
                description: "BRAND description",
                defaultCurrency: "USD",
                defaultCountry: "US",
                defaultLanguage: "en",
                jurisdictionCode: "COM",
                webSiteUrl: "http://te2st-221h.com",
            });

            await GameService.addGameToEntity(entity, { path: `:TLE1:ENT1:${entityName}:` },
                game.code, false, { settings: { jackpotId: { "jp_type1": "id1", "jp_type2": "id2" } } });

            await GameService.addGameToEntity(entity, { path: `:TLE1:ENT1:${entityName}:${brandName}:` },
                game.code, false, { settings: {}, status: "normal" });

            const entityGamesService = await getEntityGameService(master);
            // jackpots defined
            await entityGamesService.suspend(game.code, false);
            await entityGamesService.restore(game.code, false);

            const gameInfo = await GameService.getOneGame(brand, game.code);
            expect(gameInfo.settings).to.deep.equal({
                jackpotId: { "jp_type1": "id1", "jp_type2": "id2" }
            });
        });

        it("Bulk update status should add games to suspended set", async () => {
            const provider = await createRandomGameProvider();
            const games = [];
            for (let i = 0; i < 5; i++) {
                games.push((await registerRandomGame(provider.code)).code);
            }

            const tle2: BaseEntity = await EntityService.findOne({ key: complexStructure.tle2.key });

            await GameService.addGameToEntity(master, { key: complexStructure.tle2.key },
                game.code, false);

            await GameService.addGamesToAllEntities(tle2 as Entity, { codes: games });

            const entityGameService = await getEntityGameService(tle2 as BrandEntity);
            await entityGameService.bulkUpdateStatus({ status: ENTITY_GAME_STATUS.SUSPENDED, codes: games });

            await usingDb(async (db) => {
                const suspendedGames = await db.smembers(`suspendedGames:${tle2.id}`);
                expect(suspendedGames).to.have.members(games);
            });
            const enabledGames = [];
            enabledGames.push(games.pop());
            enabledGames.push(games.pop());

            await entityGameService.bulkUpdateStatus(
                { status: ENTITY_GAME_STATUS.NORMAL, codes: enabledGames });
            await usingDb(async (db) => {
                const suspendedGames = await db.smembers(`suspendedGames:${tle2.id}`);
                expect(suspendedGames).to.have.members(games);
            });
        });

        describe("Countries restrictions validator", async () => {
            const next = (err?) => {
                if (err) {
                    throw err;
                }
            };

            it("Allow undefined and null", async () => {
                const request: any = { body: { a: 1 } };
                validateCountriesRestrictions(request, undefined, next);
                request.body.settings = { a: 2 };
                validateCountriesRestrictions(request, undefined, next);
                request.body.settings.countries = null;
                validateCountriesRestrictions(request, undefined, next);

            });

            it("Should be array of strings or empty array", async () => {
                const request: any = { body: { settings: { countries: 1 } } };
                expect(() => validateCountriesRestrictions(request, undefined, next)).to.throw(Errors.ValidationError);
                request.body.settings.countries = [ 1, "US" ];
                expect(() => validateCountriesRestrictions(request, undefined, next)).to.throw(Errors.ValidationError);
                request.body.settings.countries = [];
                validateCountriesRestrictions(request, undefined, next);
            });

            it("Blacklist and whitelist cannot be mixed", async () => {
                const request: any = { body: { settings: { countries: [ "GB", "!US", "UA" ] } } };
                expect(() => validateCountriesRestrictions(request, undefined, next)).to.throw(Errors.ValidationError);
            });

            it("Only known countries are supported", async () => {
                const request: any = { body: { settings: { countries: [ "US", "UA" ] } } };
                validateCountriesRestrictions(request, undefined, next);
                request.body.settings.countries = [ "!US", "!UA" ];
                validateCountriesRestrictions(request, undefined, next);
                request.body.settings.countries = [ "!USA", "!UA" ];
                expect(() => validateCountriesRestrictions(request, undefined, next)).to.throw(Errors.ValidationError);
            });
        });
    });

    describe("Features json-filter", () => {
        let entity;

        before(async () => {
            entity = await EntityService.findOne({ key: complexStructure.tle1ent1.key });
        });

        it("Invalid json in features filed", async () => {
            await GameService.getAllGames(entity, undefined, { features: "{dd: 1" })
                .should
                .be
                .rejectedWith(Errors.ValidationError);
        });

        it("Conflicted boolean flags in the filters", async () => {
            await GameService.getAllGames(entity, undefined,
                { features: "{\"isFreebetSupported\": true}", isFreebetSupported: false })
                .should
                .be
                .rejectedWith(Errors.ValidationError);

            await GameService.getAllGames(entity, undefined,
                { features: "{\"isFreebetSupported\": false}", isFreebetSupported: true })
                .should
                .be
                .rejectedWith(Errors.ValidationError);

            await GameService.getAllGames(entity, undefined,
                { features: "{\"isBonusCoinsSupported\": false}", isBonusCoinsSupported: true })
                .should
                .be
                .rejectedWith(Errors.ValidationError);

            await GameService.getAllGames(entity, undefined,
                {
                    features: "{\"transferEnabled\": false, isBonusCoinsSupported\": false}",
                    isBonusCoinsSupported: true
                })
                .should
                .be
                .rejectedWith(Errors.ValidationError);

            await GameService.getAllGames(entity, undefined,
                {
                    features: "{\"transferEnabled\": false, isBonusCoinsSupported\": false, isGRCGame: true}",
                    isBonusCoinsSupported: false,
                    isGRCGame: false
                })
                .should
                .be
                .rejectedWith(Errors.ValidationError);
        });

        it("Conflicted jackpot filters", async () => {
            await GameService.getAllGames(entity, undefined,
                { features: "{\"jackpotTypes\": []}", jackpotTypes: false })
                .should
                .be
                .rejectedWith(Errors.ValidationError);

            await GameService.getAllGames(entity, undefined,
                { features: "{\"jackpotTypes\": [\"aaa\"]}", jackpotTypes: "bbb,aaa" })
                .should
                .be
                .rejectedWith(Errors.ValidationError);

            await GameService.getAllGames(entity, undefined,
                { features: "{\"jackpotTypes\": []}", jackpotTypes: "bbb,aaa" })
                .should
                .be
                .rejectedWith(Errors.ValidationError);
        });

        it("Conflicted live filters", async () => {
            await GameService.getAllGames(entity, undefined,
                { features: "{\"live\": {}}", live: false })
                .should
                .be
                .rejectedWith(Errors.ValidationError);

            await GameService.getAllGames(entity, undefined,
                { features: "{\"live\": {\"provider\":\"mock\"}", live: "{\"provider\":\"mock2\"}" })
                .should
                .be
                .rejectedWith(Errors.ValidationError);

            await GameService.getAllGames(entity, undefined,
                { features: "{\"live\": {}}", live: "{\"provider\":\"mock2\"}" })
                .should
                .be
                .rejectedWith(Errors.ValidationError);
        });

    });
});
