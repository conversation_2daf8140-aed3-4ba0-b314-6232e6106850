import { suite, test } from "mocha-typescript";
import { use, should, expect } from "chai";
import * as chaiAsPromised from "chai-as-promised";
import { BaseEntity } from "../../skywind/entities/entity";
import { getEntityJurisdictionService } from "../../skywind/services/entityJurisdiction";
import { truncate } from "./helper";
import * as Errors from "../../skywind/errors";
import { findOne } from "../../skywind/services/entity";
import { getJurisdictionService, JurisdictionService } from "../../skywind/services/jurisdiction";
import { get as getJurisdictionModel, JurisdictionDBInstance } from "../../skywind/models/jurisdiction";
import { FACTORY } from "../factories/common";

const FactoryGirl = require("factory-girl");

@suite()
class EntityJurisdictionSpec {
    public static entity: BaseEntity;
    public static brand: BaseEntity;
    public static service = getEntityJurisdictionService();
    public static jurisdictionModel: JurisdictionService = getJurisdictionService();

    public static async before() {
        should();
        use(chaiAsPromised);

        await truncate();

        EntityJurisdictionSpec.entity = await FactoryGirl.factory.create("Entity");
        EntityJurisdictionSpec.brand = await FactoryGirl.factory.create("Brand", {}, {
            parent: EntityJurisdictionSpec.entity
        });
    }

    @test()
    public async addJurisdictionToEntity() {
        const jurisdiction = await FactoryGirl.factory.create("Jurisdiction", {});

        const result = await EntityJurisdictionSpec.service.add(
            EntityJurisdictionSpec.entity, jurisdiction.code);

        expect(result.id).to.be.equal(jurisdiction.id);
        expect(result.createdAt).to.be.exist;
    }

    @test()
    public async addJurisdictionToEntityTwice() {
        const jurisdiction = await FactoryGirl.factory.create("Jurisdiction");

        await EntityJurisdictionSpec.service.add(EntityJurisdictionSpec.entity, jurisdiction.code);

        await EntityJurisdictionSpec.service.add(EntityJurisdictionSpec.entity, jurisdiction.code)
            .should.eventually.rejectedWith(Errors.ValidationError)
            .then(err => {
                expect(err.message).to.be.equal("Validation error: Jurisdiction already exist in entity");
            });
    }

    @test()
    public async addNotExistJurisdictionToEntity() {
        await EntityJurisdictionSpec.service.add(EntityJurisdictionSpec.entity, "123")
            .should.eventually.rejectedWith(Errors.ValidationError)
            .then(err => {
                expect(err.message).to.be.equal("Validation error: Jurisdiction not found");
            });
    }

    @test()
    public async addJurisdictionToBrandWhenJurisdictionNotExistInParent() {
        const jurisdiction = await FactoryGirl.factory.create("Jurisdiction");
        await EntityJurisdictionSpec.service.add(EntityJurisdictionSpec.brand, jurisdiction.get("code"))
            .should.eventually.rejectedWith(Errors.ValidationError)
            .then(err => {
                expect(err.message).to.be.equal("Validation error: Jurisdiction not exist in parent");
            });
    }

    @test()
    public async addTwoJurisdictionsToBrandShouldBeRejected() {
        const [jurisdiction1, jurisdiction2] = await FactoryGirl.factory.createMany(
            FACTORY.ENTITY_JURISDICTION,
            2,
            {},
            { entityId: EntityJurisdictionSpec.entity.id }
        );

        await FactoryGirl.factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: EntityJurisdictionSpec.brand.id,
            jurisdictionId: jurisdiction1.jurisdictionId
        });

        const jurisdiction2Instance: JurisdictionDBInstance = await getJurisdictionModel()
            .findByPk(jurisdiction2.jurisdictionId);

        await EntityJurisdictionSpec.service.add(EntityJurisdictionSpec.brand, jurisdiction2Instance.get("code"));
        const result = await EntityJurisdictionSpec.service.findAll({ entityId: EntityJurisdictionSpec.brand.id });
        expect(result.length).to.be.equal(1);
        expect(result[0].code).to.be.equal(jurisdiction2Instance.get("code"));
    }

    @test()
    public async removeJurisdictionFromEntity() {
        const jurisdictionCode = "jrsd_to_remove";
        await FactoryGirl.factory.create("EntityJurisdiction", {}, {
            entityId: EntityJurisdictionSpec.entity.id,
            jurisdictionBuildOptions: {
                code: jurisdictionCode
            }
        });

        await EntityJurisdictionSpec.service.remove(EntityJurisdictionSpec.entity, jurisdictionCode);

        await EntityJurisdictionSpec.service.findOne(EntityJurisdictionSpec.entity.id,
            jurisdictionCode).should.eventually.rejectedWith(Errors.ValidationError);
    }

    @test()
    public async removeJurisdictionFromBrand() {
        const jurisdictionCode = "jrsd_to_remove2";
        await FactoryGirl.factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: EntityJurisdictionSpec.brand.id,
            jurisdictionBuildOptions: {
                code: jurisdictionCode
            }
        });

        await EntityJurisdictionSpec.service.remove(EntityJurisdictionSpec.brand, jurisdictionCode)
            .should.be.eventually.rejectedWith(Errors.ValidationError).then(err => {
                expect(err.message).to.be.equal("Validation error: You cannot remove jurisdiction from Brand");
            });
    }

    @test()
    public async removeJurisdictionFromEntityWithChildExist() {
        const jurisdiction = await FactoryGirl.factory.create("Jurisdiction");

        await FactoryGirl.factory.create("EntityJurisdiction", {}, {
            entityId: EntityJurisdictionSpec.entity.id,
            jurisdictionId: jurisdiction.id
        });

        await FactoryGirl.factory.create("EntityJurisdiction", {}, {
            entityId: EntityJurisdictionSpec.brand.id,
            jurisdictionId: jurisdiction.id
        });

        const entity = await findOne({ id: EntityJurisdictionSpec.entity.id });

        await EntityJurisdictionSpec.service.remove(entity, jurisdiction.code)
            .should.eventually.rejectedWith(Errors.ValidationError)
            .then(err => {
                expect(err.message).to.be.equal("Validation error: Jurisdiction exist in child entity");
            });
    }

    @test()
    public async findAllJurisdictionByEntity() {
        await FactoryGirl.factory.create("EntityJurisdiction");

        await FactoryGirl.factory.createMany("EntityJurisdiction", 2, {}, {
            entityId: EntityJurisdictionSpec.entity.id
        });

        const jurisdictions = await EntityJurisdictionSpec.service.findAll({
            entityId: EntityJurisdictionSpec.entity.id
        });

        expect(jurisdictions.length).to.be.equal(7);
    }

    @test()
    public async addJurisdictionToEntityWithNullDefaultCountryAndNoJurisdictionDefault() {
        // Create an entity with defaultCountry: null
        const entityWithNullDefault = await FactoryGirl.factory.create("Entity", {
            defaultCountry: null
        });

        // Create a jurisdiction without defaultCountry
        const jurisdictionWithoutDefault = await FactoryGirl.factory.create("Jurisdiction", {
            defaultCountry: null
        });

        await EntityJurisdictionSpec.service.add(entityWithNullDefault, jurisdictionWithoutDefault.code)
            .should.eventually.rejectedWith(Errors.ValidationError)
            .then(err => {
                expect(err.message).to.be.equal("Validation error: Default country should be defined in jurisdiction or entity");
            });
    }

    @test()
    public async addJurisdictionToEntityWithNullDefaultCountryAndJurisdictionDefault() {
        // Create an entity with defaultCountry: null
        const entityWithNullDefault = await FactoryGirl.factory.create("Entity", {
            defaultCountry: null
        });

        // Create a jurisdiction with defaultCountry
        const jurisdictionWithDefault = await FactoryGirl.factory.create("Jurisdiction", {
            defaultCountry: "US"
        });

        const result = await EntityJurisdictionSpec.service.add(entityWithNullDefault, jurisdictionWithDefault.code);
        expect(result.id).to.be.equal(jurisdictionWithDefault.id);
    }
}
