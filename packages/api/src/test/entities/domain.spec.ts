import { expect, should, use } from "chai";
import {
    complexStructure,
    createComplexStructure,
    resetDynamicDomain,
    setDynamicDomain,
    truncate
} from "../entities/helper";
import * as Errors from "../../skywind/errors";
import { DomainInUseError, DynamicDomainAlreadyExists, MigrationIsInProgressError } from "../../skywind/errors";
import EntityCache from "../../skywind/cache/entity";
import { SinonStub, stub } from "sinon";
import getEntityFactory from "../../skywind/services/entityFactory";
import { UniqueConstraintError } from "sequelize";
import { getDomainService } from "../../skywind/services/domain";
import { DOMAIN_TYPE } from "../../skywind/utils/common";
import { getEntityDomainService } from "../../skywind/services/entityDomainService";
import MigrationSerice from "../../skywind/services/migrationService";
import { Models } from "../../skywind/models/models";
import { MIGRATION_STATUS } from "../../skywind/entities/entity";
import chaiAsPromised = require("chai-as-promised");
import { factory } from "factory-girl";
import { FACTORY } from "../factories/common";
import { DynamicDomain } from "../../skywind/entities/domain";

should();
use(chaiAsPromised);

describe("Domains", () => {

    before(async() => {
        await truncate();
    });

    describe("DynamicDomain", () => {

        it("crud domain", async() => {
            const domain = await getDomainService().create({
                domain: "gc.gameserver.skywindgroup.com",
                environment: "gc"
            });

            expect(domain).contain({
                domain: "gc.gameserver.skywindgroup.com",
                environment: "gc"
            });

            const read = await getDomainService().findOne(domain.id);
            expect(read).deep.equal(domain);

            const readAll = await getDomainService().findAll();
            expect(readAll).deep.equal([domain]);

            const update = await getDomainService().update({
                id: domain.id,
                domain: "wc.gameserver.skywindgroup.com",
                environment: "wc"
            });
            expect(update).contain({
                id: domain.id,
                domain: "wc.gameserver.skywindgroup.com",
                environment: "wc"
            });

            await getDomainService().remove(domain.id);

            await expect(getDomainService().findOne(domain.id)).to.be.rejectedWith(Errors.DomainNotFoundError);
        });

        it("domain name is unique", async() => {
            await getDomainService().create({
                domain: "gc1.gameserver.skywindgroup.com",
                environment: "gc"
            });

            try {
                await getDomainService().create({
                    domain: "gc1.gameserver.skywindgroup.com",
                    environment: "gc"
                });
                expect.fail("Domain must be unique");
            } catch (err) {
                expect(err).instanceOf(DynamicDomainAlreadyExists);
            }
        });
    });

    describe("Static", () => {

        it("crud domain", async() => {
            const domain = await getDomainService(DOMAIN_TYPE.STATIC).create({
                domain: "gc.gaming.skywindgroup.com",
            });

            expect(domain).contain({
                domain: "gc.gaming.skywindgroup.com",
            });

            const read = await getDomainService(DOMAIN_TYPE.STATIC).findOne(domain.id);
            expect(read).deep.equal(domain);

            const readAll = await getDomainService(DOMAIN_TYPE.STATIC).findAll();
            expect(readAll).deep.equal([domain]);

            const update = await getDomainService(DOMAIN_TYPE.STATIC).update({
                id: domain.id,
                domain: "wc.gaming.skywindgroup.com",
            });
            expect(update).contain({
                id: domain.id,
                domain: "wc.gaming.skywindgroup.com",
            });

            await getDomainService(DOMAIN_TYPE.STATIC).remove(domain.id);

            await expect(getDomainService(DOMAIN_TYPE.STATIC).findOne(domain.id))
                .to.be.rejectedWith(Errors.DomainNotFoundError);
        });

        it("domain name is unique", async() => {
            await getDomainService(DOMAIN_TYPE.STATIC).create({
                domain: "gc1.gaming.skywindgroup.com",
            });

            try {
                await getDomainService(DOMAIN_TYPE.STATIC).create({
                    domain: "gc1.gaming.skywindgroup.com",
                });
                expect.fail("Domain mast be unique");
            } catch (err) {
                expect(err).instanceOf(UniqueConstraintError);
            }
        });
    });

    describe("Entity Domain", () => {

        let master;
        let masterDynamicDomain;
        let masterStaticDomain;
        let entityCacheReset;
        let entity;
        let brand;
        let merchant;
        let startMigrationStub: SinonStub;

        before(async() => {
            startMigrationStub = stub(MigrationSerice, "startMigration");
            master = await createComplexStructure();
            masterDynamicDomain = await getDomainService(DOMAIN_TYPE.DYNAMIC).create({
                domain: "gameserver.skywindgroup.com",
                environment: "gc"
            });
            masterStaticDomain = await getDomainService(DOMAIN_TYPE.STATIC).create({
                domain: "master.gaming.skywindgroup.com",
            });
            await setDynamicDomain(master, masterDynamicDomain);
            await getEntityDomainService(DOMAIN_TYPE.STATIC).set(master, masterDynamicDomain.id);

            entity = await getEntityFactory(master.find({ key: complexStructure.tle1ent1.key })).createEntity({
                name: "ENTITY",
                description: "ENTITY description",
                defaultCurrency: "USD",
                defaultCountry: "US",
                defaultLanguage: "en",
                jurisdictionCode: "COM"
            });
            brand = await getEntityFactory(entity).createEntity({
                name: "BRAND",
                type: "brand",
                description: "BRAND description",
                defaultCurrency: "USD",
                defaultCountry: "US",
                defaultLanguage: "en",
                jurisdictionCode: "COM",
                webSiteUrl: "http://test.com"
            });
            const created = await getEntityFactory(master.find({ key: complexStructure.tle1ent1.key })).createMerchant({
                name: "MARCHANT",
                description: "MARCHANT description",
                defaultCurrency: "USD",
                defaultCountry: "US",
                defaultLanguage: "en",
                code: "TEST",
                type: "ipm",
                params: {},
                jurisdictionCode: "COM",
                webSiteUrl: "http://test-merch.com"
            });
            merchant = master.find({ key: created.key });
        });

        after(() => {
            startMigrationStub.restore();
        });

        beforeEach(() => {
            startMigrationStub.reset();
            entityCacheReset = stub(EntityCache, "reset");
            entityCacheReset.resetBehavior();
        });

        afterEach(() => {
            entityCacheReset.restore();
        });

        describe("Dynamic", () => {

            it("set brand domain", async() => {
                // inherited from master
                let domain = await getEntityDomainService().get(brand);
                expect(domain).deep.equal(masterDynamicDomain);

                // inherited from tle
                const entityDomain = await getDomainService().create({
                    domain: "entity.gameserver.skywindgroup.com",
                    environment: "gc"
                }) as DynamicDomain;
                domain = await getEntityDomainService().set(entity, entityDomain.id);
                expect(domain).deep.equal(entityDomain);
                domain = await getEntityDomainService().get(brand);
                expect(domain).deep.equal(entityDomain);
                expect(startMigrationStub.callCount).equal(0);

                // updated to brand domain
                const domain1 = await getDomainService().create({
                    domain: "domain1.gameserver.skywindgroup.com",
                    environment: "gc1"
                }) as DynamicDomain;
                domain = await getEntityDomainService().set(brand, domain1.id);
                expect(domain).deep.equal(domain1);
                domain = await getEntityDomainService().get(brand);
                expect(domain).deep.equal(domain1);

                // reset to entity domain
                await resetDynamicDomain(brand);
                domain = await getEntityDomainService().get(brand);
                expect(domain).deep.equal(entityDomain);

                // cache invalidated
                expect(entityCacheReset.callCount).to.equal(2);
                expect(entityCacheReset.args).to.deep.equal([
                    [], []
                ]);
                expect(startMigrationStub.callCount).equal(1);
                expect(startMigrationStub.args[0][1].id).equal(entityDomain.id);
            });

            it("domain update is rejected - DomainInUseError", async () => {
                // inherited from tle
                const domain2 = await getDomainService().create({
                    domain: "domain2.gameserver.skywindgroup.com",
                    environment: "gc2"
                }) as DynamicDomain;

                const newBrand = await getEntityFactory(entity).createEntity({
                    name: "BRAND_check_DomainInUseError",
                    type: "brand",
                    description: "BRAND description",
                    defaultCurrency: "USD",
                    defaultCountry: "US",
                    defaultLanguage: "en",
                    jurisdictionCode: "COM",
                    webSiteUrl: "http://localhost2.com"
                });

                await getEntityDomainService().set(newBrand, domain2.id);
                expect(startMigrationStub.callCount).equal(1);
                expect(startMigrationStub.args[0][0]).equal(newBrand.id);

                return expect(getDomainService().update({
                    id: domain2.id,
                    domain: "newdomain.com",
                    environment: "gc_new"
                })).to.be.rejectedWith(DomainInUseError);
            });

            it("domain setup is rejected - MigrationIsInProgressError", async () => {
                const domain3 = await getDomainService().create({
                    domain: "domain3.gameserver.skywindgroup.com",
                    environment: "gc3"
                }) as DynamicDomain;
                const domain4 = await getDomainService().create({
                    domain: "domain4.gameserver.skywindgroup.com",
                    environment: "gc4"
                }) as DynamicDomain;

                const newBrand = await getEntityFactory(entity).createEntity({
                    name: "BRAND_withPrevDynamicDomain",
                    type: "brand",
                    description: "BRAND description",
                    defaultCurrency: "USD",
                    defaultCountry: "US",
                    defaultLanguage: "en",
                    jurisdictionCode: "COM",
                    webSiteUrl: "http://localhost3.com"
                });

                await Models.EntityModel.update({ prevDynamicDomainId: domain3.id, environment: domain3.environment },
                    { where: { id: newBrand.id } });
                await Models.EntityModel.update({ dynamicDomainId: domain3.id, environment: domain3.environment},
                    { where: { id: entity.id } });

                await expect(getEntityDomainService().set(newBrand, domain4.id))
                    .to.be.rejectedWith(MigrationIsInProgressError);

                await expect(getEntityDomainService().set(entity, domain4.id))
                    .to.be.rejectedWith(MigrationIsInProgressError);

                expect(startMigrationStub.callCount).equal(0);
                const brandAttributes = await Models.EntityModel.findByPk(newBrand.id).then(item => item.toJSON());
                const entityAttributes = await Models.EntityModel.findByPk(entity.id).then(item => item.toJSON());
                expect(entityAttributes.prevDynamicDomainId).is.null;
                expect(entityAttributes.migrationStatus).is.null;
                expect(brandAttributes.prevDynamicDomainId).equals(domain3.id);
                expect(brandAttributes.migrationStatus).is.null;
            });

            it("set brand domain and migrate it", async () => {
                const domain5 = await getDomainService().create({
                    domain: "domain5.gameserver.skywindgroup.com",
                    environment: "gc5"
                }) as DynamicDomain;
                const domain6 = await getDomainService().create({
                    domain: "domain6.gameserver.skywindgroup.com",
                    environment: "gc6"
                }) as DynamicDomain;

                const newBrand = await getEntityFactory(entity).createEntity({
                    name: "BRAND_migrate",
                    type: "brand",
                    description: "BRAND description",
                    defaultCurrency: "USD",
                    defaultCountry: "US",
                    defaultLanguage: "en",
                    jurisdictionCode: "COM",
                    webSiteUrl: "http://test-brand.com"
                });

                await Models.EntityModel.update({ dynamicDomainId: domain5.id, environment: domain5.environment },
                    { where: { id: newBrand.id } });

                const domain = await getEntityDomainService().set(newBrand, domain6.id);
                expect(domain.id).equal(domain6.id);

                const brandAttributes = await Models.EntityModel.findByPk(newBrand.id).then(item => item.toJSON());
                expect(brandAttributes.prevDynamicDomainId).is.equals(domain5.id);
                expect(brandAttributes.dynamicDomainId).is.equals(domain6.id);
                expect(brandAttributes.migrationStatus).equal(MIGRATION_STATUS.STARTED);

                expect(startMigrationStub.callCount).equal(1);
            });

            it("set entity domain and migrate brand", async () => {
                // inherited from tle
                const domain7 = await getDomainService().create({
                    domain: "domain7.gameserver.skywindgroup.com",
                    environment: "gc7"
                }) as DynamicDomain;
                const domain8 = await getDomainService().create({
                    domain: "domain8.gameserver.skywindgroup.com",
                    environment: "gc8"
                }) as DynamicDomain;

                const newEntity = await getEntityFactory(master.find({ key: complexStructure.tle1ent1.key }))
                    .createEntity({
                        name: "ENTITY_with_migration_childs",
                        description: "ENTITY description",
                        defaultCurrency: "USD",
                        defaultCountry: "US",
                        defaultLanguage: "en",
                        jurisdictionCode: "COM"
                    });

                const newSubEntity = await getEntityFactory(newEntity).createEntity({
                    name: "SUB_ENTITY_with_migration_childs",
                    description: "ENTITY description",
                    defaultCurrency: "USD",
                    defaultCountry: "US",
                    defaultLanguage: "en",
                    jurisdictionCode: "COM",
                    webSiteUrl: "http://localhost4.com"
                });

                const newBrand = await getEntityFactory(newSubEntity).createEntity({
                    name: "BRAND_migrate_child",
                    type: "brand",
                    description: "BRAND description",
                    defaultCurrency: "USD",
                    defaultCountry: "US",
                    defaultLanguage: "en",
                    jurisdictionCode: "COM",
                    webSiteUrl: "http://test-2.com",
                });

                await Models.EntityModel.update({ dynamicDomainId: domain7.id, environment: domain7.environment },
                    { where: { id: newEntity.id } });

                const domain = await getEntityDomainService().set(newEntity, domain8.id);
                expect(domain.id).equal(domain8.id);

                const brandAttributes = await Models.EntityModel.findByPk(newBrand.id).then(item => item.toJSON());
                const entityAttributes = await Models.EntityModel.findByPk(newEntity.id).then(item => item.toJSON());
                const subEntityAttributes = await Models.EntityModel.findByPk(newSubEntity.id)
                    .then(item => item.toJSON());
                expect(brandAttributes.prevDynamicDomainId).equal(domain7.id);
                expect(brandAttributes.dynamicDomainId).is.null;
                expect(brandAttributes.migrationStatus).equal(MIGRATION_STATUS.STARTED);

                expect(entityAttributes.prevDynamicDomainId).is.null;
                expect(entityAttributes.dynamicDomainId).is.equals(domain8.id);
                expect(entityAttributes.migrationStatus).is.null;

                expect(subEntityAttributes.prevDynamicDomainId).is.null;
                expect(subEntityAttributes.dynamicDomainId).is.null;
                expect(subEntityAttributes.migrationStatus).is.null;

                expect(startMigrationStub.callCount).equal(1);
            });

            it("set merchant domain", async () => {
                // inherited from master
                let domain = await getEntityDomainService().get(merchant);
                expect(domain).deep.equal(masterDynamicDomain);

                // updated to merchant domain
                const domain9 = await getDomainService().create({
                    domain: "domain9.gameserver.skywindgroup.com",
                    environment: "gc9"
                }) as DynamicDomain;
                domain = await getEntityDomainService().set(merchant, domain9.id);
                expect(domain).deep.equal(domain9);
                domain = await getEntityDomainService().get(merchant);
                expect(domain).deep.equal(domain9);

                // reset to master domain
                await resetDynamicDomain(merchant);
                domain =  await getEntityDomainService().get(merchant);
                expect(domain).deep.equal(masterDynamicDomain);

                expect(entityCacheReset.callCount).to.equal(1);
                expect(entityCacheReset.args).to.deep.equal([
                    []
                ]);
                expect(startMigrationStub.callCount).equal(1);
                expect(startMigrationStub.args[0][1].id).equal(masterDynamicDomain.id);
            });

            it("domain in use", async() => {
                await getDomainService().remove(masterDynamicDomain.id)
                    .should.eventually.rejectedWith(Errors.DomainInUseError);
                expect(startMigrationStub.callCount).equal(0);
            });

            it("reset brand domain and migrate it", async () => {
                const domain10 = await getDomainService().create({
                    domain: "domain10.gameserver.skywindgroup.com",
                    environment: "gc10"
                }) as DynamicDomain;

                const newBrand = await getEntityFactory(entity).createEntity({
                    name: "BRAND_migrate_reset",
                    type: "brand",
                    description: "BRAND description",
                    defaultCurrency: "USD",
                    defaultCountry: "US",
                    defaultLanguage: "en",
                    jurisdictionCode: "COM",
                    webSiteUrl: "http://test-123.com"
                });

                await Models.EntityModel.update({ dynamicDomainId: domain10.id, environment: domain10.environment },
                    { where: { id: newBrand.id } });

                await getEntityDomainService().reset(newBrand);

                const brandAttributes = await Models.EntityModel.findByPk(newBrand.id).then(item => item.toJSON());
                expect(brandAttributes.prevDynamicDomainId).is.equals(domain10.id);
                expect(brandAttributes.dynamicDomainId).is.null;
                expect(brandAttributes.environment).is.null;
                expect(brandAttributes.migrationStatus).equal(MIGRATION_STATUS.STARTED);

                expect(startMigrationStub.callCount).equal(1);
            });

            it("reset entity domain and migrate brand", async () => {
                const domain11 = await getDomainService().create({
                    domain: "domain11.gameserver.skywindgroup.com",
                    environment: "gc11"
                }) as DynamicDomain;

                const newEntity = await getEntityFactory(master.find({ key: complexStructure.tle1ent1.key }))
                    .createEntity({
                        name: "ENTITY_with_migration_childs_reset",
                        description: "ENTITY description",
                        defaultCurrency: "USD",
                        defaultCountry: "US",
                        defaultLanguage: "en",
                        jurisdictionCode: "COM"
                    });

                const newSubEntity = await getEntityFactory(newEntity).createEntity({
                    name: "SUB_ENTITY_with_migration_childs_reset",
                    description: "ENTITY description",
                    defaultCurrency: "USD",
                    defaultCountry: "US",
                    defaultLanguage: "en",
                    jurisdictionCode: "COM"
                });

                const newBrand = await getEntityFactory(newSubEntity).createEntity({
                    name: "BRAND_migrate_child_reset",
                    type: "brand",
                    description: "BRAND description",
                    defaultCurrency: "USD",
                    defaultCountry: "US",
                    defaultLanguage: "en",
                    jurisdictionCode: "COM",
                    webSiteUrl: "http://test-321.com"
                });

                await Models.EntityModel.update({ dynamicDomainId: domain11.id, environment: domain11.environment },
                    { where: { id: newEntity.id } });

                await getEntityDomainService().reset(newEntity);

                const brandAttributes = await Models.EntityModel.findByPk(newBrand.id).then(item => item.toJSON());
                const entityAttributes = await Models.EntityModel.findByPk(newEntity.id).then(item => item.toJSON());
                const subEntityAttributes = await Models.EntityModel.findByPk(newSubEntity.id)
                    .then(item => item.toJSON());
                expect(brandAttributes.prevDynamicDomainId).equal(domain11.id);
                expect(brandAttributes.dynamicDomainId).is.null;
                expect(brandAttributes.migrationStatus).equal(MIGRATION_STATUS.STARTED);

                expect(entityAttributes.prevDynamicDomainId).is.null;
                expect(entityAttributes.environment).is.null;
                expect(entityAttributes.dynamicDomainId).is.null;
                expect(entityAttributes.migrationStatus).is.null;

                expect(subEntityAttributes.prevDynamicDomainId).is.null;
                expect(subEntityAttributes.dynamicDomainId).is.null;
                expect(subEntityAttributes.environment).is.null;
                expect(subEntityAttributes.migrationStatus).is.null;

                expect(startMigrationStub.callCount).equal(1);
            });

            it("reset brand domain and skip migration  - parent has the same environment", async () => {
                const domain12 = await getDomainService().create({
                    domain: "domain12.gameserver.skywindgroup.com",
                    environment: "gc12"
                }) as DynamicDomain;

                const domain13 = await getDomainService().create({
                    domain: "domain13.gameserver.skywindgroup.com",
                    environment: "gc12"
                }) as DynamicDomain;

                const newEntity = await getEntityFactory(master.find({ key: complexStructure.tle1ent1.key }))
                    .createEntity({
                        name: "ENTITY_skip_migration_childs_reset_1",
                        description: "ENTITY description",
                        defaultCurrency: "USD",
                        defaultCountry: "US",
                        defaultLanguage: "en",
                        jurisdictionCode: "COM"
                    });

                const newSubEntity = await getEntityFactory(newEntity).createEntity({
                    name: "SUB_ENTITY_skip_migration_childs_reset_1",
                    description: "ENTITY description",
                    defaultCurrency: "USD",
                    defaultCountry: "US",
                    defaultLanguage: "en",
                    jurisdictionCode: "COM"
                });

                const newBrand = await getEntityFactory(newSubEntity).createEntity({
                    name: "BRAND_skip_migration_reset_1",
                    type: "brand",
                    description: "BRAND description",
                    defaultCurrency: "USD",
                    defaultCountry: "US",
                    defaultLanguage: "en",
                    jurisdictionCode: "COM",
                    webSiteUrl: "http://test1331223.com"
                });

                await Models.EntityModel.update({ dynamicDomainId: domain12.id, environment: domain12.environment },
                    { where: { id: newEntity.id } });

                await Models.EntityModel.update({ dynamicDomainId: domain13.id, environment: domain13.environment },
                    { where: { id: newBrand.id } });

                await getEntityDomainService().reset(newBrand);

                const brandAttributes = await Models.EntityModel.findByPk(newBrand.id).then(item => item.toJSON());
                const entityAttributes = await Models.EntityModel.findByPk(newEntity.id).then(item => item.toJSON());
                const subEntityAttributes = await Models.EntityModel.findByPk(newSubEntity.id)
                    .then(item => item.toJSON());
                expect(brandAttributes.prevDynamicDomainId).is.null;
                expect(brandAttributes.dynamicDomainId).is.null;
                expect(brandAttributes.migrationStatus).is.null;

                expect(entityAttributes.prevDynamicDomainId).is.null;
                expect(entityAttributes.environment).is.equal(domain12.environment);
                expect(entityAttributes.dynamicDomainId).is.equal(domain12.id);
                expect(entityAttributes.migrationStatus).is.null;

                expect(subEntityAttributes.prevDynamicDomainId).is.null;
                expect(subEntityAttributes.dynamicDomainId).is.null;
                expect(subEntityAttributes.environment).is.null;
                expect(subEntityAttributes.migrationStatus).is.null;
                expect(startMigrationStub.callCount).equal(0);
            });

            it("reset subentity domain and skip migration  - parent has the same environment", async () => {
                const domain14 = await getDomainService().create({
                    domain: "domain14.gameserver.skywindgroup.com",
                    environment: "gc14"
                }) as DynamicDomain;

                const domain15 = await getDomainService().create({
                    domain: "domain15.gameserver.skywindgroup.com",
                    environment: "gc14"
                }) as DynamicDomain;

                const newEntity = await getEntityFactory(master.find({ key: complexStructure.tle1ent1.key }))
                    .createEntity({
                        name: "ENTITY_skip_migration_childs_reset_2",
                        description: "ENTITY description",
                        defaultCurrency: "USD",
                        defaultCountry: "US",
                        defaultLanguage: "en",
                        jurisdictionCode: "COM"
                    });

                const newSubEntity = await getEntityFactory(newEntity).createEntity({
                    name: "SUB_ENTITY_skip_migration_childs_reset_2",
                    description: "ENTITY description",
                    defaultCurrency: "USD",
                    defaultCountry: "US",
                    defaultLanguage: "en",
                    jurisdictionCode: "COM"
                });

                const newBrand = await getEntityFactory(newSubEntity).createEntity({
                    name: "BRAND_skip_migration_reset_2",
                    type: "brand",
                    description: "BRAND description",
                    defaultCurrency: "USD",
                    defaultCountry: "US",
                    defaultLanguage: "en",
                    jurisdictionCode: "COM",
                    webSiteUrl: "http://231-test.com"
                });

                await Models.EntityModel.update({ dynamicDomainId: domain14.id, environment: domain14.environment },
                    { where: { id: newEntity.id } });

                await Models.EntityModel.update({ dynamicDomainId: domain15.id, environment: domain15.environment },
                    { where: { id: newSubEntity.id } });

                await getEntityDomainService().reset(newSubEntity);

                const brandAttributes = await Models.EntityModel.findByPk(newBrand.id).then(item => item.toJSON());
                const entityAttributes = await Models.EntityModel.findByPk(newEntity.id).then(item => item.toJSON());
                const subEntityAttributes = await Models.EntityModel.findByPk(newSubEntity.id)
                    .then(item => item.toJSON());
                expect(brandAttributes.prevDynamicDomainId).is.null;
                expect(brandAttributes.dynamicDomainId).is.null;
                expect(brandAttributes.migrationStatus).is.null;

                expect(entityAttributes.prevDynamicDomainId).is.null;
                expect(entityAttributes.environment).is.equal(domain14.environment);
                expect(entityAttributes.dynamicDomainId).is.equal(domain14.id);
                expect(entityAttributes.migrationStatus).is.null;

                expect(subEntityAttributes.prevDynamicDomainId).is.null;
                expect(subEntityAttributes.dynamicDomainId).is.null;
                expect(subEntityAttributes.environment).is.equal;
                expect(subEntityAttributes.migrationStatus).is.null;
                expect(startMigrationStub.callCount).equal(0);
            });

            it("reset entity domain and skip migration  - brand has the same environment", async () => {
                const domain16 = await getDomainService().create({
                    domain: "domain16.gameserver.skywindgroup.com",
                    environment: "gc16"
                }) as DynamicDomain;

                const domain17 = await getDomainService().create({
                    domain: "domain17.gameserver.skywindgroup.com",
                    environment: "gc16"
                }) as DynamicDomain;

                const newEntity = await getEntityFactory(master.find({ key: complexStructure.tle1ent1.key }))
                    .createEntity({
                        name: "ENTITY_skip_migration_childs_reset_3",
                        description: "ENTITY description",
                        defaultCurrency: "USD",
                        defaultCountry: "US",
                        defaultLanguage: "en",
                        jurisdictionCode: "COM"
                    });

                const newSubEntity = await getEntityFactory(newEntity).createEntity({
                    name: "SUB_ENTITY_skip_migration_childs_reset_3",
                    description: "ENTITY description",
                    defaultCurrency: "USD",
                    defaultCountry: "US",
                    defaultLanguage: "en",
                    jurisdictionCode: "COM"
                });

                const newSubSybEntity = await getEntityFactory(newEntity).createEntity({
                    name: "SUB_SUB_ENTITY_skip_migration_childs_reset_3",
                    description: "ENTITY description",
                    defaultCurrency: "USD",
                    defaultCountry: "US",
                    defaultLanguage: "en",
                    jurisdictionCode: "COM"
                });

                const newBrand = await getEntityFactory(newSubEntity).createEntity({
                    name: "BRAND_skip_migration_reset_3",
                    type: "brand",
                    description: "BRAND description",
                    defaultCurrency: "USD",
                    defaultCountry: "US",
                    defaultLanguage: "en",
                    jurisdictionCode: "COM",
                    webSiteUrl: "http://tesft1.com"
                });

                await Models.EntityModel.update({ dynamicDomainId: domain16.id, environment: domain16.environment },
                    { where: { id: newSubEntity.id } });

                await Models.EntityModel.update({ dynamicDomainId: domain17.id, environment: domain17.environment },
                    { where: { id: newBrand.id } });

                await getEntityDomainService().reset(newSubEntity);

                const brandAttributes = await Models.EntityModel.findByPk(newBrand.id).then(item => item.toJSON());
                const entityAttributes = await Models.EntityModel.findByPk(newEntity.id).then(item => item.toJSON());
                const subEntityAttributes = await Models.EntityModel.findByPk(newSubEntity.id)
                    .then(item => item.toJSON());
                const subSubEntityAttributes = await Models.EntityModel.findByPk(newSubSybEntity.id)
                    .then(item => item.toJSON());
                expect(brandAttributes.prevDynamicDomainId).is.null;
                expect(brandAttributes.dynamicDomainId).is.equal(domain17.id);
                expect(brandAttributes.environment).is.equal(domain17.environment);
                expect(brandAttributes.migrationStatus).is.null;

                expect(entityAttributes.prevDynamicDomainId).is.null;
                expect(entityAttributes.environment).is.null;
                expect(entityAttributes.dynamicDomainId).is.null;
                expect(entityAttributes.migrationStatus).is.null;

                expect(subEntityAttributes.prevDynamicDomainId).is.null;
                expect(subEntityAttributes.dynamicDomainId).is.null;
                expect(subEntityAttributes.environment).is.equal;
                expect(subEntityAttributes.migrationStatus).is.null;

                expect(subSubEntityAttributes.prevDynamicDomainId).is.null;
                expect(subSubEntityAttributes.dynamicDomainId).is.null;
                expect(subSubEntityAttributes.environment).is.equal;
                expect(subSubEntityAttributes.migrationStatus).is.null;

                expect(startMigrationStub.callCount).equal(0);
            });

        });

        describe("Static", () => {

            it("set brand domain", async() => {
                // inherited from master
                let domain = await getEntityDomainService(DOMAIN_TYPE.STATIC).get(brand);
                expect(domain).deep.equal(masterStaticDomain);

                // inherited from tle
                const entityDomain = await getDomainService(DOMAIN_TYPE.STATIC).create({
                    domain: "entity.gaming.skywindgroup.com",
                });
                domain = await getEntityDomainService(DOMAIN_TYPE.STATIC).set(entity, entityDomain.id);
                expect(domain).deep.equal(entityDomain);
                domain = await getEntityDomainService(DOMAIN_TYPE.STATIC).get(brand);
                expect(domain).deep.equal(entityDomain);

                // updated to brand domain
                const brandDomain = await getDomainService(DOMAIN_TYPE.STATIC).create({
                    domain: "brand.gaming.skywindgroup.com",
                });
                domain = await getEntityDomainService(DOMAIN_TYPE.STATIC).set(brand, brandDomain.id);
                expect(domain).deep.equal(brandDomain);
                domain = await getEntityDomainService(DOMAIN_TYPE.STATIC).get(brand);
                expect(domain).deep.equal(brandDomain);

                // reset to entity domain
                domain = await getEntityDomainService(DOMAIN_TYPE.STATIC).reset(brand);
                expect(domain).deep.equal(entityDomain);

                // cache invalidated
                expect(entityCacheReset.callCount).to.equal(3);
                expect(entityCacheReset.args).to.deep.equal([
                    [], [], []
                ]);
            });

            it("set merchant domain", async() => {
                // inherited from master
                let domain = await getEntityDomainService(DOMAIN_TYPE.STATIC).get(merchant);
                expect(domain).deep.equal(masterStaticDomain);

                // updated to merchant domain
                const merchDomain = await getDomainService(DOMAIN_TYPE.STATIC).create({
                    domain: "merchant.gaming.skywindgroup.com",
                });
                domain = await getEntityDomainService(DOMAIN_TYPE.STATIC).set(merchant, merchDomain.id);
                expect(domain).deep.equal(merchDomain);
                domain = await getEntityDomainService(DOMAIN_TYPE.STATIC).get(merchant);
                expect(domain).deep.equal(merchDomain);

                // reset to master domain
                domain = await getEntityDomainService(DOMAIN_TYPE.STATIC).reset(merchant);
                expect(domain).deep.equal(masterStaticDomain);

                expect(entityCacheReset.callCount).to.equal(2);
                expect(entityCacheReset.args).to.deep.equal([
                    [], []
                ]);
            });

            it("domain in use", async() => {
                await expect(getDomainService(DOMAIN_TYPE.STATIC).remove(masterStaticDomain.id))
                    .to.be.rejectedWith(Errors.DomainInUseError);
            });

            it("Set static domain should be rejected if not belongs to static domain tags",
                async () => {
                    const staticDomain = await factory.create(FACTORY.STATIC_DOMAIN);
                    const staticDomainTags = ["chebureck-egor.com"];
                    const brandWithTags = await factory.create(FACTORY.BRAND, {}, { staticDomainTags });

                    await getEntityDomainService(DOMAIN_TYPE.STATIC).set(brandWithTags, staticDomain.id)
                        .should.eventually.rejectedWith(Errors.ValidationError,
                            `Validation error: Domain is not valid. Allowed tags - ${staticDomainTags}`);
            });

            it("Set static domain should be successfully if belongs to static domain tags",
                async () => {
                    const staticDomainTag = "chebureck-egor.com";
                    const staticDomain = await factory.create(FACTORY.STATIC_DOMAIN, {}, { domain: staticDomainTag });
                    const brandWithTags = await factory.create(FACTORY.BRAND, {},
                        { staticDomainTags: [ staticDomainTag ] });

                    const result = await getEntityDomainService(DOMAIN_TYPE.STATIC).set(brandWithTags, staticDomain.id);

                    expect(result.domain).to.be.equal(staticDomainTag);
                });

            it("Set static domain tags", async () => {
                const staticDomainTag = "chebureck-egor.com";
                const testEntity = await factory.create(FACTORY.ENTITY);
                const { staticDomainTags, ..._ } = await getEntityDomainService(DOMAIN_TYPE.STATIC)
                    .setTags(testEntity, [ staticDomainTag ]);
                expect(staticDomainTags).to.be.deep.equal([ staticDomainTag ]);
            });

            it("Reset static domain tags", async () => {
                const testEntity = await factory.create(FACTORY.ENTITY,
                    { staticDomainTags: [ "chebureck-egor.com" ] });
                const { staticDomainTags, ..._ } = await getEntityDomainService(DOMAIN_TYPE.STATIC)
                    .resetTags(testEntity);
                expect(staticDomainTags).to.be.undefined;
            });
        });
    });
});
