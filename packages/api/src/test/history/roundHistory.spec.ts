import * as gameHistory from "../../skywind/history/gameHistory";
import { findGameHistoryEntries } from "../../skywind/history/gameHistory";
import { expect, should, use } from "chai";
import * as Errors from "../../skywind/errors";
import { OperationForbidden } from "../../skywind/errors";
import * as sinon<PERSON>hai from "sinon-chai";
import config from "../../skywind/config";
import { getSpinHistoryModel } from "../../skywind/models/spinHistory";
import * as boAggrRound from "../../skywind/models/aggrround";
import { setDynamicDomain, truncate } from "../entities/helper";
import { getRoundHistoryModel } from "../../skywind/models/roundHistory";
import { BrandEntity } from "../../skywind/entities/brand";
import { getDomainService } from "../../skywind/services/domain";
import * as EntityService from "../../skywind/services/entity";
import { findOne } from "../../skywind/services/entity";
import { RoundHistory } from "../../skywind/entities/gameHistory";
import { SinonStub, stub } from "sinon";
import { Merchant } from "../../skywind/entities/merchant";
import { verifyInternalToken } from "../../skywind/utils/token";
import { UnfinishedRoundManagementServiceFactory } from "../../skywind/history/unfinishedRoundManagementService";
import { getSpinHistoryByRound } from "../../skywind/history/spinHistory";
import { FACTORY } from "../factories/common";
import { getRoundHistoryServiceFactory } from "../../skywind/history/gameHistoryServiceFactory";
import { getMerchantInternalService } from "../../skywind/services/merchant";
import EntitySettingsService from "../../skywind/services/settings";
import * as gameService from "../../skywind/services/game";
import { BrandFinalizationType, GameFinalizationType } from "@skywind-group/sw-wallet-adapter-core";
import { createFinalizeService } from "../../skywind/history/unfinishedRoundFinalizeService";
import { DynamicDomain } from "../../skywind/entities/domain";

const chaiAsPromise = require("chai-as-promised");
const FactoryGirl = require("factory-girl");

const request = require("request");
const gameProvider = require("../../skywind/services/gameprovider");

use(sinonChai);
should();
use(chaiAsPromise);

const ORIGINAL_REDSHIFT_CONFIG = config.gameHistory.redshift;

describe("Game history", () => {
    const factory = FactoryGirl.factory;

    before(async () => {
        await truncate();
    });

    describe("Spins history", () => {
        let brand: BrandEntity;
        let brandWithManualPayments: BrandEntity;
        let staticMerchant: Merchant;
        let rounds: RoundHistory[];
        let merchant: Merchant;

        before(async () => {

            config.gameHistory.redshift = config.db;

            await boAggrRound.get().drop({ cascade: true });
            await boAggrRound.get().sync({ schema: config.db.schema });

            await getRoundHistoryModel().drop({ cascade: true });
            await getRoundHistoryModel().sync({ schema: config.db.schema });

            await getSpinHistoryModel().drop({ cascade: true });
            await getSpinHistoryModel().sync({ schema: config.db.schema });

            brand = await factory.create(FACTORY.BRAND);
            brandWithManualPayments = await factory.create(FACTORY.BRAND);
            merchant = await factory.create(FACTORY.MERCHANT,
                {},
                { params: { supportForceFinishAndRevert: true } });
            staticMerchant = await factory.create(FACTORY.MERCHANT,
                {},
                { params: { supportForceFinishAndRevert: true } });
            rounds = await factory.createMany("AggrRounds", 6, {}, {
                brandId: brand.id,
                gameCode: "test_game"
            });

            for (const round of rounds) {
                round["spins"] = await factory.createMany("SpinHistory", 10, {}, {
                    brandId: brand.id,
                    playerCode: round.playerCode,
                    gameCode: round.gameCode,
                    roundId: round.roundId
                });
            }

            const roundWithoutPayment = rounds[3];
            await factory.create("SpinHistory", {}, {
                brandId: brand.id,
                playerCode: roundWithoutPayment.playerCode,
                gameCode: roundWithoutPayment.gameCode,
                roundId: roundWithoutPayment.roundId,
                walletTransactionId: null
            });

            await factory.create("RoundHistory", {}, {
                brandId: brand.id
            });

            const parentGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
                gameBuildOptions: {
                    code: "test_game"
                }
            });
            await factory.create(FACTORY.ENTITY_GAME, {}, {
                parentEntityGameId: parentGame.id,
                entityId: staticMerchant.brandId,
                gameId: parentGame.gameId
            });

        });

        beforeEach(async () => {
            // for getEventHistory
            config.gameHistory.newRounds = false;
        });

        after(() => {
            config.gameHistory.redshift = ORIGINAL_REDSHIFT_CONFIG;
        });

        it("Retrieves round history by default", async () => {
            const filter = undefined;
            const round = rounds[0];
            const history = await getSpinHistoryByRound(brand, +round.roundId, filter, round.playerCode);

            expect(history.length).deep.equal(10);

        });

        it("Retrieves round history without balance", async () => {
            config.gameHistory.newRounds = true;
            const brandTest1 = await factory.create(FACTORY.BRAND);
            const service = new EntitySettingsService(brandTest1);
            await service.update({ hideBalanceBeforeAndAfter: true } as any);
            await factory.create("RoundHistory", {}, {
                brandId: brandTest1.id
            });
            const history = await findGameHistoryEntries(brandTest1);

            expect(history.length).deep.equal(1);
            expect(history[0].balanceBefore).to.be.undefined;
            expect(history[0].balanceAfter).to.be.undefined;
            config.gameHistory.newRounds = false;
        });

        it("Retrieves round history with limit", async () => {
            const reqQuery = { limit: 2, offset: 0 };
            const round = rounds[1];
            const history = await getSpinHistoryByRound(brand, +round.roundId, reqQuery, round.playerCode);

            expect(history.length).deep.equal(2);

        });

        it("Retrieves round history with limit and offset", async () => {
            const reqQuery = { limit: 1, offset: 0 };
            const round = rounds[2];
            const history = await getSpinHistoryByRound(brand, +round.roundId, reqQuery, round.playerCode);

            expect(history.length).deep.equal(1);
            expect(history[0].spinNumber).deep.equal(round["spins"][0].spinNumber);

            const nextReqQuery = { limit: 1, offset: 1 };
            const next = await getSpinHistoryByRound(brand, +round.roundId, nextReqQuery, round.playerCode);

            expect(next.length).deep.equal(1);
            expect(next[0].spinNumber).deep.equal(round["spins"][1].spinNumber);

        });

        it("Retrieves round history with details", async () => {
            const reqQuery = { limit: 1, offset: 0 };
            const round = rounds[1];
            const history = await getSpinHistoryByRound(brand, +round.roundId, reqQuery, round.playerCode, true);

            expect(history.length).deep.equal(1);
            expect(history[0].spinNumber).deep.equal(round["spins"][0].spinNumber);
            expect(history[0].details).to.exist;
        });

        it("Retrieves round history with and without isPayment flag", async () => {
            const round = rounds[3];
            let history = await getSpinHistoryByRound(brand,
                +round.roundId, { isPayment: "true" }, round.playerCode);
            expect(history.length).deep.equal(10);

            history = await getSpinHistoryByRound(brand,
                +round.roundId,
                { isPayment: "false" },
                round.playerCode);
            expect(history.length).deep.equal(1);
        });

        it("Retrieves event history", async () => {
            const round = rounds[4];
            const reqQuery = { limit: 10, playerCode: round.playerCode, gameCode: round.gameCode };
            const history = await gameHistory.getEvents(brand, reqQuery);

            expect(history.length).deep.equal(10);
            expect(history[0].roundId).equal(+round.roundId);
            expect(history[0].spinNumber).equal(round["spins"][9].spinNumber);

            const nextReqQuery = { limit: 10, ts__gte: history[9].ts, ...reqQuery };
            const next = await gameHistory.getEvents(brand, nextReqQuery);

            expect(next.length).deep.equal(10);
            expect(next[0].roundId).equal(+round.roundId);
            expect(next[0].spinNumber).equal(round["spins"][9].spinNumber);
        });

        it("Retrieves event history with filter", async () => {
            const round = rounds[3];
            let history = await gameHistory.getEvents(brand, {
                isPayment: "true", playerCode: round.playerCode, gameCode: round.gameCode
            });
            expect(history.length).deep.equal(10);

            history = await gameHistory.getEvents(brand, {
                isPayment: "false", playerCode: round.playerCode, gameCode: round.gameCode
            });
            expect(history.length).deep.equal(1);
        });

        it("Retrieves moorgate spin history", async () => {
            const moorgateMerchant = await factory.create(FACTORY.MERCHANT, {}, {
                type: "pop_moorgate",
                params: {
                    walletType: 0
                }
            });
            const round = await factory.create(FACTORY.ROUND_HISTORY, {},
                { brandId: moorgateMerchant.brandId });
            await factory.create(FACTORY.SPIN_HISTORY, {}, {
                brandId: moorgateMerchant.brandId,
                roundId: round.roundId,
                playerCode: round.playerCode,
                gameCode: round.gameCode,
                spinNumber: 0,
                balanceBefore: 10000.15,
                balanceAfter: 20000.15
            });
            await factory.createMany(FACTORY.SPIN_HISTORY, 3, {}, {
                brandId: moorgateMerchant.brandId,
                roundId: round.roundId,
                playerCode: round.playerCode,
                gameCode: round.gameCode,
                balanceBefore: 10000.15,
                balanceAfter: 20000.15,
                endOfRound: true
            });

            const moorgateEntity = await findOne<BrandEntity>({ id: moorgateMerchant.brandId });
            const [firstSpin, ...restSpins] = await getSpinHistoryByRound(moorgateEntity, round.roundId);
            const lastSpin = restSpins[restSpins.length - 1];

            expect(firstSpin.balanceBefore).to.be.exist;
            expect(firstSpin.balanceAfter).to.be.undefined;

            expect(lastSpin.balanceAfter).to.be.exist;
            expect(lastSpin.balanceBefore).to.be.undefined;
        });
    });

    describe("Rounds history", () => {
        let entLvl1;
        let entLvl2;
        let brandLvl1;
        let brandLvl2;
        let roundsBrand1: RoundHistory[];
        let roundsBrand2: RoundHistory[];

        before(async () => {

            config.gameHistory.redshift = config.db;
            config.gameHistory.newRounds = true;

            await boAggrRound.get().drop({ cascade: true });
            await boAggrRound.get().sync({ schema: config.db.schema });

            await getRoundHistoryModel().drop({ cascade: true });
            await getRoundHistoryModel().sync({ schema: config.db.schema });

            entLvl1 = await factory.create(FACTORY.ENTITY);
            entLvl2 = await factory.create(FACTORY.ENTITY, {}, { parent: entLvl1 });
            brandLvl1 = await factory.create(FACTORY.BRAND, {}, { parent: entLvl1 });
            brandLvl2 = await factory.create(FACTORY.BRAND, {}, { parent: entLvl2 });

            roundsBrand1 = await factory.createMany(FACTORY.ROUND_HISTORY, 5, {}, {
                brandId: brandLvl1.id
            });
            roundsBrand2 = await factory.createMany(FACTORY.ROUND_HISTORY, 5, {}, {
                brandId: brandLvl2.id
            });
        });

        after(() => {
            config.gameHistory.redshift = ORIGINAL_REDSHIFT_CONFIG;
            config.gameHistory.newRounds = false;
        });

        it("Retrieves round history of brand as usually", async () => {
            const historyService = getRoundHistoryServiceFactory().getHistoryServiceV2();
            await factory.create(FACTORY.ROUND_HISTORY, {}, {
                brandId: brandLvl1.id,
                totalJpContribution: null,
                totalJpWin: null
            });
            let brandRounds = await historyService.getRounds(brandLvl1, {}); // check passing a brand as param
            expect(brandRounds.length).equal(6);

            brandRounds = await historyService.getRounds(brandLvl1.id, {}); // check passing a brandId as param
            expect(brandRounds.length).equal(6);

            const [firstRound] = brandRounds;
            expect(firstRound.totalJpContribution).to.be.equal(0);
            expect(firstRound.totalJpWin).to.be.equal(0);
        });

        it("Retrieves round history of entity down the entity tree", async () => {
            const historyService = getRoundHistoryServiceFactory().getHistoryServiceV2();
            const entityRounds = await historyService.getRounds(entLvl1, {}); // check passing a brand as param
            expect(entityRounds.length).equal(11);
        });
    });

    describe("Round force-finish/revert/finalize", () => {
        let merchant: Merchant;
        let brand: BrandEntity;
        let popMerchant;
        let popBrand;
        let staticMerchant: Merchant;
        let brandWithManualPayments: BrandEntity;

        let findOneEntityGameMock: SinonStub;
        let requestPostMock: SinonStub;
        let getGameMock: SinonStub;

        before(async () => {
            await getRoundHistoryModel().drop({ cascade: true });
            await getRoundHistoryModel().sync({ schema: config.db.schema });
            await getRoundHistoryModel().drop({ cascade: true });
            await getRoundHistoryModel().sync({ schema: config.db.schema });

            getGameMock = stub(gameProvider, "getGame");
            findOneEntityGameMock = stub(gameService, "findOneEntityGame");
            requestPostMock = stub(request, "post");
            config.gameHistory.redshift = config.db;

            brand = await factory.create(FACTORY.BRAND);
            brandWithManualPayments = await factory.create(FACTORY.BRAND);
            merchant = await factory.create(FACTORY.MERCHANT, {}, {
                brandId: brand.id,
                params: {
                    supportForceFinishAndRevert: true,
                }
            });
            staticMerchant = await factory.create(FACTORY.MERCHANT,
                {},
                { params: { supportForceFinishAndRevert: true } });
            popBrand = await factory.create(FACTORY.MERCHANT_ENTITY);
            popMerchant = await factory.create(FACTORY.MERCHANT, {}, {
                brandId: popBrand.id,
                type: "pop_moorgate",
                params: {
                    supportForceFinishAndRevert: true,
                }
            });

            const domain = await getDomainService().create({
                domain: "gc.gameserver.skywindgroup.com",
                environment: "gc"
            });

            await setDynamicDomain(brand, domain as DynamicDomain);
            const entity = await EntityService.findOne({ id: merchant.brandId });
            await setDynamicDomain(entity, domain as DynamicDomain);
            await setDynamicDomain(brandWithManualPayments, domain as DynamicDomain);

            await setDynamicDomain(popBrand, domain as DynamicDomain);
            const entityPOP = await EntityService.findOne({ id: merchant.brandId });
            await setDynamicDomain(entityPOP, domain as DynamicDomain);

            const rounds = await factory.createMany(FACTORY.ROUND_HISTORY, 5, {}, {
                brandId: brand.id
            });

            const staticEntity = await EntityService.findOne({ id: staticMerchant.brandId });
            await setDynamicDomain(staticEntity, domain as DynamicDomain);

            for (const round of rounds) {
                await factory.createMany(FACTORY.SPIN_HISTORY, 10, {}, {
                    brandId: brand.id,
                    roundId: round.roundId,
                    playerCode: round.playerCode,
                    gameCode: round.gameCode
                });
            }
        });

        beforeEach(() => {
            requestPostMock.reset();
            getGameMock.reset();
            findOneEntityGameMock.reset();
        });

        after(() => {
            config.gameHistory.redshift = ORIGINAL_REDSHIFT_CONFIG;
            requestPostMock.restore();
            getGameMock.restore();
            findOneEntityGameMock.restore();
        });

        it("Fails to init service for merchant", async () => {
            const testMerchant = await factory.create(FACTORY.MERCHANT);
            const merchantEntity = await EntityService.findOne({ id: testMerchant.brandId });
            await UnfinishedRoundManagementServiceFactory
                .getUnfinishedRoundManagementService(merchantEntity as BrandEntity)
                .should
                .eventually
                .rejectedWith(Errors.MerchantDoesntSupportError);
            expect(requestPostMock.callCount).eq(0);
        });

        it("Force finish round for merchant with special flag", async () => {
            requestPostMock.yields(null, { statusCode: 200 }, []);
            const entity = await EntityService.findOne({ id: merchant.brandId });
            const merchantRound = await factory.create("AggrRounds", {}, {
                brandId: merchant.brandId
            });

            const service = await UnfinishedRoundManagementServiceFactory.getUnfinishedRoundManagementService(
                entity as BrandEntity,
                true);
            await service.forceFinish(merchantRound.toJSON(), undefined, true, true);

            expect(requestPostMock.callCount).eq(1);
            expect(requestPostMock.args[0][0])
                .equal("https://gc.gameserver.skywindgroup.com:4000/gamerecovery/forcefinish");

            const token = await verifyInternalToken(requestPostMock.args[0][1].body.token) as any;
            expect({
                force: token.force,
                reverted: token.reverted,
                round: {
                    ...token.round,
                }
            }).deep.equal({
                force: true,
                reverted: true,
                round: {
                    ...merchantRound.toJSON(),
                    firstTs: merchantRound.toJSON().firstTs.toISOString(),
                    ts: merchantRound.toJSON().ts.toISOString()
                }
            });
        });

        it.skip("Fail to force finish round for merchant with special flag", async () => {
            await getMerchantInternalService().forceFinishRound({
                merchantCode: merchant.code,
                merchantType: merchant.type,
                roundId: "123",
                playerCode: "pl001"
            }, false)
                .should.eventually.rejectedWith(Errors.GameHistoryDetailsNotFound);
        });

        // TODO: add more tests

        it("Start finalize game when brand supports finalize", async () => {
            requestPostMock.yields(null, { statusCode: 200 }, []);
            findOneEntityGameMock.resolves({ game: {} });
            getGameMock.resolves({});
            const entity = await EntityService.findOne({ id: staticMerchant.brandId });
            await (new EntitySettingsService(entity)).patch(
                { finalizationSupport: BrandFinalizationType.ROUND_STATISTICS }
            );
            const service = createFinalizeService(entity as BrandEntity, undefined);
            await service.startFinalize("test_merchant_session", "test_mch_player", "test_game");

            expect(requestPostMock.callCount).eq(1);
            expect(requestPostMock.args[0][0])
                .equal("https://gc.gameserver.skywindgroup.com:4000/gamerecovery/start-finalize");
            const tokenInfo = {
                brandId: staticMerchant.brandId,
                merchantSessionId: "test_merchant_session",
                operatorSupportsFinalization: true
            };

            const parsedExpectedToken: any = await verifyInternalToken(requestPostMock.args[0][1].body.token);
            expect({
                brandId: parsedExpectedToken.brandId,
                merchantSessionId: parsedExpectedToken.merchantSessionId,
                operatorSupportsFinalization: parsedExpectedToken.operatorSupportsFinalization,
            }).deep.eq(tokenInfo);
        });

        it.skip("Start finalize external game", async () => {
            requestPostMock.resolves({});
            const entity = await EntityService.findOne({ id: staticMerchant.brandId });
            await (new EntitySettingsService(entity)).patch(
                { finalizationSupport: BrandFinalizationType.ROUND_STATISTICS }
            );
            const service = createFinalizeService(entity as BrandEntity,
                {
                    type: "external",
                    code: "itg_gov",
                    providerGameCode: "gov",
                    gameProvider: { code: "ITG", settings: { recoveryApiUrl: "http://countach.com/api" } }
                } as any);
            await service.startFinalize("test_merchant_session", "test_mch_player", "itg_gov");

            expect(requestPostMock.args[0][0]).equal("http://localhost:80/skywind/closeround/start");

            expect(requestPostMock.args[0][1].body).deep.equal({
                "brandFinalizationType": "roundStatistics",
                "brandId": entity.id,
                "gameCode": "gov",
                "gameFinalizationType": undefined,
                "playerCode": "test_mch_player"
            });
        });

        it.skip("Finalize external game", async () => {
            requestPostMock.resolves({});
            const entity = await EntityService.findOne({ id: staticMerchant.brandId });
            await (new EntitySettingsService(entity)).patch(
                { finalizationSupport: BrandFinalizationType.ROUND_STATISTICS }
            );
            const service = createFinalizeService(entity as BrandEntity,
                {
                    type: "external",
                    code: "itg_gov",
                    providerGameCode: "gov",
                    gameProvider: { code: "ITG", settings: { recoveryApiUrl: "http://countach.com/api" } },
                    features: { gameFinalizationType: "neverBeImplemented" }
                } as any);
            await service.finalize({ playerCode: "test_mch_player", gameCode: "itg_gov" });

            expect(requestPostMock.args[0][0]).equal("http://localhost:80/skywind/closeround/finalize");

            expect(requestPostMock.args[0][1].body).deep.equal({
                "brandFinalizationType": "roundStatistics",
                "brandId": entity.id,
                "gameCode": "gov",
                "gameFinalizationType": "neverBeImplemented",
                "playerCode": "test_mch_player"
            });
        });

        it("Start finalize game for forceFinish", async () => {
            requestPostMock.yields(null, { statusCode: 200 }, []);
            findOneEntityGameMock.resolves({
                game: { features: { gameFinalizationType: GameFinalizationType.AUTO_PLAY } },
                isSuspended: () => false
            });
            getGameMock.resolves({});
            const entity = await EntityService.findOne({ id: staticMerchant.brandId });
            await (new EntitySettingsService(entity)).patch(
                { finalizationSupport: BrandFinalizationType.ROUND_STATISTICS }
            );
            const service = createFinalizeService(entity as BrandEntity, undefined);
            await (new EntitySettingsService(entity)).patch(
                { finalizationSupport: BrandFinalizationType.FORCE_FINISH }
            );
            await service.startFinalize("test_merchant_session", "test_mch_player", "test_game");

            expect(requestPostMock.callCount).eq(1);
            expect(requestPostMock.args[0][0])
                .equal("https://gc.gameserver.skywindgroup.com:4000/gamerecovery/start-finalize");
            const tokenInfo = {
                brandId: staticMerchant.brandId,
                merchantSessionId: "test_merchant_session",
                operatorSupportsFinalization: false,
                brandFinalizationType: BrandFinalizationType.FORCE_FINISH
            };

            const parsedExpectedToken: any = await verifyInternalToken(requestPostMock.args[0][1].body.token);
            expect({
                brandId: parsedExpectedToken.brandId,
                merchantSessionId: parsedExpectedToken.merchantSessionId,
                operatorSupportsFinalization: parsedExpectedToken.operatorSupportsFinalization,
                brandFinalizationType: tokenInfo.brandFinalizationType
            }).deep.eq(tokenInfo);
        });

        it("Start finalize game - failed, context not found", async () => {
            findOneEntityGameMock.resolves({ game: {} });
            getGameMock.resolves({});
            const entity = await EntityService.findOne({ id: staticMerchant.brandId });
            const service = createFinalizeService(entity as BrandEntity, undefined);
            await (new EntitySettingsService(entity)).patch({ finalizationSupport: null });
            await expect(service.startFinalize("test_merchant_session", "test_mch_player", "test_game"))
                .to.be.rejectedWith(OperationForbidden);
            expect(requestPostMock.callCount).eq(0);
        });

        it("Finish finalize game", async () => {
            requestPostMock.yields(null, { statusCode: 201 }, []);
            const entity = await EntityService.findOne({ id: merchant.brandId });
            await (new EntitySettingsService(entity)).patch({
                finalizationSupport: BrandFinalizationType.OFFLINE_PAYMENTS
            });
            const service = createFinalizeService(entity as BrandEntity, undefined);
            await service.completeFinalize("test_merchant_session", undefined);

            expect(requestPostMock.callCount).eq(1);
            expect(requestPostMock.args[0][0])
                .equal("https://gc.gameserver.skywindgroup.com:4000/gamerecovery/complete-finalize");
            const tokenInfo = {
                brandId: merchant.brandId,
                merchantSessionId: "test_merchant_session"
            };

            const tokenData: any = await verifyInternalToken(requestPostMock.args[0][1].body.token);
            expect(tokenData).deep.equal({
                ...tokenInfo,
                "exp": tokenData.exp,
                "iat": tokenData.iat,
                "iss": tokenData.iss
            });
        });

        it.skip("Finish finalize external game", async () => {
            requestPostMock.resolves({});
            const entity = await EntityService.findOne({ id: staticMerchant.brandId });
            await (new EntitySettingsService(entity)).patch(
                { finalizationSupport: BrandFinalizationType.ROUND_STATISTICS }
            );
            const service = createFinalizeService(entity as BrandEntity,
                {
                    type: "external",
                    code: "itg_gov",
                    providerGameCode: "gov",
                    gameProvider: { code: "ITG", settings: { recoveryApiUrl: "http://countach.com/api/" } }
                } as any);
            await service.completeFinalize("test_merchant_session", "test_mch_player");

            expect(requestPostMock.args[0][0]).equal("http://localhost:80/skywind/closeround/complete");

            expect(requestPostMock.args[0][1].body).deep.equal({
                "brandFinalizationType": "roundStatistics",
                "brandId": entity.id,
                "gameCode": "gov",
                "gameFinalizationType": undefined,
                "playerCode": "test_mch_player"
            });
        });

        it("Finalize game", async () => {
            requestPostMock.yields(null, { statusCode: 200 }, []);
            findOneEntityGameMock.resolves({ game: {}, isSuspended: () => false });
            getGameMock.resolves({ features: { gameFinalizationType: GameFinalizationType.FINALIZATION } });
            const entity = await EntityService.findOne({ id: merchant.brandId });
            await (new EntitySettingsService(entity)).patch({
                finalizationSupport: BrandFinalizationType.OFFLINE_PAYMENTS
            });
            const service = createFinalizeService(entity as BrandEntity, undefined);
            await service.finalize({ gameContextId: "some_game_context_id", gameCode: "gameCode" } as any);

            expect(requestPostMock.callCount).eq(1);
            expect(requestPostMock.args[0][0])
                .equal("https://gc.gameserver.skywindgroup.com:4000/gamerecovery/finalize");
            const tokenInfo = {
                gameContextId: "some_game_context_id", gameCode: "gameCode", finalizeBrokenPayment: "retry",
                operatorSupportsFinalization: true,
                gameFinalizationType: "finalization",
                brandFinalizationType: BrandFinalizationType.OFFLINE_PAYMENTS
            };

            const tokenData: any = await verifyInternalToken(requestPostMock.args[0][1].body.token);
            expect(tokenData).deep.equal({
                ...tokenInfo,
                "exp": tokenData.exp,
                "iat": tokenData.iat,
                "iss": tokenData.iss
            });
        });

        it("Finalize game - error", async () => {
            findOneEntityGameMock.resolves({ game: {}, isSuspended: () => false });
            getGameMock.resolves({ features: {} });
            const entity = await EntityService.findOne({ id: merchant.brandId });
            const service = createFinalizeService(entity as BrandEntity, undefined);
            const finalizationPromise = service.finalize({
                gameContextId: "some_game_context_id",
                gameCode: "gameCode"
            });
            await (finalizationPromise).should
                .eventually
                .rejectedWith(OperationForbidden);
            expect(requestPostMock.called).false;
        });

        it("Finalize game with manual payments", async () => {
            requestPostMock.yields(null, { statusCode: 200 }, []);
            findOneEntityGameMock.resolves({
                game: { features: { gameFinalizationType: GameFinalizationType.AUTO_PLAY } },
                isSuspended: () => false
            });
            getGameMock.resolves({ features: { gameFinalizationType: GameFinalizationType.AUTO_PLAY } });
            const entitySettings = new EntitySettingsService(brandWithManualPayments);
            await entitySettings.update(
                { finalizationSupport: BrandFinalizationType.MANUAL_PAYMENTS } as any
            );
            const service = createFinalizeService(brandWithManualPayments as BrandEntity, undefined);
            await service.finalize({
                gameContextId: "some_game_context_id",
                gameCode: "gameCode",
                gameFinalizationType: GameFinalizationType.AUTO_PLAY
            });

            expect(requestPostMock.callCount).eq(1);
            expect(requestPostMock.lastCall.args[0])
                .equal("https://gc.gameserver.skywindgroup.com:4000/gamerecovery/finalize");
            const tokenInfo = {
                gameContextId: "some_game_context_id",
                gameCode: "gameCode",
                operatorSupportsFinalization: true,
                brandFinalizationType: "manualPayments",
                finalizeBrokenPayment: "markFinalized",
                gameFinalizationType: "autoPlay",
                lockContext: true
            };

            const tokenData: any = await verifyInternalToken(requestPostMock.lastCall.args[1].body.token);
            expect(tokenData).deep.equal({
                ...tokenInfo,
                "exp": tokenData.exp,
                "iat": tokenData.iat,
                "iss": tokenData.iss
            });
        });

        it("Finalize game - override brand finalization type by entity game settings", async () => {
            requestPostMock.yields(null, { statusCode: 200 }, []);
            findOneEntityGameMock.resolves({
                    game: {},
                    settings: { finalizationSupport: BrandFinalizationType.FORCE_FINISH },
                    isSuspended: () => false
                }
            );
            getGameMock.resolves({ features: { gameFinalizationType: GameFinalizationType.AUTO_PLAY } });
            await new EntitySettingsService(brandWithManualPayments).update(
                { finalizationSupport: BrandFinalizationType.MANUAL_PAYMENTS } as any
            );
            const service = createFinalizeService(brandWithManualPayments as BrandEntity, undefined);
            await service.finalize({ gameContextId: "some_game_context_id", gameCode: "gameCode" } as any);

            expect(requestPostMock.callCount).eq(1);
            expect(requestPostMock.args[0][0])
                .equal("https://gc.gameserver.skywindgroup.com:4000/gamerecovery/finalize");
            const tokenInfo = {
                gameContextId: "some_game_context_id",
                gameCode: "gameCode",
                operatorSupportsFinalization: false,
                brandFinalizationType: "forceFinish",
                finalizeBrokenPayment: "retry",
                gameFinalizationType: "autoPlay",
            };

            const tokenData: any = await verifyInternalToken(requestPostMock.args[0][1].body.token);
            expect(tokenData).deep.equal({
                ...tokenInfo,
                "exp": tokenData.exp,
                "iat": tokenData.iat,
                "iss": tokenData.iss
            });
        });

        it("Finalize game - force finish instead of finalization", async () => {
            requestPostMock.yields(null, { statusCode: 200 }, []);
            findOneEntityGameMock.resolves({ game: {}, isSuspended: () => false });
            getGameMock.resolves({ features: { gameFinalizationType: GameFinalizationType.NONE } });
            const entity = await EntityService.findOne({ id: merchant.brandId });
            await (new EntitySettingsService(entity)).patch({
                finalizationSupport: BrandFinalizationType.OFFLINE_PAYMENTS,
                isForceFinishInsteadOfFinalizationSupported: true
            });
            const service = createFinalizeService(entity as BrandEntity, undefined);
            await service.finalize({
                gameContextId: "games:context:brandId:playerCode:gameCode:web",
                gameCode: "gameCode"
            } as any);

            expect(requestPostMock.callCount).eq(1);
            expect(requestPostMock.args[0][0])
                .to
                .equal("https://gc.gameserver.skywindgroup.com:4000/gamerecovery/forcefinish");

            const tokenData: any = await verifyInternalToken(requestPostMock.args[0][1].body.token);
            expect(tokenData).to.deep.equal({
                force: false,
                gameContextId: "games:context:brandId:playerCode:gameCode:web",
                exp: tokenData.exp,
                iat: tokenData.iat,
                iss: tokenData.iss
            });
        });

        it("Finalize game - try to do force finish instead of finalization but isForceFinishForbidden = true - does finalization",
            async () => {
                requestPostMock.yields(null, { statusCode: 200 }, []);
                findOneEntityGameMock.resolves({ game: {}, isSuspended: () => false });
                getGameMock.resolves({
                    features: {
                        isForceFinishForbidden: true,
                        gameFinalizationType: GameFinalizationType.FINALIZATION
                    }
                });
                const entity = await EntityService.findOne({ id: merchant.brandId });
                await (new EntitySettingsService(entity)).patch({
                    finalizationSupport: BrandFinalizationType.OFFLINE_PAYMENTS,
                    isForceFinishInsteadOfFinalizationSupported: true
                });
                const service = createFinalizeService(entity as BrandEntity, undefined);
                await service.finalize({
                    gameContextId: "games:context:brandId:playerCode:gameCode:web",
                    gameCode: "gameCode"
                } as any);

                expect(requestPostMock.callCount).eq(1);
                expect(requestPostMock.args[0][0])
                    .to
                    .equal("https://gc.gameserver.skywindgroup.com:4000/gamerecovery/finalize");
                const tokenInfo = {
                    gameContextId: "games:context:brandId:playerCode:gameCode:web",
                    gameCode: "gameCode",
                    finalizeBrokenPayment: "retry",
                    operatorSupportsFinalization: true,
                    gameFinalizationType: GameFinalizationType.FINALIZATION,
                    brandFinalizationType: BrandFinalizationType.OFFLINE_PAYMENTS
                };

                const tokenData: any = await verifyInternalToken(requestPostMock.args[0][1].body.token);
                expect(tokenData).to.deep.equal({
                    ...tokenInfo,
                    exp: tokenData.exp,
                    iat: tokenData.iat,
                    iss: tokenData.iss
                });
            }
        );

        it("Finalize game - try to do force finish instead of finalization but isManualApiCall = true - does finalization",
            async () => {
                requestPostMock.yields(null, { statusCode: 200 }, []);
                findOneEntityGameMock.resolves({ game: {}, isSuspended: () => false });
                getGameMock.resolves({
                    features: {
                        isForceFinishForbidden: true,
                        gameFinalizationType: GameFinalizationType.FINALIZATION
                    }
                });
                const entity = await EntityService.findOne({ id: merchant.brandId });
                await (new EntitySettingsService(entity)).patch({
                    finalizationSupport: BrandFinalizationType.OFFLINE_PAYMENTS,
                    isForceFinishInsteadOfFinalizationSupported: true
                });
                const service = createFinalizeService(entity as BrandEntity, undefined);
                await service.finalize({
                    gameContextId: "games:context:brandId:playerCode:gameCode:web",
                    gameCode: "gameCode",
                    isManualApiCall: true
                } as any);
                expect(requestPostMock.callCount).eq(1);
                expect(requestPostMock.args[0][0])
                    .to
                    .equal("https://gc.gameserver.skywindgroup.com:4000/gamerecovery/finalize");
                const tokenInfo = {
                    gameContextId: "games:context:brandId:playerCode:gameCode:web",
                    gameCode: "gameCode",
                    finalizeBrokenPayment: "retry",
                    operatorSupportsFinalization: true,
                    isManualApiCall: true,
                    gameFinalizationType: GameFinalizationType.FINALIZATION,
                    brandFinalizationType: BrandFinalizationType.OFFLINE_PAYMENTS
                };

                const tokenData: any = await verifyInternalToken(requestPostMock.args[0][1].body.token);
                expect(tokenData).to.deep.equal({
                    ...tokenInfo,
                    exp: tokenData.exp,
                    iat: tokenData.iat,
                    iss: tokenData.iss
                });
            }
        );
    });
});
