import { expect, should, use } from "chai";
import {
    getEvents,
    getGameHistoryDetails,
    getGameHistoryDetailsForGHApp,
    getGameHistoryDetailsImageUrl,
    getGameVersion,
    getRoundHistoryDetailsForGHApp,
    getRoundInfoForGHApp
} from "../../skywind/history/gameHistory";
import { getSpinHistoryModel } from "../../skywind/models/spinHistory";
import { getGameInitSettingsModel } from "../../skywind/models/gameInitSettings";
import { createRandomGameProvider, registerGame, setDynamicDomain, truncate } from "../entities/helper";
import EntitySettingsService from "../../skywind/services/settings";
import * as GameProviderService from "../../skywind/services/gameprovider";
import { generateGameHistoryVisualizationToken, verifyGameHistoryVisualizationToken } from "../../skywind/utils/token";
import { RoundHistory } from "../../skywind/entities/gameHistory";
import { getRoundHistoryModel } from "../../skywind/models/roundHistory";
import config from "../../skywind/config";
import { getEntityDomainService } from "../../skywind/services/entityDomainService";
import { DOMAIN_TYPE } from "../../skywind/utils/common";
import { FACTORY } from "../factories/common";
import { getSpinHistoryByRound } from "../../skywind/history/spinHistory";
import { BrandEntity } from "../../skywind/entities/brand";
import { getDomainService } from "../../skywind/services/domain";
import { Game } from "../../skywind/entities/game";
import { ChildEntity } from "../../skywind/entities/entity";

const chaiAsPromise = require("chai-as-promised");
should();
use(chaiAsPromise);

const FactoryGirl = require("factory-girl");

describe("Game history", () => {

    const game1 = {
        id: "GAME777",
        code: "CrashTestGame"
    };

    const game2 = {
        id: "sw_boofge",
        code: "sw_boofge",
        title: "BookOfGemsMegaways",
        url: "https://{staticDomain}/bookofgemsmegaways/{clientVersion}/index.html?hide_play_for_real=true",
        defaultClientVersion: "develop/412"
    };

    const game3 = {
        id: "itg_luckyforestcasino",
        code: "itg_luckyforestcasino",
        providerGameCode: "LuckyForestCasino",
        title: "Lucky Forest Casino",
        url: "https://stag-wa.slotfactory.com/launch/skywind/?"
    };

    let brand: BrandEntity;
    let brandSettingsService: EntitySettingsService;
    let rounds: RoundHistory[];
    let dynaicDomain;
    let domainName;
    const factory = FactoryGirl.factory;

    async function createGame(data: any) {
        const game: Game = await factory.create(FACTORY.GAME, {}, data);
        await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: game.id,
            entityId: brand.id,
        });
    }

    before(async () => {
        await truncate();
        await getSpinHistoryModel().drop();
        await getSpinHistoryModel().sync();
        await getSpinHistoryModel().truncate({ cascade: true });

        await getRoundHistoryModel().drop();
        await getRoundHistoryModel().sync();
        await getRoundHistoryModel().truncate({ cascade: true });

        await getGameInitSettingsModel().drop();
        await getGameInitSettingsModel().sync();

        brand = await factory.create(FACTORY.BRAND, {
            defaultLanguage: "en"
        });

        await factory.create(FACTORY.GAME, {}, {
            code: game1.code,
            providerGameCode: game1.id,
        });

        await createGame({
            code: game2.code,
            providerGameCode: game2.id,
            title: game2.title,
            url: game2.url,
            historyRenderType: 3,
            defaultClientVersion: game2.defaultClientVersion,
        });

        await createGame({
            code: game3.code,
            providerGameCode: game3.providerGameCode,
            title: game3.title,
            url: game3.url,
            historyRenderType: 3
        });
        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, { entityId: brand.id });

        brandSettingsService = new EntitySettingsService(brand);
        await brandSettingsService.patch({
            urlParams: {
                history_url: "https://{staticDomain}/game_history/index.html#"
            },
            ghAppSettings: { urlParams: { showAdditionalInfo: true } }
        });

        dynaicDomain = await getDomainService().create({
            domain: "gcX.gameserver.skywindgroup.com",
            environment: "gcX"
        });
        await setDynamicDomain(brand, dynaicDomain);

        domainName = "game777.skywindgroup.com";
        const domain = await getDomainService(DOMAIN_TYPE.STATIC).create({
            domain: domainName,
        });
        await getEntityDomainService(DOMAIN_TYPE.STATIC).set(brand, domain.id);

        const swRounds = await factory.createMany("RoundHistory", 7, {}, {
            brandId: brand.id,
            gameId: game1.id,
            gameCode: game1.code
        });

        for (const round of swRounds) {
            round["spins"] = await factory.createMany("SpinHistory", 10, {}, {
                roundId: round.roundId,
                gameCode: round.gameCode,
                brandId: brand.id,
                playerCode: round.playerCode,
                gameId: game1.id,
                endOfRound: true,
                credit: 5,
                debit: 10
            });
        }

        const itgRounds = await factory.createMany("RoundHistory", 1, {}, {
            brandId: brand.id,
            gameId: game3.id,
            gameCode: game3.code
        });

        for (const round of itgRounds) {
            round["spins"] = await factory.createMany("SpinHistory", 3, {}, {
                roundId: round.roundId,
                gameCode: round.gameCode,
                brandId: brand.id,
                playerCode: round.playerCode,
                gameId: game3.id,
                endOfRound: true,
                credit: 5,
                debit: 10
            });
        }

        rounds = [...swRounds, ...itgRounds];

        await getGameInitSettingsModel().create({
            gameId: game1.id,
            version: "1.1.1",
            data: {
                description: "Game init data"
            }
        });

        await getGameInitSettingsModel().create({
            gameId: game3.id,
            version: "1.1.1",
            data: {
                description: "Game init data"
            }
        });

        config.gameHistory.newRounds = true;
    });

    it("Retrieves round history", async () => {
        const round1 = rounds[0];
        const spinHistory1 = await getSpinHistoryByRound(brand, +round1.roundId, undefined, round1.playerCode);
        expect(spinHistory1).to.exist;
        expect(spinHistory1.length).to.be.equal(10);
        expect(spinHistory1[0].isPayment).equal(true);
        expect(spinHistory1[0].spinNumber).equal(1);
        expect(spinHistory1[0].endOfRound).equal(true);

        const round2 = rounds[1];
        const spinHistory9 = await getSpinHistoryByRound(brand, +round2.roundId, undefined, round2.playerCode);
        expect(spinHistory9).to.exist;
        expect(spinHistory9.length).to.be.equal(10);
        expect(spinHistory9[0].spinNumber).equal(round2["spins"][0].spinNumber);
        expect(spinHistory9[0].endOfRound).equal(true);
        const spinHistory10 = await getSpinHistoryByRound(brand, 10, undefined, "somePlayer");
        expect(spinHistory10).to.exist;
        expect(spinHistory10.length).to.be.equal(0);

        const details = await getGameHistoryDetails(brand, +round1.roundId,
            spinHistory1[0].spinNumber, { playerCode: round1.playerCode });

        expect(details).deep.eq({
            "details": {},
            "gameId": "GAME777",
            "gameCode": "CrashTestGame",
            "gameVersion": "1.1.1",
            "initSettings": {
                "description": "Game init data",
            },
            "historyInfo": {
                "historyRenderType": 0,
                "url": `https://${domainName}/game_history/index.html#`
            },
            "roundId": +round1.roundId,
            "spinNumber": spinHistory1[0].spinNumber,
            "ts": spinHistory1[0].ts,
            "totalJpContribution": 1,
            "totalJpWin": 3,
            "credit": 5,
            "debit": 10,
            "extraData": null,
            "currency": "USD"
        });
    });

    it("Retrieves spin history with details for ITG", async () => {
        await brandSettingsService.patch({
            urlParams: {
                history_url: "https://{staticDomain}/game_history/index.html#",
                history2_url: "https://{staticDomain}/game_history/index2.html#"
            },
        });

        const round7 = rounds[7];
        const spinHistory = await getSpinHistoryByRound(brand, +round7.roundId, undefined, round7.playerCode, true);
        expect(spinHistory).to.exist;
        expect(spinHistory.length).to.be.equal(3);
        expect(spinHistory[0].isPayment).equal(true);
        expect(spinHistory[0].spinNumber).equal(1);
        expect(spinHistory[0].endOfRound).equal(true);
        expect(spinHistory[0].details.historyInfo.historyRenderType).equal(3);
        expect(spinHistory[0].details.historyInfo.url).equal(`https://${domainName}/game_history/index2.html#`);
    });

    it("Retrieves round history with new history info", async () => {
        const url = "http://gc.gaming.skywindgroup.com/simei/latest/index.html" +
            "?startGameToken={startGameToken}&url=http://api.cd.d.skywind-tech.com:4000/casino/game2";

        await GameProviderService.update(game1.code, { url, historyRenderType: 1 } as any);

        const round = rounds[2];
        const details = await getGameHistoryDetails(brand, +round.roundId, 2, { playerCode: round.playerCode });
        expect(details).deep.eq({
            "details": {},
            "gameId": "GAME777",
            "gameCode": "CrashTestGame",
            "gameVersion": "1.1.1",
            "initSettings": {
                "description": "Game init data",
            },
            "historyInfo": {
                "historyRenderType": 1,
                "url": "http://gc.gaming.skywindgroup.com/simei/latest/history.html"
            },
            "roundId": +round.roundId,
            "spinNumber": 2,
            "ts": details.ts,
            "totalJpContribution": 1,
            "totalJpWin": 3,
            "credit": 5,
            "debit": 10,
            "extraData": null,
            "currency": "USD"
        });
    });

    it("Retrieves round history with new history info (using Domain)", async () => {
        const url = "http://{staticDomain}/simei/latest/index.html" +
            "?startGameToken={startGameToken}&url=http://api.cd.d.skywind-tech.com:4000/casino/game2";
        await GameProviderService.update(game1.code, { url } as any);

        const round = rounds[3];
        const details = await getGameHistoryDetails(brand, +round.roundId, 2, { playerCode: round.playerCode });
        expect(details).deep.eq({
            "details": {},
            "gameId": "GAME777",
            "gameCode": "CrashTestGame",
            "gameVersion": "1.1.1",
            "initSettings": {
                "description": "Game init data",
            },
            "historyInfo": {
                "historyRenderType": 1,
                "url": `http://${domainName}/simei/latest/history.html`
            },
            "roundId": +round.roundId,
            "spinNumber": 2,
            "ts": details.ts,
            "totalJpContribution": 1,
            "totalJpWin": 3,
            "credit": 5,
            "debit": 10,
            "extraData": null,
            "currency": "USD"
        });
    });

    it("Retrieves round history with new history 2 url (using Domain)", async () => {
        const url = "http://{staticDomain}/simei/latest/index.html" +
            "?startGameToken={startGameToken}&url=http://api.cd.d.skywind-tech.com:4000/casino/game2";
        await GameProviderService.update(game1.code, { url, historyRenderType: 3 } as any);
        await brandSettingsService.patch({
            urlParams: {
                history_url: "https://{staticDomain}/game_history/index.html#",
                history2_url: "https://{staticDomain}/game_history/index2.html#"
            },
        });

        const round = rounds[3];
        const details = await getGameHistoryDetails(brand, +round.roundId, 2, { playerCode: round.playerCode });
        expect(details).deep.eq({
            "details": {},
            "gameId": "GAME777",
            "gameCode": "CrashTestGame",
            "gameVersion": "1.1.1",
            "initSettings": {
                "description": "Game init data",
            },
            "historyInfo": {
                "historyRenderType": 3,
                "url": `https://${domainName}/game_history/index2.html#`
            },
            "roundId": +round.roundId,
            "spinNumber": 2,
            "totalJpContribution": 1,
            "totalJpWin": 3,
            "ts": details.ts,
            "credit": 5,
            "debit": 10,
            "extraData": null,
            "currency": "USD"
        });
    });

    it("Retrieves round history with new history url from game", async () => {
        const url = "http://{staticDomain}/simei/latest/history_LIVE.html";
        await GameProviderService.update(game1.code, { historyUrl: url } as any);

        const round = rounds[3];
        const details = await getGameHistoryDetails(brand, +round.roundId, 2, { playerCode: round.playerCode });
        expect(details).deep.eq({
            "details": {},
            "gameId": "GAME777",
            "gameCode": "CrashTestGame",
            "gameVersion": "1.1.1",
            "initSettings": {
                "description": "Game init data",
            },
            "historyInfo": {
                "historyRenderType": 3,
                "url": `http://${domainName}/simei/latest/history_LIVE.html`
            },
            "roundId": +round.roundId,
            "spinNumber": 2,
            "totalJpContribution": 1,
            "totalJpWin": 3,
            "ts": details.ts,
            "credit": 5,
            "debit": 10,
            "extraData": null,
            "currency": "USD"
        });

        await GameProviderService.update(game1.code, { historyUrl: null, historyRenderType: 1 } as any);
    });

    it("retrieves round history with isPayment filter", async () => {
        const round = rounds[4];
        let spinHistory1 = await getSpinHistoryByRound(brand,
            +round.roundId,
            { isPayment: "true" },
            round.playerCode);
        expect(spinHistory1).to.exist;
        expect(spinHistory1.length).to.be.equal(10);
        expect(spinHistory1[0].isPayment).equal(true);
        expect(spinHistory1[0].spinNumber).equal(round["spins"][0].spinNumber);
        expect(spinHistory1[0].endOfRound).equal(true);
        const round5 = rounds[5];
        spinHistory1 = await getSpinHistoryByRound(brand,
            +round5.roundId,
            { isPayment: "false" },
            round5.playerCode);
        expect(spinHistory1.length).to.be.equal(0);
        spinHistory1 = await getSpinHistoryByRound(brand,
            +round5.roundId,
            { isPayment: "true" },
            round5.playerCode);
        expect(spinHistory1.length).to.be.equal(10);
    });

    it("Retrieves spin history", async () => {
        const round = rounds[6];
        let spinHistory = await getEvents(brand, { playerCode: round.playerCode, gameCode: round.gameCode });
        expect(spinHistory).to.exist;
        expect(spinHistory.length).to.be.equal(10);
        expect(spinHistory[0].roundId).to.deep.equal(+round.roundId);
        expect(spinHistory[0].spinNumber).to.deep.equal(round["spins"][9].spinNumber);
        expect(spinHistory[9].roundId).to.deep.equal(+round.roundId);
        expect(spinHistory[9].spinNumber).to.deep.equal(round["spins"][0].spinNumber);

        // filter
        spinHistory = await getEvents(brand, {
            isPayment: "false", playerCode: round.playerCode, gameCode: round.gameCode
        });
        expect(spinHistory).to.exist;
        expect(spinHistory.length).to.be.equal(0);

        // nothing found
        spinHistory = await getEvents(brand,
            { limit: 1, ts__lt: "2015-01-08", playerCode: round.playerCode, gameCode: round.gameCode });
        expect(spinHistory).to.exist;
        expect(spinHistory.length).to.be.equal(0);
    });

    it("gets game version", async () => {
        const gameProvider = await createRandomGameProvider();
        const game = await registerGame(gameProvider.code, "game-with-version", "test", "https://game777.skywindgroup.com/game_history/index.html");
        await getGameInitSettingsModel().create({
            gameId: "game-with-version",
            version: "1.1.1",
            data: {
                description: "Game init data"
            }
        });
        const gameDetails = await getGameVersion(game.code, "1.1.1", brand.id);

        expect(gameDetails).to.deep.equal({
            "gameId": "game-with-version",
            "gameVersion": "1.1.1",
            "initSettings": {
                "description": "Game init data"
            },
            "jrsdSettings": {
                "rulesDateStamped": true,
                "showRTP": true,
            },
            "historyInfo": {
                "url": "https://game777.skywindgroup.com/game_history/history.html"
            },
        });
    });

    it("gets game version for ITG", async () => {
        const providerGameCode = "itg_game-with-version";
        const gameProvider = await createRandomGameProvider();
        const game = await registerGame(gameProvider.code, providerGameCode, "test");

        const gameDetails = await getGameVersion(game.code, "1.1.1", brand.id);

        expect(gameDetails).to.deep.equal({
            "gameId": providerGameCode,
            "gameVersion": "1.1.1",
            "initSettings": undefined,
            "jrsdSettings": {
                "rulesDateStamped": true,
                "showRTP": true,
            },
            "historyInfo": {
                "url": `${config.itgBaseUrl}/history/${providerGameCode}/?presenter=1&locale=en`
            },
        });
    });

    it("gets url for game history app", async () => {
        const round = rounds[0];
        const data = await getGameHistoryDetailsImageUrl(brand, +round.roundId,
            { language: "ch-zn", timezone: "Asia/Shanghai", spinNumber: 2 });

        expect(data.imageUrl).to.contain("history.html#?");
        expect(data.imageUrl).to.contain("url=" + dynaicDomain.domain);
        expect(data.imageUrl).to.contain("language=ch-zn");
        expect(data.imageUrl).to.contain("showAdditionalInfo=true");

        const tokenStr = data.imageUrl.substr(
            data.imageUrl.indexOf("data=") + 5,
            data.imageUrl.indexOf("&url=") - data.imageUrl.indexOf("data=") - 5);
        const tokenData = await verifyGameHistoryVisualizationToken(tokenStr);

        expect(tokenData.sId).to.eq(2);
        expect(tokenData.rId).to.eq(+round.roundId);
        expect(tokenData.eId).to.eq(brand.id);
        expect(tokenData.timezone).to.eq("Asia/Shanghai");
    });

    it("gets url for game history 2 app", async () => {

        const round = rounds[0];
        const data = await getGameHistoryDetailsImageUrl(brand, +round.roundId,
            { language: "ch-zn", timezone: "Asia/Shanghai", spinNumber: 2, gameCode: game2.code });

        expect(data.imageUrl).to.contain("index2.html#?");
        expect(data.imageUrl).to.contain("url=" + encodeURIComponent("https://" + dynaicDomain.domain));
        expect(data.imageUrl).to.contain("language=ch-zn");
        expect(data.imageUrl).to.contain("showAdditionalInfo=true");
        expect(data.imageUrl).to.contain("gameName=" + game2.title);
        expect(data.imageUrl).to.contain("bookofgemsmegaways" + encodeURIComponent("/" + game2.defaultClientVersion));

        const historyUrl = encodeURIComponent("https://" + domainName + game2.url.substring(
            game2.url.indexOf("}") + 1,
            game2.url.indexOf("?"))
            .replace("index", "history")
            .replace("{clientVersion}", game2.defaultClientVersion)
        );

        expect(data.imageUrl).to.contain("historyUrl=" + historyUrl);

        const tokenStr = data.imageUrl.substr(
            data.imageUrl.indexOf("data=") + 5,
            data.imageUrl.indexOf("&url=") - data.imageUrl.indexOf("data=") - 5);
        const tokenData = await verifyGameHistoryVisualizationToken(tokenStr);

        expect(tokenData.sId).to.eq(2);
        expect(tokenData.rId).to.eq(+round.roundId);
        expect(tokenData.eId).to.eq(brand.id);
        expect(tokenData.timezone).to.eq("Asia/Shanghai");
    });

    it("gets url for ITG game history 2 app", async () => {

        const round = rounds[0];
        const language = "ch-zn";
        const data = await getGameHistoryDetailsImageUrl(brand, +round.roundId,
            { language, timezone: "Asia/Shanghai", spinNumber: 2, gameCode: game3.code });

        expect(data.imageUrl).to.contain("index2.html#?");
        expect(data.imageUrl).to.contain("url=" + encodeURIComponent("https://" + dynaicDomain.domain));
        expect(data.imageUrl).to.contain("language=ch-zn");
        expect(data.imageUrl).to.contain("showAdditionalInfo=true");
        expect(data.imageUrl).to.contain("gameName=" + encodeURIComponent(game3.title));

        const lang = (brand as ChildEntity).defaultLanguage;
        const historyUrl = encodeURIComponent(`${config.itgBaseUrl}/history/${game3.providerGameCode}/?presenter=1&locale=${lang}`);

        expect(data.imageUrl).to.contain("historyUrl=" + historyUrl);
    });

    it("gets url for history based on spinNumber and fetch token data", async () => {
        const round = rounds[0];
        const data = await getGameHistoryDetailsImageUrl(brand,
            +round.roundId,
            { language: "ch-zn", spinNumber: 2 });

        expect(data.imageUrl).to.contain("url=" + dynaicDomain.domain);
        expect(data.imageUrl).to.contain("language=ch-zn");

        const tokenStr = data.imageUrl.substr(
            data.imageUrl.indexOf("data=") + 5,
            data.imageUrl.indexOf("&url=") - data.imageUrl.indexOf("data=") - 5);

        const details = await getGameHistoryDetailsForGHApp(tokenStr);
        expect(details).deep.eq({
            "details": {},
            "gameId": "GAME777",
            "gameCode": "CrashTestGame",
            "gameVersion": "1.1.1",
            "initSettings": {
                "description": "Game init data",
            },
            "historyInfo": {
                "historyRenderType": 1,
                "url": `http://${domainName}/simei/latest/history.html`
            },
            "roundId": +round.roundId,
            "spinNumber": 2,
            "currency": "USD",
            "type": "slot",
            "ts": details.ts,
            "totalJpContribution": 1,
            "totalJpWin": 3,
            "credit": 5,
            "debit": 10,
            "bet": 0,
            "win": 0,
            "device": "web",
            "balanceBefore": undefined,
            "balanceAfter": undefined,
            "extraData": null,
            "jrsdSettings": {
                "rulesDateStamped": true,
                "showRTP": true,
            },
        });
    });

    it("gets url for ITG history based on spinNumber and fetch token data", async () => {
        const round = rounds[7];
        const data = await getGameHistoryDetailsImageUrl(brand,
            +round.roundId,
            { language: "ch-zn", spinNumber: 2 });

        expect(data.imageUrl).to.contain("url=" + dynaicDomain.domain);
        expect(data.imageUrl).to.contain("language=ch-zn");

        const tokenStr = data.imageUrl.substr(
            data.imageUrl.indexOf("data=") + 5,
            data.imageUrl.indexOf("&url=") - data.imageUrl.indexOf("data=") - 5);

        const details = await getGameHistoryDetailsForGHApp(tokenStr);
        expect(details).deep.eq({
            "details": {},
            "gameId": "itg_luckyforestcasino",
            "gameCode": "itg_luckyforestcasino",
            "gameVersion": "1.1.1",
            "initSettings": {
                "description": "Game init data",
            },
            "historyInfo": {
                "historyRenderType": 3,
                "url": `${config.itgBaseUrl}/history/${game3.providerGameCode}/?presenter=1&locale=en`
            },
            "roundId": +round.roundId,
            "spinNumber": 2,
            "currency": "USD",
            "type": "slot",
            "ts": details.ts,
            "totalJpContribution": 1,
            "totalJpWin": 3,
            "credit": 5,
            "debit": 10,
            "bet": 0,
            "win": 0,
            "device": "web",
            "balanceBefore": undefined,
            "balanceAfter": undefined,
            "extraData": null,
            "jrsdSettings": {
                "rulesDateStamped": true,
                "showRTP": true,
            },
        });
    });

    it("gets url for history based on roundId and fetch token data", async () => {
        const round = rounds[0];
        const data = await getGameHistoryDetailsImageUrl(
            brand, +round.roundId, { language: "ch-zn", spinNumber: 2 });

        expect(data.imageUrl).to.contain("url=" + dynaicDomain.domain);
        expect(data.imageUrl).to.contain("language=ch-zn");

        const tokenStr = data.imageUrl.substr(
            data.imageUrl.indexOf("data=") + 5,
            data.imageUrl.indexOf("&url=") - data.imageUrl.indexOf("data=") - 5);

        let details = await getRoundHistoryDetailsForGHApp(tokenStr);
        expect(details.length).equal(10);
        const expectedSpin = details.find(spin => spin.spinNumber === 2);

        const { insertedAt, ...cmp } = expectedSpin;

        expect(cmp).deep.eq({
            "details": {},
            "gameId": "GAME777",
            "gameCode": "CrashTestGame",
            "gameVersion": "1.1.1",
            "initSettings": {
                "description": "Game init data",
            },
            "historyInfo": {
                "historyRenderType": 1,
                "url": `http://${domainName}/simei/latest/history.html`
            },
            "roundId": +round.roundId,
            "spinNumber": 2,
            "currency": "USD",
            "type": "slot",
            "balanceAfter": undefined,
            "balanceBefore": undefined,
            "trxId": undefined,
            "bet": 0,
            "endOfRound": true,
            "isPayment": true,
            "test": false,
            "win": 0,
            "device": expectedSpin.device,
            "ts": expectedSpin.ts,
            "totalJpContribution": 1,
            "totalJpWin": 3,
            "credit": 5,
            "debit": 10,
            "playerCode": cmp.playerCode,
            "extraData": undefined,
            "jrsdSettings": {
                "rulesDateStamped": true,
                "showRTP": true,
            },
            "isHidden": null,
        });

        expect(cmp.playerCode !== undefined).deep.equals(true);

        details = await getRoundHistoryDetailsForGHApp(tokenStr, "slot");
        expect(details.length).equal(10);
        details = await getRoundHistoryDetailsForGHApp(tokenStr, "unknown");
        expect(details.length).equal(0);
    });

    it("gets url for ITG history based on roundId and fetch token data", async () => {
        const round = rounds[7];
        const data = await getGameHistoryDetailsImageUrl(
            brand, +round.roundId, { language: "ch-zn", spinNumber: 2 });

        expect(data.imageUrl).to.contain("url=" + dynaicDomain.domain);
        expect(data.imageUrl).to.contain("language=ch-zn");

        const tokenStr = data.imageUrl.substr(
            data.imageUrl.indexOf("data=") + 5,
            data.imageUrl.indexOf("&url=") - data.imageUrl.indexOf("data=") - 5);

        let details = await getRoundHistoryDetailsForGHApp(tokenStr);
        expect(details.length).equal(3);
        const expectedSpin = details.find(spin => spin.spinNumber === 2);

        const { insertedAt, ...cmp } = expectedSpin;

        expect(cmp).deep.eq({
            "details": {},
            "gameId": "itg_luckyforestcasino",
            "gameCode": "itg_luckyforestcasino",
            "gameVersion": "1.1.1",
            "initSettings": {
                "description": "Game init data",
            },
            "historyInfo": {
                "historyRenderType": 3,
                "url": `${config.itgBaseUrl}/history/${game3.providerGameCode}/?presenter=1&locale=en`
            },
            "roundId": +round.roundId,
            "spinNumber": 2,
            "currency": "USD",
            "type": "slot",
            "balanceAfter": undefined,
            "balanceBefore": undefined,
            "trxId": undefined,
            "bet": 0,
            "endOfRound": true,
            "isPayment": true,
            "test": false,
            "win": 0,
            "device": expectedSpin.device,
            "ts": expectedSpin.ts,
            "totalJpContribution": 1,
            "totalJpWin": 3,
            "credit": 5,
            "debit": 10,
            "playerCode": cmp.playerCode,
            "extraData": undefined,
            "jrsdSettings": {
                "rulesDateStamped": true,
                "showRTP": true,
            },
            "isHidden": null,
        });

        expect(cmp.playerCode !== undefined).deep.equals(true);

        details = await getRoundHistoryDetailsForGHApp(tokenStr, "slot");
        expect(details.length).equal(3);
        details = await getRoundHistoryDetailsForGHApp(tokenStr, "unknown");
        expect(details.length).equal(0);
    });

    it("Find round info by visualization token", async () => {
        const [round] = rounds;
        const { token } = await generateGameHistoryVisualizationToken({ eId: brand.id, rId: +round.roundId });

        const roundInfo = await getRoundInfoForGHApp(token);

        expect(roundInfo.roundId).to.be.deep.equal(+round.roundId);

    });
});
