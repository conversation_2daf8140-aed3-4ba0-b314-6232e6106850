import { UrlPlaceholders, UrlPlaceholdersParams } from "../../../skywind/services/gameUrl/urlPlaceholders";
import { expect } from "chai";

describe(UrlPlaceholders.name, () => {
    describe("replace", () => {
        it("should replace placeholders with corresponding values", () => {
            const input = "https://example.com/{startGameToken}/{lang}/{playmode}/{currency}/{lobby}/{cashier}/{externalGameUrl}/{wrapperVersion}";
            const params: UrlPlaceholdersParams = {
                startGameToken: "12345",
                language: "en",
                playMode: "real",
                currency: "USD",
                lobby: "lobby",
                cashier: "cashier",
                externalGameUrl: "externalGameUrl.com",
                wrapperLauncherVersion: "1.0.0"
            };

            const result = UrlPlaceholders.replace(input, params);

            expect(result).to.equal("https://example.com/12345/en/real/USD/lobby/cashier/externalGameUrl.com/1.0.0");
        });

        it("should throw an error if required params are missing", () => {
            const input = "https://example.com/{staticDomain}";
            const params: UrlPlaceholdersParams = {};

            expect(() => UrlPlaceholders.replace(input, params)).to.throw();
        });

        it("should encode ITG parameters correctly", () => {
            const input = "https://example.com?gs={itgDynamicDomain}";
            const params = {
                dynamicDomain: "dynamic.example.com",
            };

            const result = UrlPlaceholders.replace(input, params);

            expect(result).to.eq("https://example.com?gs=MUluLJ1pLy5leTStcTxlYmAvbD==");
        });

        it("should replace all placeholders in the input string", () => {
            const input = "https://example.com/{currency}";
            const params: UrlPlaceholdersParams = {
                currency: "USD",
            };

            const result = UrlPlaceholders.replace(input, params);

            expect(result).to.equal("https://example.com/USD");
        });

        it("should handle decoding of parameters after replacement", () => {
            const input = "https://example.com/{lang}";
            const params: UrlPlaceholdersParams = {
                language: encodeURIComponent("fr-FR"),
            };

            const result = UrlPlaceholders.replace(input, params);

            expect(result).to.equal("https://example.com/fr-FR");
        });
    });

    describe("mergeAndReplace", () => {
        it("should merge and replace placeholders from multiple parameter sets", () => {
            const paramsToMerge = [
                { url1: "https://example.com/{startGameToken}" },
                { url2: "https://example.com/{lang}" },
            ];

            const urlParams: any = {
                startGameToken: "abc123",
                language: "fr",
            };

            const result = UrlPlaceholders.mergeAndReplace(paramsToMerge, urlParams);

            expect(result).to.deep.equal({
                url1: "https://example.com/abc123",
                url2: "https://example.com/fr",
            });
        });

        it("should encode parameters with url", () => {
            const lobby = "https://domain.zone/path/path?page=https%3A%2F%2Fwww.google.com";
            const result = UrlPlaceholders.mergeAndReplace(
                [{ lobby }],
                { lobby },
                { parametersWithUrl: "lobby" }
            );
            expect(result).to.deep.equal({ lobby });
        });

        it("should encode parameters without url", () => {
            const lobby = "https://lobby-test.com?a=b&c=d";
            const result = UrlPlaceholders.mergeAndReplace(
                [{ lobby }],
                { lobby },
                { parametersWithUrl: "lobby" }
            );
            expect(result).to.deep.equal({ lobby });
        });

        it("should encode parameters with /", () => {
            const lobby = "https://lobby-test.com/?a=b&c=d";
            const result = UrlPlaceholders.mergeAndReplace(
                [{ lobby }],
                { lobby },
                { parametersWithUrl: "lobby" }
            );
            expect(result).to.deep.equal({ lobby });
        });

        it("should merge urlParams.modules", () => {
            const paramsToMerge = [
                { modules: "b365,swmp_pause" },
                { modules: "balance" },
            ];

            const urlParams: any = {
                startGameToken: "abc123",
                language: "fr"
            };

            const result = UrlPlaceholders.mergeAndReplace(paramsToMerge, urlParams);

            expect(result).to.deep.equal({
                modules: "balance,b365,swmp_pause"
            });
        });
    });

    describe("decodeParameter", () => {
        it("should decode a URL parameter correctly", () => {
            const encoded = encodeURIComponent("https://example.com");

            // @ts-ignore
            const result = UrlPlaceholders.decodeParameter(encoded);

            expect(result).to.equal("https://example.com");
        });
    });

    describe("getModuleValue", () => {
        it("should merge and deduplicate module values", () => {
            // @ts-ignore
            const result = UrlPlaceholders.getModuleValue("module1,module2", "module2,module3");

            expect(result).to.equal("module2,module3,module1");
        });

        it("should handle empty initial module value", () => {
            // @ts-ignore
            const result = UrlPlaceholders.getModuleValue("", "module1,module2");

            expect(result).to.equal("module1,module2");
        });
    });
});
