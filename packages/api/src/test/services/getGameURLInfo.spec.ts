import { expect, should, use } from "chai";
import * as sinon from "sinon";
import { FACTORY } from "../factories/common";
import { factory } from "factory-girl";
import { EntityGame, Game, GameDescription, PlayerGameURLInfo } from "../../skywind/entities/game";

import {
    MerchantGameInitRequest,
    MerchantGameURLInfo,
    MerchantStartGameTokenData,
    PlayMode,
    StartGameTokenData
} from "@skywind-group/sw-wallet-adapter-core";
import * as request from "request";

import * as settingsService from "../../skywind/services/settings";
import * as entityDomainService from "../../skywind/services/entityDomainService";
import { getBrandPlayerValidator } from "../../skywind/services/brandPlayerValidator";
import * as token from "../../skywind/utils/token";
import { verifyStartGameToken } from "../../skywind/utils/token";
import * as entityJrsdService from "../../skywind/services/entityJurisdiction";
import * as Errors from "../../skywind/errors";
import * as _ from "lodash";
import { getDeploymentGroupService } from "../../skywind/services/deploymentGroup";
import { GameProvider } from "../../skywind/entities/gameprovider";
import { get as getGameProviderModel } from "../../skywind/models/gameprovider";
import { getGameModel } from "../../skywind/models/game";
import { truncate } from "../entities/helper";
import { getGameClientVersionService } from "../../skywind/services/gameVersionService";
import { EntityHelper } from "../../skywind/services/gameUrl/entityHelper";
import { getGameURLInfo } from "../../skywind/services/gameUrl/getGameURLInfo";
import config from "../../skywind/config";
import { EmailTemplate, EntitySettings } from "../../skywind/entities/settings";
import { createHash } from "../../skywind/utils/hash";
import * as countrySourceService from "../../skywind/utils/countrySource";
import { CountrySource } from "../../skywind/utils/countrySource";
import { BrandEntity } from "../../skywind/entities/brand";
import { Merchant } from "../../skywind/entities/merchant";
import { DeploymentGroupType } from "../../skywind/entities/deploymentGroup";

const chaiAsPromise = require("chai-as-promised");
should();
use(chaiAsPromise);

async function createTestGame() {

    const gameProvider: GameProvider = {
        code: "sw",
        user: "user",
        title: "title",
        secret: "secret",
        status: "normal",
        isTest: true,
        mustStoreExtHistory: false,
    } as GameProvider;

    const provider = await getGameProviderModel().create(gameProvider);

    const game: Game = {
        code: "game_code",
        title: "Test game",
        type: "slot",
        url: "http://game.com",
        providerId: provider.get("id"),
        providerGameCode: "sw",
        status: "available",
        defaultInfo: {} as GameDescription,
        info: {},
        limits: {},
    } as Game;

    await getGameModel().create(game);
}

describe("getGameURLInfo", function () {
    // this.timeout(20000);

    const language = "en";
    const emailTemplate: EmailTemplate = {
        from: "",
        subject: "",
        html: ""
    };
    const entitySettings: EntitySettings = {
        emailTemplates: {
            passwordRecovery: emailTemplate,
            changeEmail: emailTemplate
        },
        urlParams: {
            sound_popup: "true"
        },
        gameSplashes: {
            sw_csgo: "sw_pt",
            sw_pubg: "sw_pg"
        }
    };
    const game = {
        id: 1,
        code: "GAME001",
        url: "http://gc.gaming.skywindgroup.com/mrmonkey/latest/index.html" +
            "?startGameToken={startGameToken}" +
            "&url={dynamicDomain}/casino/game2" +
            "&language={lang}",
        gameProvider: {
            code: "GamePro"
        },
        providerGameCode: "ProGame",
        status: "normal"
    } as Game;
    const gameWithLobbyAndCashierPlaceholder = {
        id: 1,
        code: "GAME001",
        url: "http://gc.gaming.skywindgroup.com/mrmonkey/latest/index.html" +
            "?language={lang}" +
            "&lobby={lobby}" +
            "&cashier={cashier}",
        gameProvider: {
            code: "GamePro"
        },
        providerGameCode: "ProGame",
        status: "normal"
    } as Game;
    const entityGame = {
        id: 1,
        entityId: 1,
        gameId: 1,
        parentEntityGameId: 1,
        game,
        isLiveGame(): boolean {
            return false;
        }
    } as EntityGame;
    const payload = {
        ip: "q1t2",
        playMode: PlayMode.FUN,
        language: "en"
    };
    const dynamicDomain = {
        domain: "http://localhost:4000"
    };
    const dynamicEntityDomainServiceFake = {
        get() {
            return dynamicDomain;
        }
    };
    const staticEntityDomainServiceFake = {
        get() {
            return undefined;
        }
    };
    const entityJrsdServiceFake = {
        findOne() {
            return undefined;
        },
        findAll() {
            return [];
        }
    };
    const merchantRequest = {
        merchantType: "someType",
        merchantCode: "MERCHANT001",
        gameCode: "GAME001",
        playmode: PlayMode.FUN,
        ticket: "TICKET",
        language: "en"
    } as MerchantGameInitRequest;
    const postFake = (options, cb) => {
        const incomingMessage = {
            method: "get",
            statusCode: 200
        };
        const body = {
            cust_session_id: "SESSION001",
            cust_id: "ID001",
            error_code: 0,
            language: "en"
        };
        cb(undefined, incomingMessage, body);
    };

    const playerToken = "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9" +
        ".eyJwbGF5ZXJDb2RlIjoicGxheWVyMTU2MzE4MjMzNjkzMiIsImJyYW5kSWQiOjIsImdhbWVDb2RlIjoiR0FNRT" +
        "EiLCJwcm92aWRlckNvZGUiOiJQUiIsInByb3ZpZGVyR2FtZUNvZGUiOiJzd19tcm1ua3kiLCJjdXJyZW5jeSI6I" +
        "lVTRCIsInBsYXltb2RlIjoiZnVuIiwiZW52SWQiOiJnYyIsImlhdCI6MTU2MzE4MjYwMiwiZXhwIjoxNTYzMTg5" +
        "ODAyLCJpc3MiOiJza3l3aW5kZ3JvdXAifQ" +
        ".dHn7I6GJUs701jeVZZtePT1I-kjTo6PitGlPk87vYBfitM5gSr1lqFet_066o4xAuHfgaB82dPv2Mme7P0ON4g";

    let getEntitySettingsStub: sinon.SinonStub;
    let getEntityDomainServiceStub: sinon.SinonStub;
    let validateBonusCoinsAvailableStub: sinon.SinonStub;
    let generateStartGameTokenStub: sinon.SinonStub;
    let verifyStartGameTokenStub: sinon.SinonStub;
    let getAvailableLanguageStub: sinon.SinonStub;
    let getEntityJurisdictionServiceStub: sinon.SinonStub;
    let getIpCountrySourceStub: sinon.SinonStub;
    let findOneStub: sinon.SinonStub;
    let findAllStub: sinon.SinonStub;
    let postStub: sinon.SinonStub;

    before(async () => {
        await truncate();
        getEntitySettingsStub = sinon.stub(settingsService, "getEntitySettings");
        getEntityDomainServiceStub = sinon.stub(entityDomainService, "getEntityDomainService");
        validateBonusCoinsAvailableStub = sinon.stub(getBrandPlayerValidator(), "validateBonusCoinsAvailable");
        generateStartGameTokenStub = sinon.stub(token, "generateStartGameToken");
        verifyStartGameTokenStub = sinon.stub(token, "verifyStartGameToken");
        getAvailableLanguageStub = sinon.stub(EntityHelper, "getAvailableLanguage");
        getEntityJurisdictionServiceStub = sinon.stub(entityJrsdService, "getEntityJurisdictionService");
        getIpCountrySourceStub = sinon.stub(countrySourceService, "getIpCountrySource");
        findOneStub = sinon.stub(entityJrsdServiceFake, "findOne");
        findAllStub = sinon.stub(entityJrsdServiceFake, "findAll");
        postStub = sinon.stub(request, "post").callsFake(postFake as any);
    });

    after(async () => {
        getEntitySettingsStub.restore();
        getEntityDomainServiceStub.restore();
        validateBonusCoinsAvailableStub.restore();
        generateStartGameTokenStub.restore();
        verifyStartGameTokenStub.restore();
        getAvailableLanguageStub.restore();
        getEntityJurisdictionServiceStub.restore();
        getIpCountrySourceStub.restore();
        findOneStub.restore();
        findAllStub.restore();
        postStub.restore();
    });

    describe("GetGameUrlWithGameClientVersionPlaceholder", async () => {

        const depGame = {
            id: 1,
            code: "GAME001",
            url: "http://gc.gaming.skywindgroup.com/mrmonkey/{clientVersion}/index.html" +
                "?startGameToken={startGameToken}&url={dynamicDomain}/casino/game2&language={lang}",
            gameProvider: {
                code: "GamePro"
            },
            providerGameCode: "ProGame",
            status: "normal",
            defaultClientVersion: "1.2.3"
        } as Game;
        const depEntityGame = {
            id: 1,
            entityId: 1,
            gameId: 1,
            parentEntityGameId: 1,
            game: depGame,
            isLiveGame: () => false
        } as EntityGame;

        before(async () => {
            await truncate();
        });

        beforeEach(async () => {
            getEntitySettingsStub.reset();
            getEntityDomainServiceStub.reset();
            validateBonusCoinsAvailableStub.reset();
            generateStartGameTokenStub.reset();
            verifyStartGameTokenStub.reset();
            getAvailableLanguageStub.reset();
            getEntityJurisdictionServiceStub.reset();
            getIpCountrySourceStub.returns(Promise.resolve({}));
            findOneStub.reset();
        });

        it("game client version with placeholder", async () => {
            const brand = await factory.create(FACTORY.BRAND);
            const player = await factory.create(FACTORY.PLAYER);

            getEntitySettingsStub.returns(entitySettings);
            getEntityDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(1).returns(staticEntityDomainServiceFake);
            validateBonusCoinsAvailableStub.returns(true);
            generateStartGameTokenStub.returns(playerToken);
            getAvailableLanguageStub.returns(language);
            getEntityJurisdictionServiceStub.returns(entityJrsdServiceFake);

            findAllStub.returns([]);

            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                entityGame: depEntityGame,
                brand: brand,
                entitySettings: entitySettings,
                disableLauncher: true,
                merchant: null,
                player: player,
                isLobby: false,
                request: payload
            });

            const gameUrl = "http://gc.gaming.skywindgroup.com/mrmonkey/" +
                `1.2.3/index.html?startGameToken=${playerToken}` +
                "&url=http%3A%2F%2Flocalhost%3A4000%2Fcasino%2Fgame2&language=en" +
                "&sound_popup=true";
            const expectedResponse = {
                token: playerToken,
                url: gameUrl,
                "currency": "USD"
            } as PlayerGameURLInfo;
            expect(actualResponse).to.deep.equal(expectedResponse);
        });

        it("game client version with placeholder, no default client version", async () => {
            const brand = await factory.create(FACTORY.BRAND);
            const player = await factory.create(FACTORY.PLAYER);

            getEntitySettingsStub.returns(entitySettings);
            getEntityDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(1).returns(staticEntityDomainServiceFake);
            validateBonusCoinsAvailableStub.returns(true);
            generateStartGameTokenStub.returns(playerToken);
            getAvailableLanguageStub.returns(language);
            getEntityJurisdictionServiceStub.returns(entityJrsdServiceFake);

            findAllStub.returns([]);

            const depEntityGame2 = _.cloneDeep(depEntityGame);
            depEntityGame2.game.defaultClientVersion = undefined;

            await getGameURLInfo({
                entityGame: depEntityGame2,
                brand: brand,
                entitySettings: entitySettings,
                disableLauncher: true,
                merchant: null,
                player: player,
                request: payload
            }).should.eventually.rejectedWith(Errors.ValidationError);
        });

        it("game client version with placeholder with depl. group", async () => {
            const brand = await factory.create(FACTORY.BRAND);
            const player = await factory.create(FACTORY.PLAYER);

            getEntitySettingsStub.returns(entitySettings);
            getEntityDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(1).returns(staticEntityDomainServiceFake);
            validateBonusCoinsAvailableStub.returns(true);
            generateStartGameTokenStub.returns(playerToken);
            getAvailableLanguageStub.returns(language);
            getEntityJurisdictionServiceStub.returns(entityJrsdServiceFake);
            findAllStub.returns([]);

            await createTestGame();

            const depEntityGame2 = _.cloneDeep(depEntityGame);
            depEntityGame2.game.defaultClientVersion = undefined;
            depEntityGame2.game.code = "game_code";

            const depGroup = await getDeploymentGroupService()
                .addDeploymentGroup("italy", DeploymentGroupType.GAME, "italy");
            await getGameClientVersionService().updateGameClientVersionPerRoute("game_code", {
                "italy": { clientVersion: "3.2.1", clientFeatures: {} }
            });

            depEntityGame2.game.deploymentGroupId = depGroup.id;

            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                entityGame: depEntityGame2,
                brand: brand,
                entitySettings: entitySettings,
                disableLauncher: true,
                merchant: null,
                player: player,
                request: payload
            });

            const gameUrl = "http://gc.gaming.skywindgroup.com/mrmonkey/" +
                `3.2.1/index.html?startGameToken=${playerToken}` +
                "&url=http%3A%2F%2Flocalhost%3A4000%2Fcasino%2Fgame2&language=en" +
                "&sound_popup=true";
            const expectedResponse = {
                token: playerToken,
                url: gameUrl,
                "currency": "USD"
            } as PlayerGameURLInfo;

            expect(actualResponse).to.deep.equal(expectedResponse);
        });

    });

    describe("getGameUrl", async () => {
        let params;
        let brand;
        let player;

        beforeEach(async () => {
            brand = await factory.create(FACTORY.BRAND);
            player = await factory.create(FACTORY.PLAYER);
            params = {
                entityGame,
                brand,
                entitySettings,
                disableLauncher: true,
                merchant: null,
                player,
                request: payload
            };

            getEntitySettingsStub.reset();
            getEntityDomainServiceStub.reset();
            validateBonusCoinsAvailableStub.reset();
            generateStartGameTokenStub.reset();
            getAvailableLanguageStub.reset();
            getEntityJurisdictionServiceStub.reset();
            findOneStub.reset();

            getEntitySettingsStub.returns(entitySettings);
            getEntityDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(1).returns(staticEntityDomainServiceFake);
            validateBonusCoinsAvailableStub.returns(true);
            generateStartGameTokenStub.returns(playerToken);
            getAvailableLanguageStub.returns(language);
            getEntityJurisdictionServiceStub.returns(entityJrsdServiceFake);

            // MEMO: this argument determines whether the sound_popup exists
            findAllStub.returns([]);
            getIpCountrySourceStub.returns(Promise.resolve({}));
        });

        it("should return sound pop-up feature in the game url, when jurisdiction doesn't exist", async () => {
            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                ...params,
                request: { ...payload, referrer: "google.com" }
            });
            expect(actualResponse).to.deep.equal({
                token: playerToken,
                url: "http://gc.gaming.skywindgroup.com/mrmonkey/latest/index.html" +
                    `?startGameToken=${playerToken}` +
                    "&url=http%3A%2F%2Flocalhost%3A4000%2Fcasino%2Fgame2" +
                    "&language=en" +
                    "&sound_popup=true",
                "currency": "USD"
            });
            expect(generateStartGameTokenStub.args[0][0].referrer).to.equal("google.com");
        });

        it("should not return sound pop-up feature in the game url, when jurisdiction exist", async () => {
            // MEMO: this argument determines whether the sound_popup exists
            findAllStub.returns([{}]);

            const actualResponse: PlayerGameURLInfo = await getGameURLInfo(params);
            expect(actualResponse).to.deep.equal({
                token: playerToken,
                url: "http://gc.gaming.skywindgroup.com/mrmonkey/latest/index.html" +
                    `?startGameToken=${playerToken}` +
                    "&url=http%3A%2F%2Flocalhost%3A4000%2Fcasino%2Fgame2" +
                    "&language=en",
                "currency": "USD"
            });
        });

        it("should return history_url in the game url", async () => {
            const testEntitySettings = {
                ...entitySettings,
                urlParams: {
                    ...entitySettings.urlParams,
                    history_url: "http://gameserver.skywindgroup.com/history"
                }
            };
            getEntitySettingsStub.returns(testEntitySettings);

            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                ...params,
                entitySettings: testEntitySettings,
                request: { ...payload, referrer: "google.com" }
            });
            expect(actualResponse).to.deep.equal({
                token: playerToken,
                url: "http://gc.gaming.skywindgroup.com/mrmonkey/latest/index.html" +
                    `?startGameToken=${playerToken}` +
                    "&url=http%3A%2F%2Flocalhost%3A4000%2Fcasino%2Fgame2" +
                    "&language=en" +
                    "&sound_popup=true" +
                    "&history_url=http%3A%2F%2Fgameserver.skywindgroup.com%2Fhistory",
                "currency": "USD"
            });
            expect(generateStartGameTokenStub.args[0][0].referrer).to.equal("google.com");
        });

        it("should return history2_url in the game url", async () => {
            getEntityJurisdictionServiceStub.returns({
                findOne() {
                    return undefined;
                },
                findAll() {
                    return ["test"];
                }
            });

            const testEntitySettings = {
                ...entitySettings,
                urlParams: {
                    ...entitySettings.urlParams,
                    history_url: "http://gameserver.skywindgroup.com/history",
                    history2_url: "http://gameserver.skywindgroup.com/history2"
                }
            };
            getEntitySettingsStub.returns(testEntitySettings);

            const entityGame2 = Object.assign({}, entityGame);
            entityGame2.game.historyRenderType = 3;

            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                ...params,
                entityGame: entityGame2,
                entitySettings: testEntitySettings,
                request: { ...payload, referrer: "google.com" }
            });
            expect(actualResponse).to.deep.equal({
                token: playerToken,
                url: "http://gc.gaming.skywindgroup.com/mrmonkey/latest/index.html" +
                    `?startGameToken=${playerToken}` +
                    "&url=http%3A%2F%2Flocalhost%3A4000%2Fcasino%2Fgame2" +
                    "&language=en" +
                    "&history_url=http%3A%2F%2Fgameserver.skywindgroup.com%2Fhistory2",
                "currency": "USD"
            });
            expect(generateStartGameTokenStub.args[0][0].referrer).to.equal("google.com");
        });

        it("should return empty modules if null", async () => {
            const testEntitySettings = {
                ...entitySettings,
                urlParams: {
                    ...entitySettings.urlParams,
                    modules: null
                }
            };
            getEntitySettingsStub.returns(testEntitySettings);

            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                ...params,
                entitySettings: testEntitySettings,
                request: { ...payload, referrer: "google.com" }
            });
            expect(actualResponse).to.deep.equal({
                token: playerToken,
                url: "http://gc.gaming.skywindgroup.com/mrmonkey/latest/index.html" +
                    `?startGameToken=${playerToken}` +
                    "&url=http%3A%2F%2Flocalhost%3A4000%2Fcasino%2Fgame2" +
                    "&language=en" +
                    "&sound_popup=true" +
                    "&modules=",
                "currency": "USD"
            });
            expect(generateStartGameTokenStub.args[0][0].referrer).to.equal("google.com");
        });

        it("externalGameId", async () => {
            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                ...params,
                entityGame: {
                    ...entityGame,
                    externalGameId: "777",
                    game: {
                        ...entityGame.game,
                        url: "http://jammymonkey.com/games/external_id/{externalGameId}?language={lang}"
                    }
                }
            });
            expect(actualResponse).to.deep.equal({
                token: playerToken,
                url: "http://jammymonkey.com/games/external_id/777?" +
                    "language=en" +
                    "&sound_popup=true",
                "currency": "USD"
            });
        });

        it.skip("externalGameId notfound", async () => {
            try {
                await getGameURLInfo({
                    ...params,
                    entityGame: {
                        ...entityGame,
                        game: {
                            ...entityGame.game,
                            url: "http://jammymonkey.com/games/external_id/{externalGameId}?language={lang}"
                        }
                    }
                });
                expect.fail("externalGameId must undefined");
            } catch (err) {
                expect(err).instanceOf(Errors.ExternalGameIdNotDefined);
            }
        });
    });

    describe("getMerchantUrl", async () => {
        beforeEach(async () => {
            getEntitySettingsStub.reset();
            getEntityDomainServiceStub.reset();
            validateBonusCoinsAvailableStub.reset();
            generateStartGameTokenStub.reset();
            getAvailableLanguageStub.reset();
            getEntityJurisdictionServiceStub.reset();
            findOneStub.reset();
            getIpCountrySourceStub.returns(Promise.resolve({}));
        });

        it("should return sound pop-up feature in the game url, when jurisdiction doesn't exist", async () => {
            const brand = await factory.create(FACTORY.BRAND);
            const merchant = await factory.create(FACTORY.MERCHANT);
            const player = await factory.create(FACTORY.PLAYER);

            getEntitySettingsStub.returns(entitySettings);
            // MEMO: this function is called by the ipadapter for the first time
            getEntityDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(1).returns(staticEntityDomainServiceFake);
            validateBonusCoinsAvailableStub.returns(true);
            generateStartGameTokenStub.returns(playerToken);
            getAvailableLanguageStub.returns(language);
            getEntityJurisdictionServiceStub.returns(entityJrsdServiceFake);

            // MEMO: this argument determines whether the sound_popup exists
            findAllStub.returns([]);

            const gameUrl = "http://gc.gaming.skywindgroup.com/mrmonkey/" +
                `latest/index.html?startGameToken=${playerToken}` +
                "&url=http%3A%2F%2Flocalhost%3A4000%2Fcasino%2Fgame2&language=en" +
                "&sound_popup=true&playmode=fun";
            const expectedResponse = {
                token: playerToken,
                url: gameUrl,
                currency: "USD"
            } as PlayerGameURLInfo;

            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                entityGame: entityGame,
                brand: brand,
                entitySettings: entitySettings,
                disableLauncher: true,
                merchant: merchant,
                player: player,
                request: { ...merchantRequest, ...payload } as MerchantGameInitRequest
            });
            expect(actualResponse).to.deep.equal(expectedResponse);
        });

        it("should not return sound pop-up feature in the game url, when jurisdiction exists", async () => {
            const brand = await factory.create(FACTORY.BRAND);
            const merchant = await factory.create(FACTORY.MERCHANT);
            const player = await factory.create(FACTORY.PLAYER);

            getEntitySettingsStub.returns(entitySettings);
            // MEMO: this function is called by the ipadapter for the first time
            getEntityDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(1).returns(staticEntityDomainServiceFake);
            validateBonusCoinsAvailableStub.returns(true);
            generateStartGameTokenStub.returns(playerToken);
            getAvailableLanguageStub.returns(language);
            getEntityJurisdictionServiceStub.returns(entityJrsdServiceFake);

            // MEMO: this argument determines whether the sound_popup exists
            findAllStub.returns([{}]);

            const gameUrl = "http://gc.gaming.skywindgroup.com/mrmonkey/" +
                `latest/index.html?startGameToken=${playerToken}` +
                "&url=http%3A%2F%2Flocalhost%3A4000%2Fcasino%2Fgame2&language=en" +
                "&playmode=fun";
            const expectedResponse = {
                token: playerToken,
                url: gameUrl,
                currency: "USD"
            } as PlayerGameURLInfo;

            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                entityGame: entityGame,
                brand: brand,
                entitySettings: entitySettings,
                disableLauncher: true,
                merchant: merchant,
                player: player,
                request: { ...merchantRequest, ...payload } as MerchantGameInitRequest
            });
            expect(actualResponse).to.deep.equal(expectedResponse);
        });

        it("should return error when currency does not match", async () => {
            const brand = await factory.create(FACTORY.BRAND);
            const merchant = await factory.create(FACTORY.MERCHANT);
            const player = await factory.create(FACTORY.PLAYER);

            const createGameUrlStub = sinon.stub(merchant, "createGameUrl");
            const urlInfo: MerchantGameURLInfo = {
                urlParams: {},
                tokenData: {
                    country: "BY",
                    language: "en",
                    providerCode: "PROVIDER001",
                    providerGameCode: "PRGAME001",
                    playerCode: "PLAYER001",
                    gameCode: "GAME001",
                    brandId: 3255,
                    currency: "EUR",
                    merchantType: "ipm",
                    merchantCode: "MERCH001"
                }
            };
            createGameUrlStub.returns(Promise.resolve(urlInfo));

            getEntitySettingsStub.returns(entitySettings);
            getEntityDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(1).returns(staticEntityDomainServiceFake);

            await getGameURLInfo({
                entityGame: entityGame,
                brand: brand,
                entitySettings: entitySettings,
                disableLauncher: true,
                merchant: merchant,
                player: player,
                request: {
                    ...merchantRequest,
                    ...payload
                } as MerchantGameInitRequest
            }).should.eventually.rejectedWith(Errors.CurrencyNotFoundError);
        });

        it.skip("game url with entity url params", async () => {
            const brand = await factory.create(FACTORY.BRAND, {}, {
                currencies: ["CNY", "USD", "EUR", "RUB"],
                countries: ["us", "ru", "de", "IT"]
            });
            const merchant = await factory.create(FACTORY.MERCHANT);
            const player = await factory.create(FACTORY.PLAYER, {}, { currency: "RUB", country: "IT", isTest: true });

            const testEntitySettings = {
                ...entitySettings,
                urlParams: {
                    "modules": "pokerstars"
                }
            };
            getEntitySettingsStub.returns(testEntitySettings);
            // MEMO: this function is called by the ipadapter for the first time
            getEntityDomainServiceStub.onCall(1).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(2).returns(staticEntityDomainServiceFake);
            validateBonusCoinsAvailableStub.returns(true);

            generateStartGameTokenStub.restore();
            verifyStartGameTokenStub.restore();

            getAvailableLanguageStub.returns(language);
            getEntityJurisdictionServiceStub.returns({ findAll: () => [] });

            const entityGameWithUrlParams = {
                id: 1,
                entityId: 1,
                gameId: 1,
                parentEntityGameId: 1,
                game: game,
                urlParams: {
                    "modules": "balance",
                    "balance_idle": 1,
                    "balance_ping": 30
                }
            } as EntityGame;

            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                entityGame: entityGameWithUrlParams,
                brand: brand,
                entitySettings: testEntitySettings,
                disableLauncher: true,
                merchant: merchant,
                player: player,
                isLobby: true,
                request: { ...merchantRequest, ...payload, referrer: "fishki.net" } as MerchantGameInitRequest
            });
            expect(actualResponse).to.deep.equal({
                token: actualResponse.token,
                url: actualResponse.url.replace("{{playerToken}}", actualResponse.token),
                currency: "RUB"
            });

            const actualResponseToken = await verifyStartGameToken(actualResponse.token);
            expect(actualResponse.url.indexOf("balance_idle=1") > -1).eq(true);
            expect(actualResponse.url.indexOf("balance_ping=30") > -1).eq(true);
            expect(actualResponse.url.indexOf("modules=balance%2Cpokerstars") > -1).eq(true);

            expect(actualResponseToken.country).eq(player.country);
            expect(actualResponseToken.currency).eq(player.currency);
            expect(actualResponseToken.test).eq(player.isTest);

            const parsedToken: StartGameTokenData = await verifyStartGameToken(actualResponse.token);
            expect(parsedToken).deep.eq({
                "brandId": merchant.brandId,
                "country": "IT",
                "currency": "RUB",
                "gameCode": "GAME001",
                "ipmSessionId": "SESSION001",
                "language": "en",
                "licenseeId": config.itgLicenseeId,
                "merchantCode": merchant.code,
                "merchantType": merchant.type,
                "playerCode": player.code,
                "playmode": "fun",
                "providerCode": "GamePro",
                "providerGameCode": "ProGame",
                "test": true,
                "referrer": "fishki.net",
                "operatorCountry": "IT",

                exp: (parsedToken as any).exp,
                iat: (parsedToken as any).iat,
                iss: (parsedToken as any).iss
            });
        });

        it("game url with lobby url", async () => {
            const brand = await factory.create(FACTORY.BRAND, {}, {
                currencies: ["CNY", "USD", "EUR", "RUB"],
                countries: ["us", "ru", "de", "IT"]
            });
            const merchant = await factory.create(FACTORY.MERCHANT);
            const player = await factory.create(FACTORY.PLAYER, {}, { currency: "RUB", country: "IT", isTest: true });

            const testEntitySettings = {
                ...entitySettings,
                urlParams: {
                    lobby: "http://tut.by"
                }
            };
            getEntitySettingsStub.returns(testEntitySettings);
            // MEMO: this function is called by the ipadapter for the first time
            getEntityDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(1).returns(staticEntityDomainServiceFake);
            validateBonusCoinsAvailableStub.returns(true);

            generateStartGameTokenStub.restore();
            verifyStartGameTokenStub.restore();

            getAvailableLanguageStub.returns(language);
            getEntityJurisdictionServiceStub.returns({ findAll: () => [] });

            const entityGameWithUrlParams = {
                id: 1,
                entityId: 1,
                gameId: 1,
                parentEntityGameId: 1,
                game,
                isLiveGame: () => false
            } as EntityGame;

            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                entityGame: entityGameWithUrlParams,
                brand: brand,
                entitySettings: testEntitySettings,
                disableLauncher: true,
                merchant: merchant,
                player: player,
                isLobby: true,
                request: { ...merchantRequest, ...payload, referrer: "fishki.net" } as MerchantGameInitRequest
            });
            expect(actualResponse).to.deep.equal({
                token: actualResponse.token,
                url: actualResponse.url.replace("{{playerToken}}", actualResponse.token),
                currency: "RUB"
            });

            expect(actualResponse.url.indexOf("lobby=http%3A%2F%2Ftut.by") > -1).eq(true);
        });

        it.skip("game url with entity url params with disable balance param", async () => {
            const brand = await factory.create(FACTORY.BRAND, {}, {
                currencies: ["CNY", "USD", "EUR", "RUB"],
                countries: ["us", "ru", "de", "IT"]
            });
            const merchant = await factory.create(FACTORY.MERCHANT);
            const player = await factory.create(FACTORY.PLAYER, {}, { currency: "RUB", country: "IT", isTest: true });

            const testEntitySettings = {
                ...entitySettings,
                urlParams: {
                    "modules": "pokerstars,balance",
                    "balance_idle": "1",
                    "balance_ping": "30"
                }
            };
            getEntitySettingsStub.returns(testEntitySettings);
            // MEMO: this function is called by the ipmadapter for the first time
            getEntityDomainServiceStub.onCall(1).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(2).returns(staticEntityDomainServiceFake);
            validateBonusCoinsAvailableStub.returns(true);

            generateStartGameTokenStub.restore();
            verifyStartGameTokenStub.restore();

            getAvailableLanguageStub.returns(language);
            getEntityJurisdictionServiceStub.returns({ findOne: () => null, findAll: () => [null] });

            const entityGameWithUrlParams = {
                id: 1,
                entityId: 1,
                gameId: 1,
                parentEntityGameId: 1,
                game: game,
                urlParams: {
                    "modules": "keep_alive",
                    "keep_alive_idle": 5,
                    "keep_alive_ping": 900,
                    disableBalancePing: true
                },
                isLiveGame: () => false
            } as EntityGame;

            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                entityGame: entityGameWithUrlParams,
                brand: brand,
                entitySettings: testEntitySettings,
                disableLauncher: true,
                merchant: merchant,
                player: player,
                isLobby: true,
                request: { ...merchantRequest, ...payload, referrer: "fishki.net" } as MerchantGameInitRequest
            });
            expect(actualResponse).to.deep.equal({
                token: actualResponse.token,
                url: actualResponse.url.replace("{{playerToken}}", actualResponse.token),
                currency: "RUB"
            });

            const actualResponseToken = await verifyStartGameToken(actualResponse.token);
            expect(actualResponse.url.indexOf("modules=keep_alive%2Cpokerstars") > -1).eq(true);
            expect(actualResponse.url.indexOf("keep_alive_idle=5") > -1).eq(true);
            expect(actualResponse.url.indexOf("keep_alive_ping=900") > -1).eq(true);
            expect(actualResponse.url.indexOf("balance") > -1).eq(false);

            expect(actualResponseToken.country).eq(player.country);
            expect(actualResponseToken.currency).eq(player.currency);
            expect(actualResponseToken.test).eq(player.isTest);

            const parsedToken: StartGameTokenData = await verifyStartGameToken(actualResponse.token);
            expect(parsedToken).deep.eq({
                "brandId": merchant.brandId,
                "country": "IT",
                "currency": "RUB",
                "gameCode": "GAME001",
                "ipmSessionId": "SESSION001",
                "language": "en",
                "licenseeId": config.itgLicenseeId,
                "merchantCode": merchant.code,
                "merchantType": merchant.type,
                "playerCode": player.code,
                "playmode": "fun",
                "providerCode": "GamePro",
                "providerGameCode": "ProGame",
                "test": true,
                "referrer": "fishki.net",
                "operatorCountry": "IT",

                exp: (parsedToken as any).exp,
                iat: (parsedToken as any).iat,
                iss: (parsedToken as any).iss
            });
        });

        it("game url with empty cashier and lobby urls", async () => {
            const brand = await factory.create(FACTORY.BRAND, {}, {
                currencies: ["CNY", "USD", "EUR", "RUB"],
                countries: ["us", "ru", "de", "IT"]
            });
            const merchant = await factory.create(FACTORY.MERCHANT);
            const player = await factory.create(FACTORY.PLAYER, {}, { currency: "EUR", country: "IT", isTest: true });

            const testEntitySettings = {
                emailTemplates: entitySettings.emailTemplates
            };
            getEntitySettingsStub.returns(testEntitySettings);

            getEntityDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(1).returns(staticEntityDomainServiceFake);
            // validateBonusCoinsAvailableStub.returns(true);

            generateStartGameTokenStub.restore();
            verifyStartGameTokenStub.restore();

            getAvailableLanguageStub.returns(language);
            getEntityJurisdictionServiceStub.returns({ findAll: () => [] });

            const entityGameWithUrlParams = {
                id: 1,
                entityId: 1,
                gameId: 1,
                parentEntityGameId: 1,
                game: gameWithLobbyAndCashierPlaceholder,
                isLiveGame: () => false
            } as EntityGame;

            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                entityGame: entityGameWithUrlParams,
                brand: brand,
                entitySettings: testEntitySettings,
                disableLauncher: true,
                merchant: merchant,
                player: player,
                isLobby: true,
                request: { ...merchantRequest, ...payload } as MerchantGameInitRequest
            });
            expect(actualResponse).to.deep.equal({
                token: actualResponse.token,
                url: actualResponse.url.replace("{{playerToken}}", actualResponse.token),
                currency: "EUR"
            });

            expect(actualResponse.url).to.eq("http://gc.gaming.skywindgroup.com/mrmonkey/latest/index.html?" +
                "language=en&playmode=fun");
        });

        it("game url with empty lobby url", async () => {
            const brand = await factory.create(FACTORY.BRAND, {}, {
                currencies: ["CNY", "USD", "EUR", "RUB"],
                countries: ["us", "ru", "de", "IT"]
            });
            const merchant = await factory.create(FACTORY.MERCHANT, { brandId: brand.id });
            const player = await factory.create(FACTORY.PLAYER, {}, { currency: "EUR", country: "IT", isTest: true });

            const testEntitySettings = {
                ...entitySettings,
                urlParams: { cashier: "1" }
            };
            getEntitySettingsStub.returns(testEntitySettings);

            getEntityDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(1).returns(staticEntityDomainServiceFake);
            // validateBonusCoinsAvailableStub.returns(true);

            generateStartGameTokenStub.restore();
            verifyStartGameTokenStub.restore();

            getAvailableLanguageStub.returns(language);
            getEntityJurisdictionServiceStub.returns({ findAll: () => [] });

            const entityGameWithUrlParams = {
                id: 1,
                entityId: 1,
                gameId: 1,
                parentEntityGameId: 1,
                game: gameWithLobbyAndCashierPlaceholder,
                isLiveGame: () => false
            } as EntityGame;

            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                entityGame: entityGameWithUrlParams,
                brand: brand,
                entitySettings: testEntitySettings,
                disableLauncher: true,
                merchant: merchant,
                player: player,
                isLobby: true,
                request: { ...merchantRequest, ...payload } as MerchantGameInitRequest
            });
            expect(actualResponse).to.deep.equal({
                token: actualResponse.token,
                url: actualResponse.url.replace("{{playerToken}}", actualResponse.token),
                currency: "EUR"
            });

            expect(actualResponse.url).to.eq("http://gc.gaming.skywindgroup.com/mrmonkey/latest/index.html?" +
                "language=en&cashier=1&playmode=fun");
        });

        it("game url with hashes of cashier and lobby urls in startGameToken", async () => {
            const brand = await factory.create(FACTORY.BRAND, {}, {
                currencies: ["CNY", "USD", "EUR", "RUB"],
                countries: ["us", "ru", "de", "IT"]
            });
            const merchant = await factory.create(FACTORY.MERCHANT);
            const player = await factory.create(FACTORY.PLAYER, {}, { currency: "EUR", country: "IT", isTest: true });

            const testEntitySettings = {
                emailTemplates: entitySettings.emailTemplates,
                hashLobbyAndCashierEnabled: true,
                urlParams: {
                    lobby: "lobbyUrlFromSettings",
                    cashier: "cashierUrlFromSettings"
                }
            };
            getEntitySettingsStub.returns(testEntitySettings);

            getEntityDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(1).returns(staticEntityDomainServiceFake);

            generateStartGameTokenStub.restore();
            verifyStartGameTokenStub.restore();

            getAvailableLanguageStub.returns(language);
            getEntityJurisdictionServiceStub.returns({ findAll: () => [] });

            const entityGameWithUrlParams = {
                id: 1,
                entityId: 1,
                gameId: 1,
                parentEntityGameId: 1,
                game: gameWithLobbyAndCashierPlaceholder,
                isLiveGame: () => false
            } as EntityGame;

            const lobbyForInit = {
                lobby: "lobbyUrl"
            };

            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                entityGame: entityGameWithUrlParams,
                brand: brand,
                entitySettings: testEntitySettings,
                disableLauncher: true,
                merchant: merchant,
                player: player,
                isLobby: true,
                request: { ...merchantRequest, ...payload, ...lobbyForInit } as MerchantGameInitRequest
            });
            expect(actualResponse).to.deep.equal({
                token: actualResponse.token,
                url: actualResponse.url.replace("{{playerToken}}", actualResponse.token),
                currency: "EUR"
            });

            const actualResponseToken = await verifyStartGameToken(actualResponse.token);
            expect(actualResponseToken.lb).to.eq(createHash(lobbyForInit.lobby));
            expect(actualResponseToken.csh).to.eq(createHash(testEntitySettings.urlParams.cashier));

            expect(actualResponse.url).to.eq("http://gc.gaming.skywindgroup.com/mrmonkey/latest/index.html?" +
                "language=en&lobby=lobbyUrl&cashier=cashierUrlFromSettings&playmode=fun");
        });

        it("game url with hashes of numeric values for cashier and lobby urls in startGameToken", async () => {
            const brand = await factory.create(FACTORY.BRAND, {}, {
                currencies: ["CNY", "USD", "EUR", "RUB"],
                countries: ["us", "ru", "de", "IT"]
            });
            const merchant = await factory.create(FACTORY.MERCHANT);
            const player = await factory.create(FACTORY.PLAYER, {}, { currency: "EUR", country: "IT", isTest: true });

            const testEntitySettings = {
                emailTemplates: entitySettings.emailTemplates,
                hashLobbyAndCashierEnabled: true,
                urlParams: {
                    lobby: 1,
                    cashier: 1
                }
            };
            getEntitySettingsStub.returns(testEntitySettings);

            getEntityDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(1).returns(staticEntityDomainServiceFake);

            generateStartGameTokenStub.restore();
            verifyStartGameTokenStub.restore();

            getAvailableLanguageStub.returns(language);
            getEntityJurisdictionServiceStub.returns({ findAll: () => [] });

            const entityGameWithUrlParams = {
                id: 1,
                entityId: 1,
                gameId: 1,
                parentEntityGameId: 1,
                game: gameWithLobbyAndCashierPlaceholder,
                isLiveGame: () => false
            } as EntityGame;

            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                entityGame: entityGameWithUrlParams,
                brand: brand,
                entitySettings: testEntitySettings as any,
                disableLauncher: true,
                merchant: merchant,
                player: player,
                isLobby: true,
                request: { ...merchantRequest, ...payload } as MerchantGameInitRequest
            });
            expect(actualResponse).to.deep.equal({
                token: actualResponse.token,
                url: actualResponse.url.replace("{{playerToken}}", actualResponse.token),
                currency: "EUR"
            });

            const actualResponseToken = await verifyStartGameToken(actualResponse.token);
            expect(actualResponseToken.lb).to.eq(createHash(testEntitySettings.urlParams.lobby));
            expect(actualResponseToken.csh).to.eq(createHash(testEntitySettings.urlParams.cashier));

            expect(actualResponse.url).to.eq("http://gc.gaming.skywindgroup.com/mrmonkey/latest/index.html?" +
                "language=en&lobby=1&cashier=1&playmode=fun");
        });

        it("game url with hashes of cashier and lobby urls set to zero in startGameToken", async () => {
            const brand = await factory.create(FACTORY.BRAND, {}, {
                currencies: ["CNY", "USD", "EUR", "RUB"],
                countries: ["us", "ru", "de", "IT"]
            });
            const merchant = await factory.create(FACTORY.MERCHANT);
            const player = await factory.create(FACTORY.PLAYER, {}, { currency: "EUR", country: "IT", isTest: true });

            const testEntitySettings = {
                emailTemplates: entitySettings.emailTemplates,
                hashLobbyAndCashierEnabled: true,
                urlParams: {
                    lobby: 0,
                    cashier: 0
                }
            };
            getEntitySettingsStub.returns(testEntitySettings);

            getEntityDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(1).returns(staticEntityDomainServiceFake);

            generateStartGameTokenStub.restore();
            verifyStartGameTokenStub.restore();

            getAvailableLanguageStub.returns(language);
            getEntityJurisdictionServiceStub.returns({ findAll: () => [] });

            const entityGameWithUrlParams = {
                id: 1,
                entityId: 1,
                gameId: 1,
                parentEntityGameId: 1,
                game: gameWithLobbyAndCashierPlaceholder,
                isLiveGame: () => false
            } as EntityGame;

            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                entityGame: entityGameWithUrlParams,
                brand: brand,
                entitySettings: testEntitySettings as any,
                disableLauncher: true,
                merchant: merchant,
                player: player,
                isLobby: true,
                request: { ...merchantRequest, ...payload } as MerchantGameInitRequest
            });
            expect(actualResponse).to.deep.equal({
                token: actualResponse.token,
                url: actualResponse.url.replace("{{playerToken}}", actualResponse.token),
                currency: "EUR"
            });

            const actualResponseToken = await verifyStartGameToken(actualResponse.token);
            expect(actualResponseToken.lb).to.be.undefined;
            expect(actualResponseToken.csh).to.eq(createHash(testEntitySettings.urlParams.cashier));

            expect(actualResponse.url).to.eq("http://gc.gaming.skywindgroup.com/mrmonkey/latest/index.html?" +
                "language=en&cashier=0&playmode=fun");
        });

        it("game url with static domain", async () => {
            const brand = await factory.create(FACTORY.BRAND);
            const player = await factory.create(FACTORY.PLAYER);
            const entityGame2 = _.cloneDeep(entityGame);
            entityGame2.game.url = "http://{staticDomain}/mrmonkey/latest/index.html";

            getEntitySettingsStub.returns(entitySettings);
            getEntityDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(1).returns({
                get() {
                    return { domain: "static.gaming.skywindgroup.com" };
                }
            });
            getEntityDomainServiceStub.onCall(2).returns(dynamicEntityDomainServiceFake);
            getEntityDomainServiceStub.onCall(3).returns({
                get() {
                    return { domain: "static.gaming.skywindgroup.com" };
                }
            });

            generateStartGameTokenStub.restore();
            verifyStartGameTokenStub.restore();

            getAvailableLanguageStub.returns(language);
            getEntityJurisdictionServiceStub.returns({ findAll: () => [] });

            let actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                entityGame: entityGame2,
                brand: brand,
                entitySettings: entitySettings,
                disableLauncher: true,
                merchant: null,
                player: player,
                isLobby: false,
                request: { ...merchantRequest, ...payload } as MerchantGameInitRequest
            });

            expect(actualResponse.url).to.eq("http://static.gaming.skywindgroup.com/mrmonkey/latest/index.html?" +
                "sound_popup=true&language=en");

            entityGame2.domain = "static.eg.skywindgroup.com";

            actualResponse = await getGameURLInfo({
                entityGame: entityGame2,
                brand: brand,
                entitySettings: entitySettings,
                disableLauncher: true,
                merchant: null,
                player: player,
                isLobby: false,
                request: { ...merchantRequest, ...payload } as MerchantGameInitRequest
            });

            expect(actualResponse.url).to.eq("http://static.eg.skywindgroup.com/mrmonkey/latest/index.html?"
                + "sound_popup=true&language=en");
        });

        it("game url with unknown site code", async () => {
            const brand = await factory.create(FACTORY.BRAND);
            const player = await factory.create(FACTORY.PLAYER);
            const entityGame2 = _.cloneDeep(entityGame);
            entityGame2.game.url = "http://gc.gaming.skywindgroup.com/mrmonkey/latest/index.html?siteCode={siteCode}";
            getEntitySettingsStub.returns(entitySettings);

            generateStartGameTokenStub.restore();
            verifyStartGameTokenStub.restore();

            getAvailableLanguageStub.returns(language);
            getEntityJurisdictionServiceStub.returns({ findAll: () => [] });
            getEntityDomainServiceStub.returns(dynamicEntityDomainServiceFake);

            return getGameURLInfo({
                entityGame: entityGame2,
                brand: brand,
                entitySettings: entitySettings,
                disableLauncher: true,
                merchant: null,
                player: player,
                isLobby: false,
                request: { ...merchantRequest, ...payload } as MerchantGameInitRequest
            }).should.be.rejectedWith(Errors.SiteCodeNotDefined);
        });

        it("game url with known site code", async () => {
            const brand = await factory.create(FACTORY.BRAND);
            const player = await factory.create(FACTORY.PLAYER);
            const entityGame2 = _.cloneDeep(entityGame);
            const entitySettings2 = _.cloneDeep(entitySettings);
            entityGame2.game.url = "http://gc.gaming.skywindgroup.com/mrmonkey/latest/index.html?siteCode={siteCode}";
            entitySettings2.gameProviderSiteCodes = {
                GamePro: "code1"
            };
            getEntitySettingsStub.returns(entitySettings2);

            generateStartGameTokenStub.restore();
            verifyStartGameTokenStub.restore();

            getAvailableLanguageStub.returns(language);
            getEntityJurisdictionServiceStub.returns({ findAll: () => [] });
            getEntityDomainServiceStub.returns(dynamicEntityDomainServiceFake);

            const actualResponse: PlayerGameURLInfo = await getGameURLInfo({
                entityGame: entityGame2,
                brand: brand,
                entitySettings: entitySettings2,
                disableLauncher: true,
                merchant: null,
                player: player,
                isLobby: false,
                request: { ...merchantRequest, ...payload } as MerchantGameInitRequest
            });

            expect(actualResponse.url).to.eq("http://gc.gaming.skywindgroup.com/mrmonkey/latest/index.html?"
                + "siteCode=code1&sound_popup=true&language=en");
        });

        describe("Country", async () => {
            const tokenData: MerchantStartGameTokenData = {
                language: "en",
                providerCode: "PROVIDER001",
                providerGameCode: "PRGAME001",
                playerCode: "PLAYER001",
                gameCode: "GAME001",
                brandId: 3255,
                currency: "EUR",
                merchantType: "ipm",
                merchantCode: "MERCH001"
            };

            let brand: BrandEntity;
            let merchant: Merchant;
            let createGameUrlStub: sinon.SinonStub;

            type GetGamesUrlOptions = Parameters<typeof getGameURLInfo>[0];
            let gamesUrlOptions: GetGamesUrlOptions;

            before(async () => {
                brand = await factory.create(FACTORY.BRAND, {}, {
                    defaultCountry: "US",
                    currencies: ["EUR"],
                    countries: ["US", "BY"]
                });
                merchant = await factory.create(FACTORY.MERCHANT, { brandId: brand.id });

                createGameUrlStub = sinon.stub(merchant, "createGameUrl");
            });

            after(async () => {
                postStub.restore();
            });

            beforeEach(async () => {
                config.merchantGameRestrictionsUseIp = false;
                gamesUrlOptions = {
                    entityGame,
                    brand,
                    entitySettings,
                    disableLauncher: true,
                    merchant,
                    request: {
                        ...merchantRequest,
                        ...payload
                    }
                };

                generateStartGameTokenStub.restore();
                verifyStartGameTokenStub.restore();
                createGameUrlStub.reset();
                getIpCountrySourceStub.reset();

                getEntitySettingsStub.returns(entitySettings);
                // MEMO: this function is called by the ipadapter for the first time
                getEntityDomainServiceStub.onCall(0).returns(dynamicEntityDomainServiceFake);
                getEntityDomainServiceStub.onCall(1).returns(staticEntityDomainServiceFake);
                validateBonusCoinsAvailableStub.returns(true);
                getAvailableLanguageStub.returns(language);
                getEntityJurisdictionServiceStub.returns({ findAll: () => [] });
            });

            describe("country from operator is valid", async () => {
                it("gameRestrictionsUseIp is false", async () => {
                    createGameUrlStub.returns(Promise.resolve<MerchantGameURLInfo>({
                        urlParams: {},
                        tokenData: {
                            ...tokenData,
                            country: "BY"
                        }
                    }));

                    const info = await getGameURLInfo(gamesUrlOptions);
                    const data = await verifyStartGameToken(info.token);
                    expect(data.country).to.eq("BY");
                });

                it("gameRestrictionsUseIp is true", async () => {
                    config.merchantGameRestrictionsUseIp = true;
                    createGameUrlStub.returns(Promise.resolve<MerchantGameURLInfo>({
                        urlParams: {},
                        tokenData: {
                            ...tokenData,
                            country: "BY"
                        }
                    }));
                    getIpCountrySourceStub.returns(Promise.resolve<CountrySource>({
                        whitelisted: false,
                        restricted: false,
                        reason: "",
                        code: "BY",
                        source: ""
                    }));

                    const info = await getGameURLInfo(gamesUrlOptions);
                    const data = await verifyStartGameToken(info.token);
                    expect(data.country).to.eq("BY");
                });
            });

            describe("country from operator is empty", async () => {
                beforeEach(async () => {
                    createGameUrlStub.returns(Promise.resolve<MerchantGameURLInfo>({
                        urlParams: {},
                        tokenData: {
                            ...tokenData,
                            country: undefined
                        }
                    }));
                });

                it("IP from operator is valid", async () => {
                    getIpCountrySourceStub.returns(Promise.resolve<CountrySource>({
                        whitelisted: false,
                        restricted: false,
                        reason: "",
                        code: "BY",
                        source: ""
                    }));

                    const info = await getGameURLInfo(gamesUrlOptions);
                    const data = await verifyStartGameToken(info.token);
                    expect(data.country).to.eq("BY");
                });

                it("IP from operator is whitelisted", async () => {
                    getIpCountrySourceStub.returns(Promise.resolve<CountrySource>({
                        whitelisted: true,
                        restricted: false,
                        reason: "",
                        source: ""
                    }));

                    const info = await getGameURLInfo(gamesUrlOptions);
                    const data = await verifyStartGameToken(info.token);
                    expect(data.country).to.eq("US");
                });

                it("IP from operator is empty", async () => {
                    getIpCountrySourceStub.neverCalledWith(entitySettings, undefined);

                    const info = await getGameURLInfo({
                        ...gamesUrlOptions,
                        request: {
                            ...gamesUrlOptions.request,
                            ip: undefined
                        }
                    });
                    const data = await verifyStartGameToken(info.token);
                    expect(data.country).to.eq("");
                });

                it("IP from operator is wrong", async () => {
                    getIpCountrySourceStub.throws(new Errors.UnknownIpAddress("wrong_ip"));

                    await getGameURLInfo({
                        ...gamesUrlOptions,
                        request: {
                            ...gamesUrlOptions.request,
                            ip: "wrong_ip"
                        }
                    }).should.eventually.rejectedWith(Errors.UnknownIpAddress);
                });
            });

            describe("country from operator is wrong", async () => {
                beforeEach(async () => {
                    createGameUrlStub.returns(Promise.resolve<MerchantGameURLInfo>({
                        urlParams: {},
                        tokenData: {
                            ...tokenData,
                            country: "wrong_country"
                        }
                    }));
                });

                it("IP from operator is valid", async () => {
                    getIpCountrySourceStub.returns(Promise.resolve<CountrySource>({
                        whitelisted: false,
                        restricted: false,
                        reason: "",
                        code: "BY",
                        source: ""
                    }));

                    const info = await getGameURLInfo(gamesUrlOptions);
                    const data = await verifyStartGameToken(info.token);
                    expect(data.country).to.eq("BY");
                });

                it("IP from operator is whitelisted", async () => {
                    getIpCountrySourceStub.returns(Promise.resolve<CountrySource>({
                        whitelisted: true,
                        restricted: false,
                        reason: "",
                        source: ""
                    }));

                    const info = await getGameURLInfo(gamesUrlOptions);
                    const data = await verifyStartGameToken(info.token);
                    expect(data.country).to.eq("US");
                });

                it("IP from operator is empty", async () => {
                    getIpCountrySourceStub.neverCalledWith(entitySettings, undefined);

                    const info = await getGameURLInfo({
                        ...gamesUrlOptions,
                        request: {
                            ...gamesUrlOptions.request,
                            ip: undefined
                        }
                    });
                    const data = await verifyStartGameToken(info.token);
                    expect(data.country).to.eq("");
                });

                it("IP from operator is wrong", async () => {
                    getIpCountrySourceStub.throws(new Errors.UnknownIpAddress("wrong_ip"));

                    await getGameURLInfo({
                        ...gamesUrlOptions,
                        request: {
                            ...gamesUrlOptions.request,
                            ip: "wrong_ip"
                        }
                    }).should.eventually.rejectedWith(Errors.UnknownIpAddress);
                });
            });
        });
    });
});
