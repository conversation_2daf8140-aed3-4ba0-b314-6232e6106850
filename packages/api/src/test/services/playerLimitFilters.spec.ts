import { suite, test } from "mocha-typescript";
import { expect, should, use } from "chai";
import * as chaiAsPromised from "chai-as-promised";
import { truncate } from "../entities/helper";
import { factory } from "factory-girl";
import { FACTORY } from "../factories/common";
import { EntityGame } from "../../skywind/entities/game";
import { findPlayerLimits } from "../../skywind/services/limits";
import { SinonFakeTimers, SinonStub, stub, useFakeTimers } from "sinon";
import * as Errors from "../../skywind/errors";
import * as gameLimitsCurrenciesCache from "../../skywind/cache/gameLimitsCurrencies";

should();
use(chaiAsPromised);

@suite()
class PlayerLimitFiltersSpec {
    public static entityGame: EntityGame;
    public clock: SinonFakeTimers;
    public static gameLimitsCurrenciesCacheStub: SinonStub;

    public static async before() {
        await truncate();
        PlayerLimitFiltersSpec.gameLimitsCurrenciesCacheStub = stub(gameLimitsCurrenciesCache, "getGameLimitsCurrency");
        PlayerLimitFiltersSpec.gameLimitsCurrenciesCacheStub.resolves({});
    }

    public static after() {
        PlayerLimitFiltersSpec.gameLimitsCurrenciesCacheStub.restore();
    }

    public before() {
        this.clock = useFakeTimers();
        this.clock.setSystemTime(0);
    }

    public after() {
        this.clock.restore();
    }

    @test()
    public async playerLimitsFilterSlotGame() {

        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME);
        const brand = await factory.create(FACTORY.BRAND);
        const limitFilters = {
            USD: {
                stakeMin: 2,
                winMax: 1500
            }
        };
        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id,
            limitFilters
        });

        const [result] = await findPlayerLimits(brand, entityGame, "USD");
        expect(result).to.be.deep.equal({
            maxTotalStake: 100,
            stakeAll: [2, 3, 5],
            stakeDef: 2,
            stakeMax: 5,
            stakeMin: 2,
            winMax: 1500
        });
    }

    @test()
    public async testPlayerLimitsWithRegulationMaxTotalBetLowerThanInLimits() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                limits: {
                    "USD": {
                        maxTotalStake: 200,
                        stakeAll: [0.1, 0.5, 1, 2, 3, 5, 110, 115, 130],
                        stakeDef: 130,
                        stakeMax: 130,
                        stakeMin: 0.1,
                        winMax: 2000,
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: { settings: { maxTotalStake: 100 } }
        });

        const [result] = await findPlayerLimits(brand, entityGame, "USD");
        expect(result).to.be.deep.equal({
            maxTotalStake: 114.64,
            stakeAll: [0.1, 0.5, 1, 2, 3, 5],
            stakeDef: 0.1,
            stakeMax: 5,
            stakeMin: 0.1,
            winMax: 2000,
        });
    }

    @test()
    public async testCopyLimitsFromAnotherCurrency() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                limits: {
                    "EUR": {
                        maxTotalStake: 200,
                        stakeAll: [0.1, 0.5, 1, 2, 3, 5, 110, 115, 130],
                        stakeDef: 130,
                        stakeMax: 130,
                        stakeMin: 0.1,
                        winMax: 2000,
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const [result] = await findPlayerLimits(brand, entityGame, "USD");
        expect(result).to.be.deep.equal({
            maxTotalStake: 200,
            stakeAll: [0.1, 0.5, 1, 2, 3, 5, 110, 115, 130],
            stakeDef: 130,
            stakeMax: 130,
            stakeMin: 0.1,
            winMax: 2292.94,
        });
    }

    @test()
    public async testPlayerLimitsWithRegulationMaxTotalBetGreaterThanInLimits() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                limits: {
                    "USD": {
                        maxTotalStake: 50,
                        stakeAll: [0.1, 0.5, 1, 2, 3, 5, 8, 10, 15],
                        stakeDef: 8,
                        stakeMax: 15,
                        stakeMin: 0.1,
                        winMax: 2000,
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: { settings: { maxTotalStake: 60 } }
        });

        const [result] = await findPlayerLimits(brand, entityGame, "USD");
        expect(result).to.be.deep.equal({
            maxTotalStake: 68.78,
            stakeAll: [
                0.1,
                0.5,
                1,
                2,
                3,
                5,
                8,
                10,
                15,
            ],
            stakeDef: 8,
            stakeMax: 15,
            stakeMin: 0.1,
            winMax: 2000
        });
    }

    @test()
    public async testPlayerLimitsAlignWinMaxForCurrentGameGroup() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME);
        const brand = await factory.create(FACTORY.BRAND, {}, { currencies: ["EUR", "CNY", "USD"] });
        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });
        await factory.create(FACTORY.GAME_GROUP_LIMITS, {}, {
            entityGameId: entityGame.id,
            gameId: entityGame.gameId,
            gamegroupId: gameGroup.id,
            limits: {
                "CNY": {
                    maxTotalStake: 100,
                    stakeAll: [2, 3, 5],
                    stakeDef: 2,
                    stakeMax: 5,
                    stakeMin: 2,
                },
                "EUR": {
                    maxTotalStake: 100,
                    stakeAll: [2, 3, 5],
                    stakeDef: 2,
                    stakeMax: 5,
                    stakeMin: 2,
                    winMax: 1500
                }
            }
        });

        const [result] = await findPlayerLimits(brand, entityGame, "CNY", gameGroup.name);
        expect(result).to.be.deep.equal({
            maxTotalStake: 100,
            stakeAll: [2, 3, 5],
            stakeDef: 2,
            stakeMax: 5,
            stakeMin: 2,
            winMax: 11947.68
        });
    }

    @test()
    public async testPlayerLimitsAlignWinMaxForForbiddenCurrency() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME);
        const brand = await factory.create(FACTORY.BRAND, {}, { currencies: ["EUR", "CNY", "USD", "XXX"] });
        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });
        await factory.create(FACTORY.GAME_GROUP_LIMITS, {}, {
            entityGameId: entityGame.id,
            gameId: entityGame.gameId,
            gamegroupId: gameGroup.id,
            limits: {
                "XXX": {
                    maxTotalStake: 100,
                    stakeAll: [2, 3, 5],
                    stakeDef: 2,
                    stakeMax: 5,
                    stakeMin: 2,
                },
                "EUR": {
                    maxTotalStake: 100,
                    stakeAll: [2, 3, 5],
                    stakeDef: 2,
                    stakeMax: 5,
                    stakeMin: 2,
                    winMax: 1500
                }
            }
        });

        const [result] = await findPlayerLimits(brand, entityGame, "XXX", gameGroup.name);
        expect(result).to.be.deep.equal({
            maxTotalStake: 100,
            stakeAll: [2, 3, 5],
            stakeDef: 2,
            stakeMax: 5,
            stakeMin: 2,
            winMax: 1500
        });
    }

    @test()
    public async testPlayerLimitsAlignWinMaxForCurrentGameGroupVirtualCurrency() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME);
        const brand = await factory.create(FACTORY.BRAND, {}, { currencies: ["EUR", "CNY", "USD", "BNS"] });
        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });
        await factory.create(FACTORY.GAME_GROUP_LIMITS, {}, {
            entityGameId: entityGame.id,
            gameId: entityGame.gameId,
            gamegroupId: gameGroup.id,
            limits: {
                "BNS": {
                    maxTotalStake: 100,
                    stakeAll: [2, 3, 5],
                    stakeDef: 2,
                    stakeMax: 5,
                    stakeMin: 2,
                },
                "EUR": {
                    maxTotalStake: 100,
                    stakeAll: [2, 3, 5],
                    stakeDef: 2,
                    stakeMax: 5,
                    stakeMin: 2,
                    winMax: 1500
                }
            }
        });

        const [result] = await findPlayerLimits(brand, entityGame, "BNS", gameGroup.name);
        expect(result).to.be.deep.equal({
            maxTotalStake: 100,
            stakeAll: [2, 3, 5],
            stakeDef: 2,
            stakeMax: 5,
            stakeMin: 2
        });
    }

    @test()
    public async testPlayerLimitsAlignWinMaxForCurrentGameGroupArtificialCurrency() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME);
        const brand = await factory.create(FACTORY.BRAND, {}, { currencies: ["EUR", "CNY", "USD", "IDS"] });
        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });
        await factory.create(FACTORY.GAME_GROUP_LIMITS, {}, {
            entityGameId: entityGame.id,
            gameId: entityGame.gameId,
            gamegroupId: gameGroup.id,
            limits: {
                "IDS": {
                    maxTotalStake: 100,
                    stakeAll: [2, 3, 5],
                    stakeDef: 2,
                    stakeMax: 5,
                    stakeMin: 2,
                },
                "EUR": {
                    maxTotalStake: 100,
                    stakeAll: [2, 3, 5],
                    stakeDef: 2,
                    stakeMax: 5,
                    stakeMin: 2,
                    winMax: 1000
                }
            }
        });

        const [result] = await findPlayerLimits(brand, entityGame, "IDS", gameGroup.name);
        expect(result).to.be.deep.equal({
            maxTotalStake: 100,
            stakeAll: [2, 3, 5],
            stakeDef: 2,
            stakeMax: 5,
            stakeMin: 2,
            winMax: 17458.474
        });
    }

    @test()
    public async testPlayerLimitsAlignWinMaxForCurrentGameGroupForbiddenCurrency() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME);
        const brand = await factory.create(FACTORY.BRAND, {}, { currencies: ["EUR", "CNY", "USD", "BNS", "XXX"] });
        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });
        await factory.create(FACTORY.GAME_GROUP_LIMITS, {}, {
            entityGameId: entityGame.id,
            gameId: entityGame.gameId,
            gamegroupId: gameGroup.id,
            limits: {
                "XXX": {
                    maxTotalStake: 100,
                    stakeAll: [2, 3, 5],
                    stakeDef: 2,
                    stakeMax: 5,
                    stakeMin: 2,
                },
                "EUR": {
                    maxTotalStake: 100,
                    stakeAll: [2, 3, 5],
                    stakeDef: 2,
                    stakeMax: 5,
                    stakeMin: 2,
                    winMax: 1500
                }
            }
        });

        const [result] = await findPlayerLimits(brand, entityGame, "XXX", gameGroup.name);
        expect(result).to.be.deep.equal({
            maxTotalStake: 100,
            stakeAll: [2, 3, 5],
            stakeDef: 2,
            stakeMax: 5,
            stakeMin: 2,
            winMax: 1500
        });
    }

    @test()
    public async testPlayerLimitsAlignWinMaxForCurrentGameLimits() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                limits: {
                    "CNY": {
                        maxTotalStake: 100,
                        stakeAll: [2, 3, 5],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 2,
                    },
                    "EUR": {
                        maxTotalStake: 100,
                        stakeAll: [2, 3, 5],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 2,
                        winMax: 1500
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND, {}, { currencies: ["EUR", "CNY", "USD"] });
        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });
        await factory.create(FACTORY.GAME_GROUP_LIMITS, {}, {
            entityGameId: entityGame.id,
            gameId: entityGame.gameId,
            gamegroupId: gameGroup.id,
            limits: {
                "CNY": {
                    maxTotalStake: 100,
                    stakeAll: [2, 3, 5],
                    stakeDef: 2,
                    stakeMax: 5,
                    stakeMin: 2,
                }
            }
        });

        const [result] = await findPlayerLimits(brand, entityGame, "CNY", gameGroup.name);
        expect(result).to.be.deep.equal({
            maxTotalStake: 100,
            stakeAll: [2, 3, 5],
            stakeDef: 2,
            stakeMax: 5,
            stakeMin: 2,
            winMax: 11947.68
        });
    }

    @test()
    public async testPlayerLimitsAlignWinMaxForCurrentGameLimitsWhereOverrideDefaultTrue() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                limits: {
                    "CNY": {
                        maxTotalStake: 100,
                        stakeAll: [2, 3, 5],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 2,
                    },
                    "EUR": {
                        maxTotalStake: 100,
                        stakeAll: [2, 3, 5],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 2,
                        winMax: 1500
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND, {}, { currencies: ["EUR", "CNY", "USD"] });
        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id,
            overrideDefault: true
        });
        await factory.create(FACTORY.GAME_GROUP_LIMITS, {}, {
            entityGameId: entityGame.id,
            gameId: entityGame.gameId,
            gamegroupId: gameGroup.id,
            limits: {
                "CNY": {
                    maxTotalStake: 100,
                    stakeAll: [2, 3, 5],
                    stakeDef: 2,
                    stakeMax: 5,
                    stakeMin: 2,
                }
            }
        });

        const [result] = await findPlayerLimits(brand, entityGame, "CNY", gameGroup.name);
        expect(result).to.be.deep.equal({
            maxTotalStake: 100,
            stakeAll: [2, 3, 5],
            stakeDef: 2,
            stakeMax: 5,
            stakeMin: 2,
            winMax: 11947.68
        });
    }

    @test()
    public async playerLimitsFilterTableMultiRoomGame() {
        const game = await factory.create(FACTORY.TABLE_GAME);
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, { gameId: game.id });
        const brand = await factory.create(FACTORY.BRAND);
        const limitFilters = {
            USD: {
                stakeMin: 2
            }
        };
        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id,
            limitFilters
        });

        const [result] = await findPlayerLimits(brand, entityGame, "USD");
        expect(result).to.be.deep.equal({
            low: {
                bets: {
                    color_parity_high: {
                        max: 1500,
                        min: 10
                    }
                },
                stakeDef: 2,
                stakeMin: 2,
                stakeMax: 4,
                stakeAll: [2, 3, 4],
                totalStakeMax: 5000,
                totalStakeMin: 10,
                order: 1
            },
            mid: {
                stakeDef: 2,
                stakeMin: 2,
                stakeMax: 4,
                stakeAll: [2, 3, 4],
                isDefaultRoom: true,
                order: 2
            }
        });
    }

    @test()
    public async playerLimitsFilterTableMultiRoomGameWithGameGroup() {
        const game = await factory.create(FACTORY.TABLE_GAME);
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, { gameId: game.id });
        const brand = await factory.create(FACTORY.BRAND);
        const limitFilters = {
            USD: {
                stakeMin: 2
            }
        };
        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id,
            limitFilters
        });
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });
        await factory.create(FACTORY.GAME_GROUP_LIMITS, {}, {
            entityGameId: entityGame.id,
            gameId: entityGame.gameId,
            gamegroupId: gameGroup.id,
            limits: {
                "USD": {
                    low: {
                        bets: {
                            color_parity_high: {
                                max: 150,
                                min: 1
                            }
                        },
                        stakeDef: 2,
                        stakeMin: 2,
                        stakeMax: 4,
                        stakeAll: [2, 3, 4],
                        totalStakeMax: 500,
                        totalStakeMin: 1,
                        order: 1
                    }
                }
            }
        });

        const [result] = await findPlayerLimits(brand, entityGame, "USD", gameGroup.name);
        expect(result).to.be.deep.equal({
            low: {
                bets: {
                    color_parity_high: {
                        max: 150,
                        min: 1
                    }
                },
                stakeDef: 2,
                stakeMin: 2,
                stakeMax: 4,
                stakeAll: [2, 3, 4],
                totalStakeMax: 500,
                totalStakeMin: 1,
                order: 1
            },
            mid: {
                stakeDef: 2,
                stakeMin: 2,
                stakeMax: 4,
                stakeAll: [2, 3, 4],
                isDefaultRoom: true,
                order: 2
            }
        });
    }

    @test()
    public async playerInGameGroupLimitsWithMultiRoomOverride() {
        const game = await factory.create(FACTORY.TABLE_GAME);
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, { gameId: game.id });
        const brand = await factory.create(FACTORY.BRAND);
        const limitFilters = {
            USD: {
                stakeMin: 2
            }
        };
        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id,
            limitFilters
        });
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });
        await factory.create(FACTORY.GAME_GROUP_LIMITS, {}, {
            entityGameId: entityGame.id,
            gameId: entityGame.gameId,
            gamegroupId: gameGroup.id,
            limits: {
                "USD": {
                    high: {
                        bets: {
                            color_parity_high: {
                                max: 1500,
                                min: 10
                            }
                        },
                        stakeDef: 2,
                        stakeMin: 2,
                        stakeMax: 4,
                        stakeAll: [2, 3, 4],
                        totalStakeMax: 5000,
                        totalStakeMin: 10
                    }
                }
            },
            overrideDefault: true
        });

        const [result] = await findPlayerLimits(brand, entityGame, "USD", gameGroup.name);
        expect(result).to.be.deep.equal({
            high: {
                bets: {
                    color_parity_high: {
                        max: 1500,
                        min: 10
                    }
                },
                stakeDef: 2,
                stakeMin: 2,
                stakeMax: 4,
                stakeAll: [2, 3, 4],
                totalStakeMax: 5000,
                totalStakeMin: 10
            }
        });
    }

    @test()
    public async testGameGroupFilterPriorityApply() {
        const defaultLimits = {
            USD: {
                maxTotalStake: 150,
                stakeAll: [0.1, 0.5, 1, 2, 3, 5, 10, 50, 100, 150],
                stakeDef: 1,
                stakeMax: 150,
                stakeMin: 0.1,
                winMax: 2000,
            }
        };
        const parentEntityGame1 = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                limits: defaultLimits,
                totalBetMultiplier: 1,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 }
            }
        });
        const parentEntityGame2 = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                limits: defaultLimits,
                totalBetMultiplier: 1,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: false, highestWin: 1000 }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            minTotalBet: 0.2,
            maxTotalBet: 50,
            groupId: gameGroup.get("id")
        });

        this.clock.setSystemTime(1000);
        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 100,
            groupId: gameGroup.get("id")
        });

        this.clock.setSystemTime(5000);
        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            minTotalBet: 1,
            maxTotalBet: null,
            groupId: gameGroup.get("id")
        });

        const entityGame1 = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame1.gameId,
            parentEntityGameId: parentEntityGame1.id,
            entityId: brand.id
        });
        const entityGame2 = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame2.gameId,
            parentEntityGameId: parentEntityGame2.id,
            entityId: brand.id
        });

        const [result1] = await findPlayerLimits(brand,
            entityGame1,
            "USD",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: true } as any);

        const [result2] = await findPlayerLimits(brand,
            entityGame2,
            "USD",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: true } as any);

        expect(result1).to.be.deep.equal({
            maxTotalStake: 150,
            stakeAll: [1, 2, 3, 5, 10, 50, 100, 150],
            stakeDef: 1,
            stakeMax: 150,
            stakeMin: 1,
            winMax: 2000,
        });

        expect(result2).to.be.deep.equal({
            maxTotalStake: 100,
            stakeAll: [0.1, 0.5, 1, 2, 3, 5, 10, 50, 100],
            stakeDef: 1,
            stakeMax: 100,
            stakeMin: 0.1,
            winMax: 2000,
        });
    }

    @test()
    public async testGameGroupLimitFeaturesToMaxTotalStake() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 1,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            winCapping: 1500,
            currencies: [],
            games: [],
            minTotalBet: 2,
            maxTotalBet: 5,
            defTotalBet: 2,
            groupId: gameGroup.get("id")
        });

        this.clock.setSystemTime(100);
        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            winCapping: 1200,
            currencies: [],
            games: [],
            minTotalBet: 2,
            maxTotalBet: 5,
            defTotalBet: 2,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "USD",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: false } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 100,
            stakeAll: [2, 3, 5],
            stakeDef: 2,
            stakeMax: 5,
            stakeMin: 2,
            winMax: 1200
        });
    }

    @test()
    public async testGameGroupFilterWithMaxExposure() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 1,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxExposure: 4000,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "USD",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 4,
            stakeAll: [
                0.1,
                0.5,
                1,
                2,
                3
            ],
            stakeDef: 1,
            stakeMax: 3,
            stakeMin: 0.1,
            winMax: 2000
        });
    }

    @test()
    public async testGameGroupFiltersForINR() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    "USD": {
                        maxTotalStake: 100,
                        stakeAll: [0.1, 0.5, 1, 2, 3, 5],
                        stakeDef: 1,
                        stakeMax: 5,
                        stakeMin: 0.1,
                        winMax: 2000
                    },
                    INR: {
                        "winMax": 25000000,
                        "stakeAll": [
                            0.5, 1.0, 2.0, 3.0, 4.0, 5.0, 10.0, 20.0, 30.0, 40.0, 50.0, 100.0, 150.0, 250.0,
                            400.0, 500.0, 1000.0, 1500.0
                        ],
                        "stakeDef": 10.0,
                        "stakeMax": 1500.0,
                        "stakeMin": 0.5,
                        "maxTotalStake": 13500.0
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            winCapping: 70000,
            currencies: [],
            games: [],
            minTotalBet: 3,
            maxTotalBet: 150,
            defTotalBet: 2,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "INR",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            "maxTotalStake": 7500,
            "stakeAll": [20, 30, 40, 50, 100, 150, 250, 400, 500],
            "stakeDef": 20,
            "stakeMax": 500,
            "stakeMin": 20,
            "winMax": 3500000,
        });
    }

    @test()
    public async testGameGroupFilterForEur() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    "USD": {
                        maxTotalStake: 100,
                        stakeAll: [0.1, 0.5, 1, 2, 3, 5],
                        stakeDef: 1,
                        stakeMax: 5,
                        stakeMin: 0.1,
                        winMax: 2000
                    },
                    EUR: {
                        "winMax": 500000,
                        "stakeAll": [
                            0.01, 0.02, 0.03, 0.05, 0.08, 0.1, 0.2, 0.3, 0.5, 0.8, 1.0, 2.0, 3.0,
                            5.0, 8.0, 10.0
                        ],
                        "stakeDef": 0.08,
                        "stakeMax": 10.0,
                        "stakeMin": 0.01,
                        "maxTotalStake": 250.0
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            winCapping: 70000,
            currencies: [],
            games: [],
            maxTotalBet: 200,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            "winMax": 70000,
            "stakeAll": [
                0.01, 0.02, 0.03, 0.05, 0.08, 0.1, 0.2, 0.3, 0.5, 0.8, 1.0, 2.0, 3.0,
                5.0, 8.0, 10.0
            ],
            "stakeDef": 0.08,
            "stakeMax": 10.0,
            "stakeMin": 0.01,
            "maxTotalStake": 200
        });
    }

    @test()
    public async testFilterForInheritedGameGroup() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    "USD": {
                        maxTotalStake: 100,
                        stakeAll: [0.1, 0.5, 1, 2, 3, 5],
                        stakeDef: 1,
                        stakeMax: 5,
                        stakeMin: 0.1,
                        winMax: 2000
                    },
                    EUR: {
                        "winMax": 500000,
                        "stakeAll": [1, 2, 3],
                        "stakeDef": 1,
                        "stakeMax": 2,
                        "stakeMin": 1,
                        "maxTotalStake": 250.0
                    }
                }
            }
        });

        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: 1
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            winCapping: 1000,
            currencies: [],
            games: [],
            groupId: gameGroup.get("id")
        });

        const brand = await factory.create(FACTORY.BRAND);

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const [result] = await findPlayerLimits(brand, entityGame, "EUR", gameGroup.name,
            {
                defaultGameGroup: gameGroup.get("name"),
                gameGroupsInheritance: true,
                limitFeaturesToMaxTotalStake: true
            } as any);

        expect(result).to.be.deep.equal({
            "winMax": 1000,
            "stakeAll": [1, 2],
            "stakeDef": 1,
            "stakeMax": 2,
            "stakeMin": 1,
            "maxTotalStake": 100
        });
    }

    @test()
    public async testCorrectRoundingForMaxTotalBet() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 30,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    "USD": {
                        maxTotalStake: 100,
                        stakeAll: [0.1, 0.5, 1, 2, 3, 5],
                        stakeDef: 1,
                        stakeMax: 5,
                        stakeMin: 0.1,
                        winMax: 2000
                    },
                    EUR: {
                        "winMax": 500000,
                        "stakeAll": [
                            0.01, 0.02, 0.03, 0.04, 0.05, 0.06, 0.08, 0.1, 0.2, 0.3, 0.4,
                            0.5, 0.6, 0.8, 1.0, 2.0, 3.0, 5.0, 8.0, 10.0
                        ],
                        "stakeDef": 0.05,
                        "stakeMax": 10.0,
                        "stakeMin": 0.01,
                        "maxTotalStake": 300.0
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 2,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            "maxTotalStake": 2,
            "stakeAll": [
                0.01,
                0.02,
                0.03,
                0.04,
                0.05,
                0.06
            ],
            "stakeDef": 0.05,
            "stakeMax": 0.06,
            "stakeMin": 0.01,
            "winMax": 500000
        });
    }

    @test()
    public async testLimitsForInvalidLimits() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 30,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 0.005,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await findPlayerLimits(brand, entityGame, "USD", gameGroup.name).should.rejectedWith(Errors.ValidationError);
    }

    @test()
    public async testIgnoreFiltersForInvalidLimits() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 30,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 0.005,
            ignoreInvalid: true,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "USD",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: true } as any);

        expect(result).to.be.deep.equal({
            maxTotalStake: 0.005,
            stakeAll: [0.1, 0.5, 1, 2, 3, 5],
            stakeDef: 1,
            stakeMax: 5,
            stakeMin: 0.1,
            winMax: 2000
        });
    }

    @test()
    public async testGameGroupFilterPartialApply() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 1,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            winCapping: 1200,
            currencies: [],
            games: [],
            maxTotalBet: 5,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "USD",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 5,
            stakeAll: [0.1, 0.5, 1, 2, 3, 5],
            stakeDef: 1,
            stakeMax: 5,
            stakeMin: 0.1,
            winMax: 1200
        });
    }

    @test()
    public async testGameGroupFilterWithMaxTotalBetAndMaxExposure() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 1,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            winCapping: 1200,
            currencies: [],
            games: [],
            maxTotalBet: 5,
            maxExposure: 4000,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "USD",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 4,
            stakeAll: [0.1, 0.5, 1, 2, 3],
            stakeDef: 1,
            stakeMax: 3,
            stakeMin: 0.1,
            winMax: 1200
        });
    }

    @test()
    public async testGameGroupFilterWithMaxTotalBetAndMaxExposureFromEntityGame() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 1,
                features: { decreaseMaxBetSupported: false, increaseMinBetSupported: false, highestWin: 1000 }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            winCapping: 1200,
            currencies: [],
            games: [],
            maxTotalBet: 5,
            maxExposure: 4000,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id,
            settings: {
                decreaseMaxBetSupported: true,
                increaseMinBetSupported: true
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "USD",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 4,
            stakeAll: [0.1, 0.5, 1, 2, 3],
            stakeDef: 1,
            stakeMax: 3,
            stakeMin: 0.1,
            winMax: 1200
        });
    }

    @test()
    public async testGameGroupFilterWithMaxTotalBetAndMaxExposureWithoutHighestWin() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 1,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            winCapping: 1200,
            currencies: [],
            games: [],
            maxTotalBet: 3,
            maxExposure: 4000,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "USD",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 3,
            stakeAll: [0.1, 0.5, 1, 2, 3],
            stakeDef: 1,
            stakeMax: 3,
            stakeMin: 0.1,
            winMax: 1200
        });
    }

    @test()
    public async playerLimitsFilterSlotGameDefaultGame() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME);
        const brand = await factory.create(FACTORY.BRAND);
        const limitFilters = {
            USD: {
                stakeMin: 2,
                winMax: 1500
            }
        };
        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id,
            limitFilters
        });

        const gameGroupDefault = await factory.create(FACTORY.GAME_GROUP, {}, { brandId: brand.id, isDefault: true });
        await factory.create(FACTORY.GAME_GROUP_LIMITS, {}, {
            entityGameId: entityGame.id,
            gameId: entityGame.gameId,
            gamegroupId: gameGroupDefault.id,
            limits: {
                "USD": {
                    maxTotalStake: 333,
                    stakeAll: [0.3, 0.7, 1, 2, 3, 5],
                    stakeDef: 1,
                    stakeMax: 5,
                    stakeMin: 0.3,
                    winMax: 33300
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "USD",
            undefined,
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 333,
            stakeAll: [2, 3, 5],
            stakeDef: 2,
            stakeMax: 5,
            stakeMin: 2,
            winMax: 1500
        });
    }
}
