import { expect, use } from "chai";
import * as sinon from "sinon";
import { EntityStaticDomainPoolService } from "../../skywind/services/entityStaticDomainPool";
import { BaseEntity, ChildEntity } from "../../skywind/entities/entity";
import { getStaticDomainPoolModel } from "../../skywind/models/domainPool";
import * as Errors from "../../skywind/errors";

use(require("chai-as-promised"));

const StaticDomainPoolModel = getStaticDomainPoolModel();

describe("EntityStaticDomainPoolService", () => {
    let service: EntityStaticDomainPoolService;
    let sandbox: sinon.SinonSandbox;
    let entity: BaseEntity;

    beforeEach(() => {
        sandbox = sinon.createSandbox();
        entity = {
            save: sandbox.stub(),
            staticDomainPoolId: null,
            isMaster: () => false,
            getParent: () => ({ isMaster: () => true })
        } as any as BaseEntity;
        service = new EntityStaticDomainPoolService(entity);
    });

    afterEach(() => {
        sandbox.restore();
    });

    describe("getPool", () => {
        describe("without inherited parameter", () => {
            it("should throw EntityStaticDomainPoolNotDefinedError if entity has no pool ID", async () => {
                await expect(service.getPool()).to.eventually.be.rejectedWith(Errors.EntityStaticDomainPoolNotDefinedError);
            });

            it("should throw DomainPoolNotFoundError if pool ID is invalid", async () => {
                entity.staticDomainPoolId = 1;
                sandbox.stub(StaticDomainPoolModel, "findByPk").resolves(null);

                await expect(service.getPool()).to.eventually.be.rejectedWith(Errors.DomainPoolNotFoundError);
            });

            it("should return the formatted pool data if pool is found", async () => {
                entity.staticDomainPoolId = 1;
                sandbox.stub(StaticDomainPoolModel, "findByPk").resolves({
                    toJSON: () => ({
                        id: 1,
                        name: "Test Pool",
                        domains: [
                            { id: 1, isActive: true, StaticDomainPoolItem: { isActive: true } },
                            { id: 2, isActive: false, StaticDomainPoolItem: { isActive: false } }
                        ]
                    })
                } as any);

                const result = await service.getPool();

                expect(result).to.deep.equal({
                    id: 1,
                    name: "Test Pool",
                    domains: [
                        { id: 1, isActive: true },
                        { id: 2, isActive: false }
                    ],
                    lobbyDomains: []
                });
            });
        });

        describe("with inherited=true parameter", () => {
            it("should throw EntityStaticDomainPoolNotDefinedError if entity and its parents have no pool ID", async () => {
                await expect(service.getPool(true)).to.eventually.be.rejectedWith(Errors.EntityStaticDomainPoolNotDefinedError);
            });

            it("should return the parent's pool if entity has no pool ID but parent does", async () => {
                const parentEntity = {
                    staticDomainPoolId: 2,
                    isMaster: () => true
                };

                const childEntity = {
                    staticDomainPoolId: null,
                    isMaster: () => false,
                    getParent: () => parentEntity
                } as unknown as ChildEntity;

                const childService = new EntityStaticDomainPoolService(childEntity);

                sandbox.stub(StaticDomainPoolModel, "findByPk").resolves({
                    toJSON: () => ({
                        id: 2,
                        name: "Parent Pool",
                        domains: [
                            { id: 3, isActive: true, StaticDomainPoolItem: { isActive: true } }
                        ]
                    })
                } as any);

                const result = await childService.getPool(true);

                expect(result).to.deep.equal({
                    id: 2,
                    name: "Parent Pool",
                    domains: [
                        { id: 3, isActive: true }
                    ],
                    lobbyDomains: []
                });
            });

            it("should return the entity's own pool if it has one, even with inherited=true", async () => {
                entity.staticDomainPoolId = 1;
                sandbox.stub(StaticDomainPoolModel, "findByPk").resolves({
                    toJSON: () => ({
                        id: 1,
                        name: "Own Pool",
                        domains: [
                            { id: 1, isActive: true, StaticDomainPoolItem: { isActive: true } }
                        ]
                    })
                } as any);

                const result = await service.getPool(true);

                expect(result).to.deep.equal({
                    id: 1,
                    name: "Own Pool",
                    domains: [
                        { id: 1, isActive: true }
                    ],
                    lobbyDomains: []
                });
            });

            it("should traverse multiple levels of parents to find a pool", async () => {
                const grandparentEntity = {
                    staticDomainPoolId: 3,
                    isMaster: () => true
                };

                const parentEntity = {
                    staticDomainPoolId: null,
                    isMaster: () => false,
                    getParent: () => grandparentEntity
                } as unknown as ChildEntity;

                const childEntity = {
                    staticDomainPoolId: null,
                    isMaster: () => false,
                    getParent: () => parentEntity
                } as unknown as ChildEntity;

                const childService = new EntityStaticDomainPoolService(childEntity);

                sandbox.stub(StaticDomainPoolModel, "findByPk").resolves({
                    toJSON: () => ({
                        id: 3,
                        name: "Grandparent Pool",
                        domains: [
                            { id: 5, isActive: true, StaticDomainPoolItem: { isActive: true } }
                        ]
                    })
                } as any);

                const result = await childService.getPool(true);

                expect(result).to.deep.equal({
                    id: 3,
                    name: "Grandparent Pool",
                    domains: [
                        { id: 5, isActive: true }
                    ],
                    lobbyDomains: []
                });
            });
        });
    });

    describe("addPool", () => {
        it("should throw DomainPoolNotFoundError if pool ID is invalid", async () => {
            sandbox.stub(StaticDomainPoolModel, "findByPk").resolves(null);

            await expect(service.addPool(999)).to.eventually.be.rejectedWith(Errors.DomainPoolNotFoundError);
        });

        it("should set the entity's staticDomainPoolId and save it", async () => {
            const poolData = { id: 1, name: "Test Pool", domains: [], lobbyDomains: [] };
            sandbox.stub(StaticDomainPoolModel, "findByPk").resolves({ toJSON: () => poolData } as any);

            const result = await service.addPool(1);

            expect(entity.staticDomainPoolId).to.equal(1);
            expect((entity.save as any).calledOnce).to.be.true;
            expect(result).to.deep.equal(poolData);
        });
    });

    describe("removePool", () => {
        it("should set the entity's staticDomainPoolId to null and save it", async () => {
            entity.staticDomainPoolId = 1;

            await service.removePool();

            expect(entity.staticDomainPoolId).to.be.null;
            expect((entity.save as any).calledOnce).to.be.true;
        });
    });

    describe("pickStaticDomain", () => {
        it("should return undefined if entity has no pool ID or parent with pool ID", async () => {
            const findByPkStub = sandbox.stub(StaticDomainPoolModel, "findByPk");

            const result = await service.pickStaticDomain();

            expect(result).to.be.undefined;
            expect(findByPkStub.notCalled).to.be.true;
        });

        it("should traverse to parent if entity has no staticDomainPoolId", async () => {
            const parentEntity = {
                staticDomainPoolId: 1,
                isMaster: () => false,
                getParent: () => ({ staticDomainPoolId: 1, isMaster: () => true })
            } as unknown as ChildEntity;
            sandbox.stub(StaticDomainPoolModel, "findByPk").resolves({
                toJSON: () => ({
                    id: 1,
                    domains: [
                        { id: 1, isActive: true, StaticDomainPoolItem: { isActive: true } },
                        { id: 2, isActive: false, StaticDomainPoolItem: { isActive: false } }
                    ]
                })
            } as any);

            const childService = new EntityStaticDomainPoolService(parentEntity);
            const result = await childService.pickStaticDomain();

            expect(result).to.deep.equal({ id: 1, isActive: true });
        });

        it("should filter active domains and return a random active domain", async () => {
            entity.staticDomainPoolId = 1;
            sandbox.stub(StaticDomainPoolModel, "findByPk").resolves({
                toJSON: () => ({
                    id: 1,
                    domains: [
                        { id: 1, isActive: true, StaticDomainPoolItem: { isActive: true } },
                        { id: 2, isActive: false, StaticDomainPoolItem: { isActive: false } },
                        { id: 3, isActive: true, StaticDomainPoolItem: { isActive: true } }
                    ]
                })
            } as any);

            const result = await service.pickStaticDomain();

            expect([1, 3]).to.include(result!.id);
            expect(result).to.have.property("isActive", true);
        });

        it("should return undefined if no active domains are available", async () => {
            entity.staticDomainPoolId = 1;
            sandbox.stub(StaticDomainPoolModel, "findByPk").resolves({
                toJSON: () => ({
                    id: 1,
                    domains: [
                        { id: 1, isActive: false, StaticDomainPoolItem: { isActive: false } },
                        { id: 2, isActive: false, StaticDomainPoolItem: { isActive: false } }
                    ]
                })
            } as any);

            const result = await service.pickStaticDomain();

            expect(result).to.be.undefined;
        });
    });

    describe("inherited property", () => {
        it("should set inherited=false when entity uses its own pool", async () => {
            // Setup entity with its own pool and path
            const entityWithOwnPool = {
                staticDomainPoolId: 1,
                path: "/entity/",
                isMaster: () => false
            } as any as BaseEntity;

            const service = new EntityStaticDomainPoolService(entityWithOwnPool);

            sandbox.stub(StaticDomainPoolModel, "findByPk").resolves({
                toJSON: () => ({
                    id: 1,
                    name: "Own Pool",
                    domains: [
                        { id: 1, isActive: true, StaticDomainPoolItem: { isActive: true } }
                    ]
                })
            } as any);

            const result = await service.getPool(true);

            expect(result).to.not.have.property("inherited");
        });

        it("should set inherited=true when entity inherits pool from parent", async () => {
            // Setup parent with pool
            const parentEntity = {
                staticDomainPoolId: 2,
                path: "/parent/",
                isMaster: () => true
            };

            // Setup child without pool
            const childEntity = {
                staticDomainPoolId: null,
                path: "/parent/child/",
                isMaster: () => false,
                getParent: () => parentEntity
            } as unknown as ChildEntity;

            const childService = new EntityStaticDomainPoolService(childEntity);

            sandbox.stub(StaticDomainPoolModel, "findByPk").resolves({
                toJSON: () => ({
                    id: 2,
                    name: "Parent Pool",
                    domains: [
                        { id: 3, isActive: true, StaticDomainPoolItem: { isActive: true } }
                    ]
                })
            } as any);

            const result = await childService.getPool(true);

            expect(result).to.have.property("inherited", true);
        });

        it("should set inherited=true when entity inherits pool from grandparent", async () => {
            // Setup grandparent with pool
            const grandparentEntity = {
                staticDomainPoolId: 3,
                path: "/grandparent/",
                isMaster: () => true
            };

            // Setup parent without pool
            const parentEntity = {
                staticDomainPoolId: null,
                path: "/grandparent/parent/",
                isMaster: () => false,
                getParent: () => grandparentEntity
            } as unknown as ChildEntity;

            // Setup child without pool
            const childEntity = {
                staticDomainPoolId: null,
                path: "/grandparent/parent/child/",
                isMaster: () => false,
                getParent: () => parentEntity
            } as unknown as ChildEntity;

            const childService = new EntityStaticDomainPoolService(childEntity);

            sandbox.stub(StaticDomainPoolModel, "findByPk").resolves({
                toJSON: () => ({
                    id: 3,
                    name: "Grandparent Pool",
                    domains: [
                        { id: 5, isActive: true, StaticDomainPoolItem: { isActive: true } }
                    ]
                })
            } as any);

            const result = await childService.getPool(true);

            expect(result).to.have.property("inherited", true);
        });
    });
});
