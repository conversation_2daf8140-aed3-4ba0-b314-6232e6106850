import * as brandService from "../../skywind/services/brand";
import { mock, SinonStub, stub } from "sinon";
import {
    testAnonymousPlayFacadeWithMockedSettingsService as AnonymousPlayService,
    testPlayFacade as PlayService
} from "../testPlayFacade";
import { getTransactionInfo, verifyGameToken as verifyPlayGameToken } from "../../skywind/services/playService";
import * as Errors from "../../skywind/errors";
import {
    AccountPropertiesFilter,
    AccountProperty,
    ONLY_INTERNAL_BALANCE_FILTER,
    OPERATION_ID,
    PLAYER,
    PlayerWalletImpl,
    WalletErrors,
    WalletFacade
} from "@skywind-group/sw-management-wallet";
import { expect, should, use } from "chai";
import { get as getPaymentModel } from "../../skywind/models/payment";
import { complexStructure, createComplexStructure, truncate, withTransaction } from "../entities/helper";
import { BaseEntity, ENTITY_TYPE } from "../../skywind/entities/entity";
import * as EntityService from "../../skywind/services/entity";
import { CreateData, default as getEntityFactory } from "../../skywind/services/entityFactory";
import { APPROVED, INIT, TRANSFER_IN } from "../../skywind/entities/payment";
import { BAD_TRANSACTION_ID, INSUFFICIENT_BALANCE, TRANSACTION_EXISTS } from "@skywind-group/sw-wallet/lib";
import * as TokenUtils from "../../skywind/utils/token";
import { generateGameToken } from "../../skywind/utils/token";
import * as GameService from "../../skywind/services/game";
import EntityCache from "../../skywind/cache/entity";
import PlayerResponsibleGaming from "../../skywind/services/playerResponsibleGaming";
import * as settings from "../../skywind/services/settings";
import * as MerchantService from "../../skywind/services/merchant";
import { token } from "@skywind-group/sw-utils";
import { BonusCoinsData, PlayerBonusCoinWallet, PromoWalletErrors } from "@skywind-group/sw-management-promo-wallet";
import {
    BrandFinalizationType,
    FinalizeGameRequest,
    GameTokenData,
    PaymentRequestWithoutToken,
    PlayMode,
    RollbackBetRequest
} from "@skywind-group/sw-wallet-adapter-core";
import * as phantom from "../../skywind/phantom/service";
import config from "../../skywind/config";
import {
    Balance,
    BonusCoinsRedeemRequest,
    commitPaymentOperationForMerchant,
    ExternalTransferData,
    GameJackpotWinAction,
    GamePaymentAction,
    GamePaymentOperation,
    MerchantDoPaymentAction,
    PlayService as IPlayService,
    PlayServiceErrors,
    PlayServiceImpl,
    TransferRequest,
    WALLET_OPERATION_NAME
} from "@skywind-group/sw-management-playservice";
import PlayerGameSessionService from "../../skywind/services/player/playerGameSessionService";
import { ProviderAuthDetails, StartGameRequest } from "@skywind-group/sw-management-gameprovider";
import { ExternalTransferRequest, GameProviderErrors } from "@skywind-group/sw-management-gameprovider-core";
import { parseJwt } from "../helper";
import { DeferredPayment, DeferredPaymentMethod } from "@skywind-group/sw-deferred-payment";
import { ENTITY_GAME_STATUS, GAME_TYPES, HISTORY_RENDER_TYPE } from "../../skywind/utils/common";
import { getAvailableSiteService } from "../../skywind/services/availableSites";
import { AVAILABLE_SITE_STATUSES } from "../../skywind/entities/availableSite";
import { getBrandPlayerService } from "../../skywind/services/brandPlayer";
import { getEntityJurisdictionService } from "../../skywind/services/entityJurisdiction";
import { reset } from "../../skywind/cache/merchant";
import * as jwt from "jsonwebtoken";

const PaymentOrderModel = getPaymentModel();
const chaiAsPromise = require("chai-as-promised");

should();
use(chaiAsPromise);

const promoStartDate = new Date(new Date().getTime() + 10 * 60 * 10000);
// + 3 days
const promoEndDate = new Date(promoStartDate.getTime() + 3 * 24 * 60 * 60 * 1000);

describe("PlayService test", () => {

    let playService: IPlayService;
    let findTransactionStub: SinonStub;
    let startTransactionStub: SinonStub;
    let getWalletStub: SinonStub;
    let getBrandStub: SinonStub;
    let verifyGameToken: SinonStub;
    let findOneEntityGame: SinonStub;
    let findAllEntityGameInfos: SinonStub;
    let findAllGameRelatedToRush: SinonStub;
    let verifyStartGameToken: SinonStub;
    let getEntitySettings: SinonStub;
    let validatePlayerRestrictions: SinonStub;
    let findOnePlayer: SinonStub;
    let findOneMerchant: SinonStub;
    let getPhantomService: SinonStub;
    let logExternalTransaction: SinonStub;
    let findAllJurisdictions: SinonStub;
    let decode: SinonStub;

    const bonusCoinData: BonusCoinsData = {
        exchangeRate: 1,
        promoId: 2,
        rewardId: 2,
        coin: 0.25,
        expireIn: Date.now() + 1000 * 60 * 60,
        startDate: Date.now(),
        redeemMinAmount: 10,
        rewardedAmount: 60,
        ensDate: Date.now() + 1000 * 60 * 60 * 24,
        amount: 60,
        existenceType: null
    } as BonusCoinsData;
    const bonusCoinRewardFilter: AccountPropertiesFilter[] = [
        {
            account: PLAYER.PLAYER_BONUS_COINS_ACCOUNT,
            properties: [ new AccountProperty(PLAYER.PLAYER_BONUS_COINS_ACCOUNT) ],
            reward: bonusCoinData
        } as AccountPropertiesFilter
    ];

    before(async () => {
        await truncate();
        await createComplexStructure();
        findTransactionStub = stub(WalletFacade, "findCommittedTransaction");
        startTransactionStub = stub(WalletFacade, "startTransactionWithID");
        getWalletStub = stub(WalletFacade, "get");
        getBrandStub = stub(brandService, "checkBrand");
        verifyGameToken = stub(TokenUtils, "verifyGameToken");
        findOneEntityGame = stub(GameService, "findOneEntityGame");
        findAllEntityGameInfos = stub(GameService, "findAllEntityGameInfos");
        findAllGameRelatedToRush = stub(GameService, "findAllGameRelatedToRush");
        findOneMerchant = stub(MerchantService, "getMerchantCRUDService");
        verifyStartGameToken = stub(TokenUtils, "verifyStartGameToken");
        getEntitySettings = stub(settings, "getEntitySettings");
        getPhantomService = stub(phantom, "getPhantomService");
        findAllJurisdictions = stub(getEntityJurisdictionService(), "findAll");
        decode = stub(jwt, "decode");

        const brand = {
            findPlayer: mock(),
            underMaintenance: () => false,
            getVersion: () => 1
        };
        getBrandStub.returns(Promise.resolve(brand));

        playService = new PlayServiceImpl({} as any);
        validatePlayerRestrictions = stub(
            PlayerResponsibleGaming.prototype,
            "validatePlayerRestrictions"
        );
        findOnePlayer = stub(getBrandPlayerService(), "findOneWithGameGroup");
        logExternalTransaction = stub(WalletFacade, "logExternalTransaction");

        config.phantom.enablePhantomFeatures = true;
    });

    beforeEach(() => {
        getEntitySettings.returns(Promise.resolve({}));
        getPhantomService.returns({
            getJackpots: () => {
                return Promise.resolve({ jackpots: [ { jackpotId: "phantom_jackpot" } ] });
            }
        });
        findAllJurisdictions.returns([]);
        decode.returns({
            brandId: 1,
            playerCode: "PL0001",
            currency: "EUR"
        });
        reset();
    });

    afterEach(() => {
        findTransactionStub.resetBehavior();
        startTransactionStub.reset();
        getWalletStub.resetBehavior();
        getBrandStub.resetBehavior();
        verifyGameToken.resetBehavior();
        verifyStartGameToken.resetBehavior();
        getEntitySettings.resetBehavior();
        validatePlayerRestrictions.resetBehavior();
        findOnePlayer.resetBehavior();
        findOneMerchant.resetBehavior();
        getPhantomService.resetBehavior();
        findAllJurisdictions.resetBehavior();
        logExternalTransaction.resetBehavior();
    });

    after(() => {
        findTransactionStub.restore();
        startTransactionStub.restore();
        getWalletStub.restore();
        getBrandStub.restore();
        verifyGameToken.restore();
        findOneEntityGame.restore();
        findAllEntityGameInfos.restore();
        verifyStartGameToken.restore();
        getEntitySettings.restore();
        validatePlayerRestrictions.restore();
        findOnePlayer.restore();
        findOneMerchant.restore();
        getPhantomService.restore();
        findAllJurisdictions.restore();

        config.phantom.enablePhantomFeatures = false;
    });

    it("Rollback betpayment without token - not ready error", async () => {
        findTransactionStub.throws(new Error());
        const trxId = await WalletFacade.generateTrxId();
        await playService.rollbackBetPaymentWithoutToken({
            brandId: 7,
            originalTransactionId: trxId,
            extTransactionId: "extTxId",
            roundId: 2,
            gameCode: "game-code"
        }).should
            .eventually
            .rejectedWith(PlayServiceErrors.NotReadyToRollbackTransaction);
    });

    it("Rollback betpayment - transaction not found error", async () => {
        const trxId = await WalletFacade.generateTrxId();
        await playService.rollbackBetPaymentWithoutToken({
            brandId: 7,
            originalTransactionId: trxId,
            extTransactionId: "extTxId",
            roundId: 2,
            gameCode: "game-code"
        }).should
            .eventually
            .rejectedWith(PlayServiceErrors.TransactionDoesNotExist);
    });

    it("Rollback betpayment", async () => {
        const walletKey = "player:9:playerCode:USD";

        findTransactionStub.returns(Promise.resolve({
            id: "betTransactionId1",
            data: [ { account: "main", amount: -2000, trxType: "bet", walletKey: walletKey } ]
        }));

        const accountIncMock = mock();
        accountIncMock.atLeast(4).withArgs(PLAYER.PLAYER_BALANCE, 2000, "bet");
        const account = {
            inc: accountIncMock,
            get: () => {
                return 2000;
            },
        };
        const walletAccounts = {};
        const getAccountMock = mock();
        getAccountMock.atLeast(4).returns(account);
        walletAccounts["get"] = getAccountMock;

        const walletMock = {
            key: walletKey,
            data: mock(),
            accounts: walletAccounts,
            changes: mock(),
        };

        const getWalletFromTransMock = mock();
        getWalletFromTransMock.atLeast(4).returns(walletMock);
        getWalletStub.returns(Promise.resolve(walletMock));
        const trxId = await WalletFacade.generateTrxId();
        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(BAD_TRANSACTION_ID),
        }));
        await playService.rollbackBetPaymentWithoutToken({
            brandId: 7,
            originalTransactionId: trxId,
            extTransactionId: "extTxId",
            roundId: 2,
            gameCode: "game-code"
        }).should.eventually.rejectedWith(WalletErrors.BadTransactionId);

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(TRANSACTION_EXISTS),
        }));
        const balance = await playService.rollbackBetPaymentWithoutToken({
            brandId: 7,
            originalTransactionId: trxId,
            extTransactionId: "extTxId",
            roundId: 2,
            gameCode: "game-code"
        });
        expect(balance).deep.equal({ main: 20 });
        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject("error"),
        }));
        await playService.rollbackBetPaymentWithoutToken({
            brandId: 7,
            originalTransactionId: trxId,
            extTransactionId: "extTxId",
            roundId: 2,
            gameCode: "game-code"
        }).should.eventually.rejectedWith("error");
        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: mock(),
        }));
        const result = await playService.rollbackBetPaymentWithoutToken({
            brandId: 7,
            originalTransactionId: trxId,
            extTransactionId: "extTxId",
            roundId: 2,
            gameCode: "game-code"
        });

        expect(result).to.have.property("main", 20);
    });

    it("Commit win without token", async () => {
        const commitWinWithoutTokenMock = mock();
        const trxId = await WalletFacade.generateTrxId();
        const originalCommitWinWithoutToken = playService.commitWinWithoutToken;
        playService.commitWinWithoutToken = commitWinWithoutTokenMock;
        commitWinWithoutTokenMock.once().withArgs({
            code: "custId",
            brandId: 7,
            transactionId: trxId,
            roundId: "999",
            amount: 10,
            roundEnded: true,
            currency: "usd",
            deviceId: "clientId",
            spinType: "main",
            extTransactionId: "888",
            gameCode: "game-code"
        }).returns({ main: 20 });

        const result = await playService.commitWinWithoutToken({
            code: "custId",
            brandId: 7,
            transactionId: trxId,
            roundId: "999",
            amount: 10,
            roundEnded: true,
            currency: "usd",
            deviceId: "clientId",
            spinType: "main",
            extTransactionId: "888",
            gameCode: "game-code"
        } as any);

        expect(result).to.have.property("main", 20);
        commitWinWithoutTokenMock.verify();
        playService.commitWinWithoutToken = originalCommitWinWithoutToken;
    });

    it("Get transaction state", withTransaction(async () => {
        const ent2: BaseEntity = await EntityService.findOne({ key: complexStructure.tle1ent2.key });
        const data: CreateData = {
            name: "brand20",
            description: "brand20 description",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            countries: [ "US" ],
            currencies: [ "USD" ],
            languages: [ "en" ],
            jurisdictionCode: "COM",
            webSiteUrl: "http://website86.com"
        };
        const brand = await getEntityFactory(ent2).createBrand(data);

        const newPaymentData = {
            trxId: "AAA",
            extTrxId: "BBB",
            brandId: brand.id.toString(),
            playerCode: "PL00011",
            orderType: TRANSFER_IN,
            currencyCode: "USD",
            amount: 10,
            orderStatus: INIT,
            orderDate: new Date(),
            startDate: new Date(),
            endDate: new Date(),
            isTest: true,
            playerBalanceAfter: 110
        };

        await PaymentOrderModel.create(newPaymentData);

        findTransactionStub.returns(undefined);
        let result = await getTransactionInfo(brand.id, "BBB", "win");
        expect(result).to.deep.eq({
            paymentStatus: INIT,
            status: "absent"
        });

        findTransactionStub.returns(Promise.reject(new WalletErrors.TransactionIsProcessing()));
        result = await getTransactionInfo(brand.id, "BBB", "win");
        expect(result).to.deep.eq({
            status: "processing",
            paymentStatus: INIT,
            playerBalanceAfter: 110
        });

        findTransactionStub.returns(Promise.resolve({
            id: "betTransactionId1"
        }));
        result = await getTransactionInfo(brand.id, "BBB", "win");
        expect(result).to.deep.eq({
            paymentStatus: INIT,
            playerBalanceAfter: 110,
            status: "committed"
        });

        findTransactionStub.returns(Promise.resolve({
            id: "betTransactionId1",
            data: [
                {
                    walletKey: `player:${brand.id}:PL00011:USD`,
                    prevValue: 10000,
                    value: 11000,
                    account: "main",
                    property: "balance"
                }
            ]
        }));
        result = await getTransactionInfo(brand.id, "BBB", "win");
        expect(result).to.deep.eq({
            paymentStatus: APPROVED,
            playerBalanceAfter: 110,
            status: "committed"
        });

        findTransactionStub.returns(Promise.reject("error"));
        await getTransactionInfo(brand.id, "BBB", "win")
            .should.eventually.rejectedWith("error");

        result = await getTransactionInfo(12345, "qwerty", "win");
        expect(result).to.deep.eq({
            status: "absent"
        });
    }));

    it("External transfer with token", async () => {
        const walletKey = "player:9:playerCode:USD";

        findTransactionStub.returns(Promise.resolve({
            id: "betTransactionId1",
            data: [ { account: "main", amount: -2000, trxType: "bet", walletKey: walletKey } ]
        }));

        const accountIncMock = mock();
        accountIncMock.twice();
        const account = {
            inc: accountIncMock,
            get: () => {
                return 2000;
            },
        };
        const walletAccounts = {};
        const getAccountMock = mock();
        getAccountMock.twice().returns(account);
        walletAccounts["get"] = getAccountMock;

        const walletMock = {
            key: walletKey,
            data: mock(),
            accounts: walletAccounts,
            changes: mock(),
        };

        const getWalletFromTransMock = mock();
        getWalletFromTransMock.once().returns(walletMock);

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.resolve([
                {
                    account: "main",
                    amount: -2000,
                    trxType: "bet",
                    walletKey
                }
            ]),
        }));

        getWalletStub.returns(Promise.resolve(walletMock));
        const gameTokenData = {
            playerCode: "PL00001",
            gameCode: "game-code",
            brandId: 1,
            currency: "USD"
        };
        await PlayerGameSessionService.create(
            gameTokenData.brandId, gameTokenData.playerCode, gameTokenData.gameCode, {});

        getBrandStub.returns(Promise.resolve({
            findPlayer: mock(),
            underMaintenance: () => false,
            getVersion: () => 1
        }));
        await PlayService.externalTransfer({
            amount: -1
        } as ExternalTransferRequest, "transfer-in")
            .should.eventually.rejectedWith(WalletErrors.AmountIsNegativeError);

        const result = await PlayService.externalTransfer({
            amount: 1,
            gameToken: await generateGameToken(gameTokenData),
            roundId: "some_round_id",
            transactionId: await WalletFacade.generateTrxId()
        }, "transfer-in");

        expect(result).deep.equal({ main: 20 });
        expect(accountIncMock.args[0]).deep.equal([ PLAYER.PLAYER_BALANCE, -100, "bet", 0 ]);
        expect(accountIncMock.args[1]).deep.equal([ PLAYER.PLAYER_BALANCE, 0, "win", 0 ]);
    });

    it("Fail to commit payment operation for merchant", async () => {
        const walletKey = "player:9:playerCode:USD";

        findTransactionStub.returns(Promise.resolve({
            id: "betTransactionId1",
            data: [ { account: "main", amount: -2000, trxType: "bet", walletKey: walletKey } ]
        }));

        const accountIncMock = mock();
        accountIncMock.atLeast(5);
        const account = {
            inc: accountIncMock,
            get: () => {
                return 2000;
            },
            data: {
                stars: 5
            },
        };
        const walletAccounts = {};
        const getAccountMock = mock();
        getAccountMock.atLeast(5).returns(account);
        walletAccounts["get"] = getAccountMock;

        const walletMock = {
            key: walletKey,
            data: mock(),
            accounts: walletAccounts,
            changes: mock(),
        };

        const getWalletFromTransMock = mock();
        getWalletFromTransMock.atLeast(5).returns(walletMock);

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(INSUFFICIENT_BALANCE),
        }));

        getWalletStub.returns(Promise.resolve(walletMock));
        verifyGameToken.returns(Promise.resolve({
            playerCode: "PL00001",
            gameCode: "game-code",
            brandId: 1,
            currency: "USD"
        }));
        getBrandStub.returns(Promise.resolve({
            findPlayer: mock(),
            underMaintenance: () => false
        }));

        const actions: GamePaymentAction[] = [
            {
                action: "credit",
                attribute: "balance",
                amount: 100,
                changeType: "jackpot_win",
                jackpotId: "test_jp_id",
                pool: "test_pool"
            } as GameJackpotWinAction,
            {
                action: "debit",
                attribute: "balance",
                amount: 50
            },
            {
                action: "credit",
                attribute: "stars",
                amount: 1
            },
            {
                action: "freebet",
                attribute: "balance",
                amount: 100
            }
        ];
        const gameTokenData: GameTokenData = {
            playerCode: "PL00001",
            gameCode: "game-code",
            brandId: 1,
            currency: "USD"
        };
        const request: GamePaymentOperation = {
            actions,
            transactionId: await WalletFacade.generateTrxId(),
            roundId: "1",
            gameSessionId: "1",
            extTransactionId: "qwwer",
            gameToken: "token",
            ts: new Date().toISOString()
        };
        const doPaymentOperation: MerchantDoPaymentAction = async () => Promise.resolve({} as Balance);

        await commitPaymentOperationForMerchant(
            gameTokenData,
            request,
            walletKey,
            doPaymentOperation,
            {} as IPlayService,
            [])
            .should.eventually.rejectedWith(WalletErrors.InsufficientBalanceError);

        await commitPaymentOperationForMerchant(
            gameTokenData,
            {
                ...request, actions: [
                    {
                        action: "freebet",
                        attribute: "balance",
                        amount: 100
                    }, {
                        action: "freebet",
                        attribute: "stars",
                        amount: 100
                    }
                ], freeBetCoin: 0.1
            },
            walletKey,
            doPaymentOperation,
            {} as IPlayService,
            [])
            .should.eventually.rejectedWith(PromoWalletErrors.InsufficientFreebet);

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(BAD_TRANSACTION_ID),
        }));
        await commitPaymentOperationForMerchant(
            gameTokenData,
            request,
            walletKey,
            doPaymentOperation,
            {} as IPlayService,
            [])
            .should.eventually.rejectedWith(WalletErrors.BadTransactionId);

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(TRANSACTION_EXISTS),
        }));
        findTransactionStub.returns(Promise.resolve({
            id: "betTransactionId1",
            data: [ { account: "main", amount: -2000, trxType: "bet", walletKey: walletKey } ]
        }));
        const result = await commitPaymentOperationForMerchant(
            gameTokenData,
            request,
            walletKey,
            doPaymentOperation,
            {} as IPlayService,
            []);
        expect(result).deep.equal({
            extraBalances: {
                stars: 5
            }
        });
    });

    it("Fail to commit win payment without token", async () => {
        await AnonymousPlayService.commitWinPayment(undefined, {} as PaymentRequestWithoutToken)
            .should.eventually.rejectedWith(GameProviderErrors.InvalidGamePayment);

        await AnonymousPlayService.commitWinPayment(undefined, {
            amount: -1
        } as PaymentRequestWithoutToken)
            .should.eventually.rejectedWith(WalletErrors.AmountIsNegativeError);
    });

    it("Fail to rollback bet payment without token when transaction doesn't exist", async () => {
        const auth: ProviderAuthDetails = {
            providerUser: "providerUser1",
            providerSecret: "providerSecret1",
            providerCode: "provider1"
        };
        getBrandStub.returns(Promise.resolve({
            isMerchant: false,
            isMaster: () => true,
            findPlayer: mock(),
            underMaintenance: () => false,
            getVersion: () => 1
        }));

        findOneEntityGame.returns(Promise.resolve({
            gameCode: "GAME001",
            settings: {
                transferEnabled: false
            },
            game: {
                features: {},
                gameProvider: {}
            }
        }));
        await AnonymousPlayService.rollbackBetPayment(auth, {
            brandId: 1,
            originalTransactionId: await WalletFacade.generateTrxId(),
            extTransactionId: "extTxId",
            roundId: 2,
            playmode: PlayMode.REAL,
            gameCode: "GAME001"
        }).should.eventually.rejectedWith(PlayServiceErrors.TransactionDoesNotExist);
    });

    it("Fail to verify token", async () => {
        verifyGameToken.returns(Promise.reject(new token.TokenVerifyException()));
        await verifyPlayGameToken("token")
            .should.eventually.rejectedWith(Errors.GameTokenError);

        verifyGameToken.returns(Promise.reject(new token.TokenExpiredException()));
        await verifyPlayGameToken("token")
            .should.eventually.rejectedWith(Errors.GameTokenExpired);

        verifyGameToken.returns(Promise.reject("error"));
        await verifyPlayGameToken("token")
            .should.eventually.rejectedWith("error");
    });

    describe("Start fun game", () => {
        const entitySettings = {
            contributionPrecision: 8,
            splitPayment: true,
            maxPaymentRetryAttempts: 10,
            autoPlaySettings: [
                { label: "5", value: 5 },
                { label: "until feature", value: 0, isUntilFeature: true, isDefault: true }
            ]
        };
        let findOneEntityCache;
        let validatePlayerSelfExclusionRestriction;

        beforeEach(async () => {
            findOneEntityCache = stub(EntityCache, "findById");
            validatePlayerSelfExclusionRestriction = stub(
                PlayerResponsibleGaming.prototype,
                "validatePlayerSelfExclusionRestriction"
            );

            findAllJurisdictions.returns([]);
        });

        afterEach(async () => {
            findOneEntityCache.restore();
            validatePlayerSelfExclusionRestriction.restore();
        });

        it("Start fun game", async () => {
            findOneEntityCache.returns(Promise.resolve());
            getEntitySettings.returns(Promise.resolve(entitySettings));

            await PlayService.startFunGame({
                startGameToken: {
                    brandId: 1,
                    envId: "test",
                    playerCode: "PL001",
                    gameCode: "game001",
                    currency: "USD"
                }
            }).should.eventually.rejectedWith(Errors.EntityCouldNotBeFound);

            findOneEntityCache.returns(Promise.resolve({
                isBrand: () => true,
                isSuspended: () => false,
                underMaintenance: () => true
            }));
            await PlayService.startFunGame({
                startGameToken: {
                    brandId: 1,
                    envId: "test",
                    playerCode: "PL001",
                    gameCode: "game001",
                    currency: "USD"
                }
            }).should.eventually.rejectedWith(Errors.EntityUnderMaintenanceError);

            findOneEntityCache.returns(Promise.resolve({
                type: ENTITY_TYPE.ENTITY,
                isMerchant: false,
                isMaster: () => true,
                status: "world",
                isSuspended: () => false,
                environment: "test",
                isBrand: () => true,
                countryExists: () => true,
                findPlayer: mock(),
                underMaintenance: () => false
            }));
            validatePlayerSelfExclusionRestriction.returns(Promise.resolve({}));
            findOneEntityGame.returns(Promise.resolve({
                game: {
                    limits: {},
                    features: {},
                    getLimits: () => ({})
                },
                settings: {}
            }));
            await PlayService.startFunGame({
                startGameToken: {
                    brandId: 1,
                    envId: "test",
                    playerCode: "PL001",
                    gameCode: "game001",
                    currency: "USD"
                }
            }).should.eventually.rejectedWith(Errors.LimitsForCurrencyNotFound);

            findOneEntityGame.returns(Promise.resolve({
                game: {
                    type: "slot",
                    totalBetMultiplier: 50,
                    limits: {
                        USD: {
                            stakeAll: [ 1, 2, 3, 4, 5 ],
                            stakeMax: 5,
                            stakeMin: 1,
                            stakeDef: 1,
                            winMax: 2
                        }
                    },
                    features: {},
                    getLimits: () => ({
                        stakeAll: [ 1, 2, 3, 4, 5 ],
                        stakeMax: 5,
                        stakeMin: 1,
                        stakeDef: 1,
                        winMax: 2
                    })
                },
                limitFilters: { USD: { winMax: 10, stakeMax: 4 } },
                settings: {}
            }));

            const result = await PlayService.startFunGame({
                startGameToken: {
                    brandId: 1,
                    envId: "test",
                    gameCode: "game001",
                    currency: "USD"
                } as any
            });

            expect(result.gameMode).equal(PlayMode.FUN);
            expect(result.jrsdSettings).equal(undefined);
            expect(result.logoutOptions).equal(undefined);
            expect(result.limits).deep.equal({
                stakeAll: [ 1, 2, 3, 4 ],
                stakeMax: 4,
                stakeMin: 1,
                stakeDef: 1,
                winMax: 10,
                maxTotalStake: 250,
            });
            expect(result.player).not.exist;
            expect(result.settings.autoSpinsOptions).deep.equal([
                { label: "5", value: 5 },
                { label: "until feature", value: 0, isUntilFeature: true, isDefault: true }
            ]);
            expect(result.phantom).not.exist;

            const gameToken = parseJwt(result.gameToken);
            expect(!!(gameToken.playerCode)).equal(true);
            expect(gameToken.playerCode.includes("player")).equal(true);
        });

        describe("isFunModeNotSupported", () => {
            const entityGame = {
                game: {
                    limits: {},
                    features: {},
                    getLimits: () => ({})
                },
                settings: {}
            };
            const startFunGameRequest = {
                startGameToken: {
                    brandId: 1,
                    envId: "test",
                    playerCode: "PL001",
                    gameCode: "game001",
                    currency: "USD"
                }
            };

            beforeEach(async () => {
                getEntitySettings.returns(Promise.resolve(entitySettings));
                findOneEntityCache.returns(Promise.resolve({
                    type: ENTITY_TYPE.ENTITY,
                    isMerchant: false,
                    isMaster: () => true,
                    status: "world",
                    isSuspended: () => false,
                    environment: "test",
                    isBrand: () => true,
                    countryExists: () => true,
                    findPlayer: mock(),
                    underMaintenance: () => false
                }));
                validatePlayerSelfExclusionRestriction.returns(Promise.resolve({}));
                findOneEntityGame.returns(Promise.resolve(entityGame));
                findAllJurisdictions.returns([]);
            });

            it("game.isFunModeNotSupported: true", async () => {
                findOneEntityGame.returns(Promise.resolve({
                    ...entityGame,
                    game: {
                        ...entityGame.game,
                        features: { isFunModeNotSupported: true },
                    }
                }));
                await PlayService.startFunGame(startFunGameRequest)
                    .should.eventually.rejectedWith(Errors.GameLaunchForbidden);
            });

            it("game.isFunModeNotSupported: false", async () => {
                findOneEntityGame.returns(Promise.resolve({
                    ...entityGame,
                    game: {
                        ...entityGame.game,
                        features: { isFunModeNotSupported: false },
                    }
                }));
                await PlayService.startFunGame(startFunGameRequest)
                    .should.not.eventually.rejectedWith(Errors.GameLaunchForbidden);
            });

            it("game.isFunModeNotSupported: undefined", async () => {
                findOneEntityGame.returns(Promise.resolve({
                    ...entityGame,
                    game: {
                        ...entityGame.game,
                        features: { isFunModeNotSupported: undefined },
                    }
                }));
                await PlayService.startFunGame(startFunGameRequest)
                    .should.not.eventually.rejectedWith(Errors.GameLaunchForbidden);
            });

            it("entitySettings.isFunModeNotSupported: true", async () => {
                getEntitySettings.returns(Promise.resolve({
                    ...entitySettings,
                    isFunModeNotSupported: true
                }));
                await PlayService.startFunGame(startFunGameRequest)
                    .should.eventually.rejectedWith(Errors.GameLaunchForbidden);
            });

            it("entitySettings.isFunModeNotSupported: false", async () => {
                getEntitySettings.returns(Promise.resolve({
                    ...entitySettings,
                    isFunModeNotSupported: false
                }));
                await PlayService.startFunGame(startFunGameRequest)
                    .should.not.eventually.rejectedWith(Errors.GameLaunchForbidden);
            });

            it("entitySettings.isFunModeNotSupported: undefined", async () => {
                getEntitySettings.returns(Promise.resolve({
                    ...entitySettings,
                    isFunModeNotSupported: undefined
                }));
                await PlayService.startFunGame(startFunGameRequest)
                    .should.not.eventually.rejectedWith(Errors.GameLaunchForbidden);
            });

            it("game.isFunModeNotSupported: true && entitySettings.isFunModeNotSupported: true", async () => {
                findOneEntityGame.returns(Promise.resolve({
                    ...entityGame,
                    game: {
                        ...entityGame.game,
                        features: { isFunModeNotSupported: true },
                    }
                }));
                getEntitySettings.returns(Promise.resolve({
                    ...entitySettings,
                    isFunModeNotSupported: true
                }));
                await PlayService.startFunGame(startFunGameRequest)
                    .should.eventually.rejectedWith(Errors.GameLaunchForbidden);
            });

            it("game.isFunModeNotSupported: false && entitySettings.isFunModeNotSupported: true", async () => {
                findOneEntityGame.returns(Promise.resolve({
                    ...entityGame,
                    game: {
                        ...entityGame.game,
                        features: { isFunModeNotSupported: false },
                    }
                }));
                getEntitySettings.returns(Promise.resolve({
                    ...entitySettings,
                    isFunModeNotSupported: true
                }));
                await PlayService.startFunGame(startFunGameRequest)
                    .should.not.eventually.rejectedWith(Errors.GameLaunchForbidden);
            });
        });

        it("Starts fun game with filtered out settings", async () => {
            getEntitySettings.returns(Promise.resolve({
                contributionPrecision: 8,
                splitPayment: true,
                transferEnabled: true,
                autoCreateTestJP: true,
                maxPaymentRetryAttempts: 10,
                autoPlaySettings: [
                    { label: "5", value: 5 },
                    { label: "until feature", value: 0, isUntilFeature: true, isDefault: true }
                ]
            }));

            findOneEntityCache.returns(Promise.resolve({
                type: ENTITY_TYPE.ENTITY,
                isMerchant: false,
                isMaster: () => true,
                status: "world",
                isSuspended: () => false,
                environment: "test",
                isBrand: () => true,
                countryExists: () => true,
                findPlayer: mock(),
                underMaintenance: () => false
            }));
            validatePlayerSelfExclusionRestriction.returns(Promise.resolve({}));

            findOneEntityGame.returns(Promise.resolve({
                game: {
                    type: "slot",
                    totalBetMultiplier: 50,
                    limits: {
                        USD: {
                            stakeAll: [ 1, 2, 3, 4, 5 ],
                            stakeMax: 5,
                            stakeMin: 1,
                            stakeDef: 1,
                            winMax: 2
                        }
                    },
                    features: {},
                    getLimits: () => ({
                        stakeAll: [ 1, 2, 3, 4, 5 ],
                        stakeMax: 5,
                        stakeMin: 1,
                        stakeDef: 1,
                        winMax: 2
                    })
                },
                limitFilters: { USD: { winMax: 10, stakeMax: 4 } },
                settings: {}
            }));

            const result = await PlayService.startFunGame({
                startGameToken: {
                    brandId: 1,
                    envId: "test",
                    gameCode: "game001",
                    currency: "USD"
                } as any
            }, [ "splitPayment", "autoCreateTestJP", "transferEnabled" ]);
            expect(result.settings).deep.equal({
                autoSpinsOptions: [
                    { label: "5", value: 5 },
                    { label: "until feature", value: 0, isUntilFeature: true, isDefault: true }
                ],
                rtpConfigurator: undefined
            });
            expect(result.phantom).not.exist;
        });

        it("Starts fun game with jwt start game token", async () => {
            findOneEntityCache.returns(Promise.resolve({
                type: ENTITY_TYPE.BRAND,
                isMerchant: false,
                isMaster: () => true,
                status: "world",
                isSuspended: () => false,
                environment: "test",
                isBrand: () => true,
                countryExists: () => true,
                findPlayer: mock(),
                underMaintenance: () => false,
                getCountries: () => [ "RU" ]
            }));

            verifyStartGameToken.returns(Promise.resolve({
                brandId: 1,
                playmode: PlayMode.FUN,
                gameCode: "GAME001",
                currency: "USD",
                playerCode: "PL0001"
            }));

            findOneEntityGame.returns(Promise.resolve({
                game: {
                    limits: {
                        USD: {
                            stakeAll: [ 1, 2, 3 ],
                            stakeMax: 3,
                            stakeMin: 1,
                            stakeDef: 1
                        }
                    },
                    features: {},
                    getLimits: () => ({
                        stakeAll: [ 1, 2, 3 ],
                        stakeMax: 3,
                        stakeMin: 1,
                        stakeDef: 1
                    })
                },
                settings: {}
            }));

            findOnePlayer.returns(Promise.resolve({
                isSuspended: () => false,
                toInfo: () => ({})
            }));

            const startGameToken = await TokenUtils.generateStartGameToken({
                brandId: 1,
                gameCode: "GAME001",
                providerCode: "providerCode1",
                providerGameCode: "GAME001",
                playerCode: "PL0001",
                currency: "USD",
                playmode: PlayMode.FUN
            });

            let result = await PlayService.startFunGame({
                startGameToken
            });

            expect(result.gameMode).equal(PlayMode.FUN);
            expect(result.jrsdSettings).equal(undefined);
            expect(result.logoutOptions).equal(undefined);
            expect(result.limits).deep.equal({
                stakeAll: [ 1, 2, 3 ],
                stakeMax: 3,
                stakeMin: 1,
                stakeDef: 1
            });
            expect(result.player).exist;

            result = await PlayService.startFunGame({
                startGameToken
            });
            expect(result.gameMode).equal(PlayMode.FUN);
            expect(result.jrsdSettings).equal(undefined);
            expect(result.logoutOptions).equal(undefined);
            expect(result.limits).deep.equal({
                stakeAll: [ 1, 2, 3 ],
                stakeMax: 3,
                stakeMin: 1,
                stakeDef: 1
            });
            expect(result.player).exist;
            expect(result.phantom).not.exist;
        });

        it("Starts fun game with jwt start game token for external game provider", async () => {

            verifyStartGameToken.returns(Promise.resolve({
                brandId: 1,
                playmode: PlayMode.FUN,
                gameCode: "GAME001",
                currency: "USD",
                playerCode: "PL0001"
            }));

            findOneEntityCache.returns(Promise.resolve({
                type: ENTITY_TYPE.BRAND,
                isMerchant: false,
                isMaster: () => true,
                status: "world",
                isSuspended: () => false,
                environment: "test",
                isBrand: () => true,
                countryExists: () => true,
                findPlayer: mock(),
                underMaintenance: () => false
            }));

            findOneEntityGame.returns(Promise.resolve({
                game: {
                    limits: {
                        USD: {
                            stakeAll: [ 1, 2, 3 ],
                            stakeMax: 3,
                            stakeMin: 1,
                            stakeDef: 1
                        }
                    },
                    features: {},
                    getLimits: () => ({
                        stakeAll: [ 1, 2, 3 ],
                        stakeMax: 3,
                        stakeMin: 1,
                        stakeDef: 1
                    })
                },
                settings: {}
            }));

            findOnePlayer.returns(Promise.resolve({
                isSuspended: () => false,
                toInfo: () => ({})
            }));

            const startGameToken = await TokenUtils.generateStartGameToken({
                brandId: 1,
                gameCode: "GAME001",
                providerCode: "providerCode1",
                providerGameCode: "GAME001",
                playerCode: "PL0001",
                currency: "USD",
                playmode: PlayMode.FUN
            });

            const result = await PlayService.startFunGame({
                startGameToken
            });
            expect(result.gameMode).equal(PlayMode.FUN);
            expect(result.jrsdSettings).equal(undefined);
            expect(result.logoutOptions).equal(undefined);
            expect(result.limits).deep.eq({
                stakeAll: [ 1, 2, 3 ],
                stakeMax: 3,
                stakeMin: 1,
                stakeDef: 1
            });
            expect(result.player).exist;
            expect(result.phantom).not.exist;
        });
    });

    it("Start game", async () => {

        getPhantomService.returns({
            getJackpots: () => {
                return Promise.resolve(undefined);
            }
        });

        getWalletStub.returns(Promise.resolve({
            accounts: {
                get: () => ({
                    get: () => 10,
                    data: {
                        stars: 5,
                        [`promo/1/1/amount/4/5/${+Date.now() - 1000}/${+promoEndDate}/amount`]: 60,
                        [`promo/1/1/amount/4/5/${+Date.now() - 1000}/${+promoEndDate}/expireIn`]: *********
                    }
                }),
            }
        }));

        validatePlayerRestrictions.returns(Promise.resolve({
            hasRealityCheck: () => true,
            getRealityCheck: () => false
        }));
        verifyStartGameToken.returns(Promise.reject(new token.TokenVerifyException()));
        await PlayService.startGame({} as StartGameRequest, "1.0.4.0")
            .should.eventually.rejectedWith(Errors.StartGameTokenError);

        verifyStartGameToken.returns(Promise.reject(new token.TokenExpiredException()));
        await PlayService.startGame({} as StartGameRequest, "1.0.4.0")
            .should.eventually.rejectedWith(Errors.StartGameTokenExpired);

        verifyStartGameToken.returns(Promise.reject("error"));
        await PlayService.startGame({} as StartGameRequest, "1.0.4.0")
            .should.eventually.rejectedWith("error");

        getBrandStub.returns(Promise.resolve({
            underMaintenance: () => true,
            getVersion: () => 1
        }));
        verifyStartGameToken.returns(Promise.resolve({ brandId: 1 }));
        await PlayService.startGame({} as StartGameRequest, "1.0.4.0")
            .should.eventually.rejectedWith(Errors.EntityUnderMaintenanceError);

        getBrandStub.returns(Promise.resolve({
            findPlayer: mock(),
            type: ENTITY_TYPE.BRAND,
            path: "",
            name: "brand",
            isSuspended: () => false,
            isMaster: () => true,
            countryExists: () => true,
            underMaintenance: () => false,
            getVersion: () => 1,
            getCountries: () => [ "AU" ]
        }));

        findOneEntityGame.returns(Promise.resolve({
            game: {
                limits: {
                    USD: {
                        stakeAll: [ 1, 2, 3 ],
                        stakeMax: 3,
                        stakeMin: 1,
                        stakeDef: 1
                    }
                },
                features: { isMultibet: false },
                historyRenderType: HISTORY_RENDER_TYPE.OLD_AND_HISTORY_FROM_GAME,
                getLimits: () => ({
                    stakeAll: [ 1, 2, 3 ],
                    stakeMax: 3,
                    stakeMin: 1,
                    stakeDef: 1
                })
            },
            settings: {},
            isLiveGame: () => false
        }));

        verifyStartGameToken.returns(Promise.resolve({
            brandId: 1,
            playmode: PlayMode.REAL,
            gameCode: "GAME001",
            currency: "USD",
            playerCode: "PL0001"
        }));

        getEntitySettings.returns(Promise.resolve({}));

        findOnePlayer.returns(Promise.resolve({
            isSuspended: () => true,
            toInfo: () => ({})
        }));

        await PlayService.startGame({} as StartGameRequest, "1.0.4.0")
            .should.eventually.rejectedWith(Errors.PlayerIsSuspended);

        findOnePlayer.returns(Promise.resolve({
            isSuspended: () => false,
            toInfo: () => ({})
        }));

        const result = await PlayService.startGame({} as StartGameRequest, "1.0.4.0");
        delete result.gameToken;
        expect(result).deep.equal({
            balance: {
                main: 0.1,
            },
            player: {
                nicknameChangeAttemptsLeft: undefined
            },
            limits: {
                stakeAll: [ 1, 2, 3 ],
                stakeMax: 3,
                stakeMin: 1,
                stakeDef: 1
            },
            brandSettings: {
                fullscreen: true
            },
            gameSettings: {},
            settings: {
                rtpConfigurator: undefined
            },
            jrsdSettings: {
                realityCheck: false
            },
            jurisdictionCode: undefined,
            playedFromCountry: "AU",
            region: "default",
            renderType: HISTORY_RENDER_TYPE.OLD_AND_HISTORY_FROM_GAME,
            brandInfo: {
                name: "brand",
                title: ""
            },
            useGameProviderLimits: undefined,
        });

        findOneEntityGame.returns(Promise.resolve({
            status: ENTITY_GAME_STATUS.HIDDEN,
            game: {
                limits: {
                    USD: {
                        stakeAll: [ 1, 2, 3 ],
                        stakeMax: 3,
                        stakeMin: 1,
                        stakeDef: 1
                    }
                },
                features: { isMultibet: false },
                historyRenderType: HISTORY_RENDER_TYPE.OLD_AND_HISTORY_FROM_GAME,
                getLimits: () => ({
                    stakeAll: [ 1, 2, 3 ],
                    stakeMax: 3,
                    stakeMin: 1,
                    stakeDef: 1
                })
            },
            settings: {},
            isLiveGame: () => false
        }));
        await PlayService.startGame({} as StartGameRequest, "1.0.4.0")
            .should.eventually.rejectedWith(Errors.GameSuspendedError);
    });

    it("Start game with additional settings", async () => {
        getWalletStub.returns(Promise.resolve({
            accounts: {
                get: () => ({
                    get: () => 10,
                    data: { stars: 5 }
                }),
            }
        }));

        getBrandStub.returns(Promise.resolve({
            findPlayer: mock(),
            path: "",
            type: ENTITY_TYPE.BRAND,
            name: "brand",
            isSuspended: () => false,
            isMaster: () => true,
            countryExists: () => true,
            underMaintenance: () => false,
            getVersion: () => 1,
            getCountries: () => [ "AU" ]
        }));
        verifyStartGameToken.returns(Promise.resolve({
            brandId: 1,
            playmode: PlayMode.REAL,
            gameCode: "GAME001",
            currency: "USD",
            playerCode: "PL0001"
        }));

        findOnePlayer.returns(Promise.resolve({
            isSuspended: () => false,
            toInfo: () => ({})
        }));

        getEntitySettings.returns(Promise.resolve({
            contributionPrecision: 8,
            splitPayment: true,
            maxPaymentRetryAttempts: 10,
            minPaymentRetryTimeout: 30,
            enablePhantomFeatures: true,
        }));

        findOneEntityGame.returns(Promise.resolve({
            game: {
                limits: {
                    USD: {
                        stakeAll: [ 1, 2, 3 ],
                        stakeMax: 3,
                        stakeMin: 1,
                        stakeDef: 1
                    }
                },
                features: { isMultibet: true },
                historyRenderType: HISTORY_RENDER_TYPE.OLD_AND_HISTORY_FROM_GAME,
                getLimits: () => ({
                    stakeAll: [ 1, 2, 3 ],
                    stakeMax: 3,
                    stakeMin: 1,
                    stakeDef: 1
                })
            },
            settings: {},
            isLiveGame: () => false
        }));

        const result = await PlayService.startGame({} as StartGameRequest, "1.0.4.0");
        delete result.gameToken;
        expect(result).deep.equal({
            balance: {
                main: 0.1,
            },
            player: {
                nicknameChangeAttemptsLeft: undefined
            },
            limits: {
                stakeAll: [ 1, 2, 3 ],
                stakeMax: 3,
                stakeMin: 1,
                stakeDef: 1
            },
            brandSettings: {
                fullscreen: true
            },
            gameSettings: { isMultibet: true },
            settings: {
                contributionPrecision: 8,
                splitPayment: true,
                maxPaymentRetryAttempts: 10,
                minPaymentRetryTimeout: 30,
                rtpConfigurator: undefined
            },
            jrsdSettings: undefined,
            jurisdictionCode: undefined,
            playedFromCountry: "AU",
            region: "default",
            phantom: { jackpots: [ { jackpotId: "phantom_jackpot" } ] },
            jackpots: [
                {
                    id: "phantom_jackpot",
                    jackpotId: "phantom_jackpot",
                    isGameOwned: false,
                    allowMissingContribution: false,
                    gameHistoryEnabled: true,
                    paymentStatisticEnabled: true,
                    winPaymentType: "jackpot_win",
                    type: "mwjp"
                }
            ],
            // TODO Resolve test isolation issue (restore findOneEntityGame before each test)
            renderType: HISTORY_RENDER_TYPE.OLD_AND_HISTORY_FROM_GAME,
            brandInfo: {
                name: "brand",
                title: ""
            },
            useGameProviderLimits: undefined,
        });
    });

    it("Start game with filtered out settings", async () => {
        getWalletStub.returns(Promise.resolve({
            accounts: {
                get: () => ({
                    get: () => 10,
                    data: { stars: 5 }
                }),
            }
        }));

        getBrandStub.returns(Promise.resolve({
            findPlayer: mock(),
            path: "",
            type: ENTITY_TYPE.BRAND,
            name: "brand",
            isSuspended: () => false,
            isMaster: () => true,
            countryExists: () => true,
            underMaintenance: () => false,
            getVersion: () => 1,
            getCountries: () => [ "AU" ]
        }));
        verifyStartGameToken.returns(Promise.resolve({
            brandId: 1,
            playmode: PlayMode.REAL,
            gameCode: "GAME001",
            currency: "USD",
            playerCode: "PL0001"
        }));

        findOnePlayer.returns(Promise.resolve({
            isSuspended: () => false,
            toInfo: () => ({})
        }));

        getEntitySettings.returns(Promise.resolve({
            contributionPrecision: 8,
            splitPayment: true,
            maxPaymentRetryAttempts: 10,
            minPaymentRetryTimeout: 30,
            enablePhantomFeatures: true,
            autoCreateTestJP: true
        }));

        findOneEntityGame.returns(Promise.resolve({
            game: {
                limits: {
                    USD: {
                        stakeAll: [ 1, 2, 3 ],
                        stakeMax: 3,
                        stakeMin: 1,
                        stakeDef: 1
                    }
                },
                features: { isMultibet: true },
                historyRenderType: HISTORY_RENDER_TYPE.OLD_AND_HISTORY_FROM_GAME,
                getLimits: () => ({
                    stakeAll: [ 1, 2, 3 ],
                    stakeMax: 3,
                    stakeMin: 1,
                    stakeDef: 1
                })
            },
            settings: {},
            isLiveGame: () => false
        }));

        const fieldsToBeRemoved = [ "splitPayment", "autoCreateTestJP" ];
        const result = await PlayService.startGame({} as StartGameRequest, "1.0.4.0", undefined, fieldsToBeRemoved);
        delete result.gameToken;
        expect(result).to.deep.equal({
            balance: {
                main: 0.1,
            },
            player: {
                nicknameChangeAttemptsLeft: undefined
            },
            limits: {
                stakeAll: [ 1, 2, 3 ],
                stakeMax: 3,
                stakeMin: 1,
                stakeDef: 1
            },
            brandSettings: {
                fullscreen: true
            },
            gameSettings: { isMultibet: true },
            settings: {
                contributionPrecision: 8,
                maxPaymentRetryAttempts: 10,
                minPaymentRetryTimeout: 30,
                rtpConfigurator: undefined
            },
            jrsdSettings: undefined,
            jurisdictionCode: undefined,
            playedFromCountry: "AU",
            region: "default",
            phantom: { jackpots: [ { jackpotId: "phantom_jackpot" } ] },
            jackpots: [
                {
                    id: "phantom_jackpot",
                    jackpotId: "phantom_jackpot",
                    isGameOwned: false,
                    allowMissingContribution: false,
                    gameHistoryEnabled: true,
                    paymentStatisticEnabled: true,
                    winPaymentType: "jackpot_win",
                    type: "mwjp"
                }
            ],
            renderType: HISTORY_RENDER_TYPE.OLD_AND_HISTORY_FROM_GAME,
            brandInfo: {
                name: "brand",
                title: ""
            },
            useGameProviderLimits: undefined,
        });
    });

    it("Starts live rush game", async () => {

        getPhantomService.returns({
            getJackpots: () => {
                return Promise.resolve(undefined);
            }
        });

        getWalletStub.returns(Promise.resolve({
            accounts: {
                get: () => ({
                    get: () => 10,
                    data: { stars: 5 }
                }),
            }
        }));

        validatePlayerRestrictions.returns(Promise.resolve({
            hasRealityCheck: () => true,
            getRealityCheck: () => false
        }));

        getBrandStub.returns(Promise.resolve({
            findPlayer: mock(),
            type: ENTITY_TYPE.BRAND,
            path: "",
            name: "brand",
            isSuspended: () => false,
            isMaster: () => true,
            countryExists: () => true,
            underMaintenance: () => false,
            getVersion: () => 1,
            getCountries: () => [ "AU" ]
        }));

        findOneEntityGame.returns(Promise.resolve({
            game: {
                type: GAME_TYPES.live,
                limits: {
                    USD: {
                        stakeAll: [ 1, 2, 3 ],
                        stakeMax: 3,
                        stakeMin: 1,
                        stakeDef: 1
                    }
                },
                features: {
                    live: {
                        type: "roulette",
                        tables: [
                            {
                                tableId: "1",
                                tableName: "Mock Table 1 (from liveRush)",
                                provider: "mock1",
                                providerSettings: {
                                    a: "A1",
                                    b: "B1"
                                },
                                gameCode: "test1",
                                rushOrder: 0
                            },
                            {
                                tableId: "2",
                                provider: "mock1",
                                gameCode: "test2",
                                rushOrder: 1
                            },
                            {
                                tableId: "3",
                                provider: "mock1",
                                gameCode: "test3",
                                rushOrder: 2
                            },
                            {
                                tableId: "4",
                                provider: "mock3",
                                gameCode: "test4",
                                rushOrder: 3
                            }
                        ]
                    }
                },
                getLimits: () => ({
                    stakeAll: [ 1, 2, 3 ],
                    stakeMax: 3,
                    stakeMin: 1,
                    stakeDef: 1
                })
            },
            settings: {},
            isLiveGame: () => true
        }));

        findAllGameRelatedToRush.returns(Promise.resolve([
            {
                code: "test1",
                features: {
                    live: {
                        tableId: "1",
                        provider: "mock1",
                        providerSettings: {
                            a: "A12",
                            c: "C12"
                        },
                    }
                },
                isAddedToEntity: false,
            },
            {
                features: {
                    live: {
                        tableId: "2",
                        provider: "mock1",
                    }
                },
                isAddedToEntity: false,
            },
            {
                code: "test3",
                title: "Mock1 Table 3 (from title)",
                features: {
                    live: {
                        tableId: "3",
                        provider: "mock1",
                        providerSettings: {
                            a: "A31",
                            c: "C31"
                        },
                    }
                },
                isAddedToEntity: true,
            },
            {
                title: "Mock2 Table 3 (from title)",
                features: {
                    live: {
                        tableId: "3",
                        provider: "mock2",
                        providerSettings: {
                            a: "A31",
                            c: "C31"
                        },
                    }
                },
                isAddedToEntity: true,
            }
        ]));

        verifyStartGameToken.returns(Promise.resolve({
            brandId: 1,
            playmode: PlayMode.REAL,
            gameCode: "GAME001",
            currency: "USD",
            playerCode: "PL0001"
        }));

        getEntitySettings.returns(Promise.resolve({}));

        findOnePlayer.returns(Promise.resolve({
            isSuspended: () => false,
            toInfo: () => ({})
        }));

        const result = await PlayService.startGame({} as StartGameRequest, "1.0.4.0");
        delete result.gameToken;
        expect(result).deep.equal({
            balance: {
                main: 0.1,
            },
            player: {
                nicknameChangeAttemptsLeft: undefined
            },
            limits: {
                stakeAll: [ 1, 2, 3 ],
                stakeMax: 3,
                stakeMin: 1,
                stakeDef: 1
            },
            brandSettings: {
                fullscreen: true
            },
            gameSettings: {},
            settings: {
                possibleLimits: {
                    stakeAll: [ 1, 2, 3 ],
                    stakeMax: 3,
                    stakeMin: 1,
                    stakeDef: 1
                },
                rtpConfigurator: undefined,
                tables: [
                    {
                        provider: "mock1",
                        tableId: "1",
                        tableName: "Mock Table 1 (from liveRush)",
                        providerSettings: {
                            a: "A1",
                            b: "B1",
                        },
                        gameCode: "test1",
                        rushOrder: 0,
                        isAddedToEntity: false,
                    },
                    {
                        provider: "mock1",
                        tableId: "2",
                        gameCode: "test2",
                        rushOrder: 1,
                        isAddedToEntity: false,
                    },
                    {
                        provider: "mock1",
                        tableId: "3",
                        gameCode: "test3",
                        rushOrder: 2,
                        isAddedToEntity: true,
                        providerSettings: {
                            a: "A31",
                            c: "C31",
                        },
                        tableName: "Mock1 Table 3 (from title)",
                    },
                    {
                        provider: "mock3",
                        tableId: "4",
                        gameCode: "test4",
                        rushOrder: 3,
                        isAddedToEntity: false,
                    }
                ],
                title: undefined,
                type: "roulette",
            },
            jrsdSettings: {
                realityCheck: false
            },
            jurisdictionCode: undefined,
            playedFromCountry: "AU",
            region: "default",
            renderType: undefined,
            brandInfo: {
                name: "brand",
                title: ""
            },
            useGameProviderLimits: undefined,
        });

    });

    it("Fail to redeem bonus coins, insufficient balance", async () => {
        const walletKey = "player:9:playerCode:USD";
        findTransactionStub.returns(Promise.resolve({
            id: "betTransactionId1",
            data: [ { account: "main", amount: -2000, trxType: "bet", walletKey: walletKey } ]
        }));

        const redeemBonusCoins = stub(PlayerBonusCoinWallet.prototype, "redeemBonusCoins");
        const redeem = stub(PlayerWalletImpl.prototype, "redeem");
        redeemBonusCoins.returns();
        redeem.resolves();

        const getBalance = stub(PlayerWalletImpl.prototype, "getBalance");
        getBalance.returns(Promise.resolve({ main: 676 }));

        const accountIncMock = mock();
        accountIncMock.twice()
            .withArgs(PLAYER.PLAYER_BALANCE, 2000, "bet")
            .withArgs(PLAYER.PLAYER_BALANCE, -1000, "debit");
        const account = {
            inc: accountIncMock,
            data: {},
            get: () => {
                return 2000;
            },
        };
        const walletAccounts = {};
        const getAccountMock = mock();
        getAccountMock.twice().returns(account);
        walletAccounts["get"] = getAccountMock;

        const walletMock = {
            key: walletKey,
            data: mock(),
            accounts: walletAccounts,
            changes: { get: () => ([]) }
        };

        const getWalletFromTransMock = mock();
        getWalletFromTransMock.twice().returns(walletMock);

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: { get: () => ([]) },
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(INSUFFICIENT_BALANCE),
        }));

        const gameTokenData: GameTokenData = {
            playerCode: "PL00001",
            gameCode: "game-code",
            brandId: 1,
            currency: "USD"
        };
        const request: BonusCoinsRedeemRequest = {
            transactionId: await WalletFacade.generateTrxId(),
            roundId: "1",
            gameSessionId: "1",
            extTransactionId: "qwwer",
            gameToken: "token",
            promoId: "qwerty",
            amount: 10,
            ts: new Date().toISOString()
        };
        await playService.redeemBonusCoins(gameTokenData, request, bonusCoinRewardFilter)
            .should.eventually.rejectedWith(PromoWalletErrors.InsufficientBonusCoinsError);

        redeemBonusCoins.restore();
        redeem.restore();
        getBalance.restore();
    });

    it("Fail to redeem bonus coins, BadTransactionId", async () => {
        const redeemBonusCoins = stub(PlayerBonusCoinWallet.prototype, "redeemBonusCoins");
        const redeem = stub(PlayerWalletImpl.prototype, "redeem");
        redeemBonusCoins.returns();
        redeem.resolves();

        const getBalance = stub(PlayerWalletImpl.prototype, "getBalance");
        getBalance.returns(Promise.resolve({ main: 676 }));

        const accountIncMock = mock();
        accountIncMock.twice()
            .withArgs(PLAYER.PLAYER_BALANCE, 2000, "bet")
            .withArgs(PLAYER.PLAYER_BALANCE, -1000, "debit");
        const account = {
            inc: accountIncMock,
            data: {},
            get: () => {
                return 2000;
            },
        };
        const walletAccounts = {};
        const getAccountMock = mock();
        getAccountMock.twice().returns(account);
        walletAccounts["get"] = getAccountMock;

        const walletMock = {
            data: mock(),
            accounts: walletAccounts,
            changes: { get: () => ([]) }
        };

        const getWalletFromTransMock = mock();
        getWalletFromTransMock.twice().returns(walletMock);

        const gameTokenData: GameTokenData = {
            playerCode: "PL00001",
            gameCode: "game-code",
            brandId: 1,
            currency: "USD"
        };
        const request: BonusCoinsRedeemRequest = {
            transactionId: await WalletFacade.generateTrxId(),
            roundId: "1",
            gameSessionId: "1",
            extTransactionId: "qwwer",
            gameToken: "token",
            promoId: "qwerty",
            amount: 10,
            ts: new Date().toISOString()
        };

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: { get: () => ([]) },
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(BAD_TRANSACTION_ID),
        }));

        await playService.redeemBonusCoins(gameTokenData, request, bonusCoinRewardFilter)
            .should.eventually.rejectedWith(WalletErrors.BadTransactionId);
        redeemBonusCoins.restore();
        redeem.restore();
        getBalance.restore();
    });

    it("Fail to redeem bonus coins, TRANSACTION_EXISTS", async () => {
        const walletKey = "player:9:playerCode:USD";
        findTransactionStub.returns(Promise.resolve({
            id: "betTransactionId1",
            data: [ { account: "main", amount: -2000, trxType: "bet", walletKey: walletKey } ]
        }));

        const redeemBonusCoins = stub(PlayerBonusCoinWallet.prototype, "redeemBonusCoins");
        const redeem = stub(PlayerWalletImpl.prototype, "redeem");
        redeemBonusCoins.returns();
        redeem.resolves();

        const getBalance = stub(PlayerWalletImpl.prototype, "getBalance");
        getBalance.returns(Promise.resolve({ main: 676 }));

        const accountIncMock = mock();
        accountIncMock.twice()
            .withArgs(PLAYER.PLAYER_BALANCE, 2000, "bet")
            .withArgs(PLAYER.PLAYER_BALANCE, -1000, "debit");
        const account = {
            inc: accountIncMock,
            data: {},
            get: () => {
                return 2000;
            },
        };
        const walletAccounts = {};
        const getAccountMock = mock();
        getAccountMock.twice().returns(account);
        walletAccounts["get"] = getAccountMock;

        const walletMock = {
            key: walletKey,
            data: mock(),
            accounts: walletAccounts,
            changes: { get: () => ([]) }
        };

        const getWalletFromTransMock = mock();
        getWalletFromTransMock.twice().returns(walletMock);

        const gameTokenData: GameTokenData = {
            playerCode: "PL00001",
            gameCode: "game-code",
            brandId: 1,
            currency: "USD"
        };
        const request: BonusCoinsRedeemRequest = {
            transactionId: await WalletFacade.generateTrxId(),
            roundId: "1",
            gameSessionId: "1",
            extTransactionId: "qwwer",
            gameToken: "token",
            promoId: "qwerty",
            amount: 10,
            ts: new Date().toISOString()
        };

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: { get: () => ([]) },
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(TRANSACTION_EXISTS),
        }));
        const result = await playService.redeemBonusCoins(gameTokenData, request, bonusCoinRewardFilter);
        expect(result).deep.equal({ main: 676 });

        redeemBonusCoins.restore();
        redeem.restore();
        getBalance.restore();
    });

    it("Fail to redeem bonus coins, TransactionIsProcessing", async () => {
        const walletKey = "player:9:playerCode:USD";
        findTransactionStub.returns(Promise.resolve({
            id: "betTransactionId1",
            data: [ { account: "main", amount: -2000, trxType: "bet", walletKey: walletKey } ]
        }));

        const redeemBonusCoins = stub(PlayerBonusCoinWallet.prototype, "redeemBonusCoins");
        const redeem = stub(PlayerWalletImpl.prototype, "redeem");
        redeemBonusCoins.returns();
        redeem.resolves();

        const getBalance = stub(PlayerWalletImpl.prototype, "getBalance");
        getBalance.returns(Promise.resolve({ main: 676 }));

        const accountIncMock = mock();
        accountIncMock.twice()
            .withArgs(PLAYER.PLAYER_BALANCE, 2000, "bet")
            .withArgs(PLAYER.PLAYER_BALANCE, -1000, "debit");
        const account = {
            inc: accountIncMock,
            data: {},
            get: () => {
                return 2000;
            },
        };
        const walletAccounts = {};
        const getAccountMock = mock();
        getAccountMock.twice().returns(account);
        walletAccounts["get"] = getAccountMock;

        const walletMock = {
            key: walletKey,
            data: mock(),
            accounts: walletAccounts,
            changes: { get: () => ([]) }
        };

        const getWalletFromTransMock = mock();
        getWalletFromTransMock.twice().returns(walletMock);

        const gameTokenData: GameTokenData = {
            playerCode: "PL00001",
            gameCode: "game-code",
            brandId: 1,
            currency: "USD"
        };
        const request: BonusCoinsRedeemRequest = {
            transactionId: await WalletFacade.generateTrxId(),
            roundId: "1",
            gameSessionId: "1",
            extTransactionId: "qwwer",
            gameToken: "token",
            promoId: "qwerty",
            amount: 10,
            ts: new Date().toISOString()
        };

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: { get: () => ([]) },
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(TRANSACTION_EXISTS),
        }));
        findTransactionStub.returns(Promise.reject(new WalletErrors.TransactionIsProcessing()));
        await playService.redeemBonusCoins(gameTokenData, request, bonusCoinRewardFilter)
            .should.eventually.rejectedWith(WalletErrors.TransactionIsProcessing);

        redeemBonusCoins.restore();
        redeem.restore();
        getBalance.restore();
    });

    it("Fail to redeem bonus coins, unknown error", async () => {
        const walletKey = "player:9:playerCode:USD";
        findTransactionStub.returns(Promise.resolve({
            id: "betTransactionId1",
            data: [ { account: "main", amount: -2000, trxType: "bet", walletKey: walletKey } ]
        }));

        const redeemBonusCoins = stub(PlayerBonusCoinWallet.prototype, "redeemBonusCoins");
        const redeem = stub(PlayerWalletImpl.prototype, "redeem");
        redeemBonusCoins.returns();
        redeem.resolves();

        const getBalance = stub(PlayerWalletImpl.prototype, "getBalance");
        getBalance.returns(Promise.resolve({ main: 676 }));

        const accountIncMock = mock();
        accountIncMock.twice()
            .withArgs(PLAYER.PLAYER_BALANCE, 2000, "bet")
            .withArgs(PLAYER.PLAYER_BALANCE, -1000, "debit");
        const account = {
            inc: accountIncMock,
            data: {},
            get: () => {
                return 2000;
            },
        };
        const walletAccounts = {};
        const getAccountMock = mock();
        getAccountMock.twice().returns(account);
        walletAccounts["get"] = getAccountMock;

        const walletMock = {
            key: walletKey,
            data: mock(),
            accounts: walletAccounts,
            changes: { get: () => ([]) }
        };

        const getWalletFromTransMock = mock();
        getWalletFromTransMock.twice().returns(walletMock);

        const gameTokenData: GameTokenData = {
            playerCode: "PL00001",
            gameCode: "game-code",
            brandId: 1,
            currency: "USD"
        };
        const request: BonusCoinsRedeemRequest = {
            transactionId: await WalletFacade.generateTrxId(),
            roundId: "1",
            gameSessionId: "1",
            extTransactionId: "qwwer",
            gameToken: "token",
            promoId: "qwerty",
            amount: 10,
            ts: new Date().toISOString()
        };

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: { get: () => ([]) },
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(TRANSACTION_EXISTS),
        }));

        findTransactionStub.returns(Promise.reject("error"));
        await playService.redeemBonusCoins(gameTokenData, request, bonusCoinRewardFilter)
            .should.eventually.rejectedWith("error");

        redeemBonusCoins.restore();
        redeem.restore();
        getBalance.restore();
    });

    it("Fail to external transfer", async () => {
        const walletKey = "player:9:playerCode:USD";

        findTransactionStub.returns(Promise.resolve({
            id: "betTransactionId1",
            data: [ { account: "main", amount: -2000, trxType: "bet", walletKey: walletKey } ]
        }));

        const commitGamePayment = stub(PlayerWalletImpl.prototype, "commitGamePayment").resolves();
        const getBalance = stub(PlayerWalletImpl.prototype, "getPlayerBalance").resolves({ main: 111 });

        const accountIncMock = mock();
        const account = {
            inc: accountIncMock,
            get: () => {
                return 2000;
            },
        };
        const walletAccounts = {};
        const getAccountMock = mock();
        getAccountMock.twice().returns(account);
        walletAccounts["get"] = getAccountMock;

        const walletMock = {
            key: walletKey,
            data: mock(),
            accounts: walletAccounts,
            changes: mock(),
            redeemBonusCoins: mock(),
            getBalance: mock(),
            getResultBalance: () => ({
                main: 222
            })
        };

        const getWalletFromTransMock = mock();
        getWalletFromTransMock.once().returns(walletMock);

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(INSUFFICIENT_BALANCE),
        }));

        getWalletStub.returns(Promise.resolve(walletMock));
        const gameTokenData: GameTokenData = {
            playerCode: "PL00001",
            gameCode: "game-code",
            brandId: 1,
            currency: "USD"
        };
        const request: ExternalTransferData = {
            transactionId: await WalletFacade.generateTrxId(),
            roundId: "1",
            gameSessionId: "1",
            operation: "transfer-in",
            gameToken: "token",
            amount: 10,
            ts: new Date().toISOString(),
        };
        await playService.externalTransfer(gameTokenData, request)
            .should.eventually.rejectedWith(WalletErrors.InsufficientEntityBalanceError);

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(BAD_TRANSACTION_ID),
        }));
        await playService.externalTransfer(gameTokenData, request)
            .should.eventually.rejectedWith(WalletErrors.BadTransactionId);

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(TRANSACTION_EXISTS),
        }));
        const result = await playService.externalTransfer(gameTokenData, request);
        expect(result).deep.equal({ main: 111 });

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject("error"),
        }));
        await playService.externalTransfer(gameTokenData, request)
            .should.eventually.rejectedWith("error");
        commitGamePayment.restore();
        getBalance.restore();
    });

    it("Rollback payment", async () => {
        const rollback = stub(playService as any, "rollbackChange");
        const walletKey = "player:9:playerCode:USD";
        const gameTokenData: GameTokenData = {
            playerCode: "PL00001",
            gameCode: "game-code",
            brandId: 1,
            currency: "USD"
        };
        const request: RollbackBetRequest = {
            originalTransactionId: await WalletFacade.generateTrxId(),
            roundId: 1,
            extTransactionId: "qwwer",
            gameToken: "token"
        };
        const accountIncMock = mock();
        accountIncMock.once().withArgs(PLAYER.PLAYER_BALANCE, 2000, "bet");
        const account = {
            inc: accountIncMock,
            get: () => {
                return 2000;
            },
        };
        const walletAccounts = {};
        const getAccountMock = mock();
        getAccountMock.twice().returns(account);
        walletAccounts["get"] = getAccountMock;

        const walletMock = {
            key: walletKey,
            data: mock(),
            accounts: walletAccounts,
            changes: mock(),
        };

        const geBalance = stub(PlayerWalletImpl.prototype, "getPlayerBalance").resolves({ main: 111 });

        const getWalletFromTransMock = mock();
        getWalletFromTransMock.atLeast(1).returns(walletMock);
        findTransactionStub.returns(Promise.reject("error"));
        await playService.rollbackBetPayment(gameTokenData, request)
            .should.eventually.rejectedWith(PlayServiceErrors.NotReadyToRollbackTransaction);

        findTransactionStub.returns(Promise.resolve());
        await playService.rollbackBetPayment(gameTokenData, request)
            .should.eventually.rejectedWith(PlayServiceErrors.TransactionDoesNotExist);

        findTransactionStub.returns(Promise.resolve({
            id: "betTransactionId1",
            data: [ { account: "main", amount: -2000, trxType: "bet", walletKey: walletKey } ]
        }));
        rollback.returns(Promise.resolve([]));
        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(BAD_TRANSACTION_ID),
        }));
        await playService.rollbackBetPayment(gameTokenData, request)
            .should.eventually.rejectedWith(WalletErrors.BadTransactionId);

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(TRANSACTION_EXISTS),
        }));
        const rollbackResult = await playService.rollbackBetPayment(gameTokenData, request);
        expect(rollbackResult).deep.equal({ main: 111 });

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject("error"),
        }));
        await playService.rollbackBetPayment(gameTokenData, request)
            .should.eventually.rejectedWith("error");
        rollback.restore();
        geBalance.restore();
    });

    it("Fail to transfer balance", async () => {
        const gameTokenData: GameTokenData = {
            playerCode: "PL00001",
            gameCode: "game-code",
            brandId: 1,
            currency: "USD"
        };
        const request: TransferRequest = {
            transactionId: await WalletFacade.generateTrxId(),
            roundId: "qwqw",
            gameSessionId: "1",
            amount: 10,
            operation: "transfer-in",
            gameToken: "token",
            ts: new Date().toISOString(),
        };
        await playService.transferBalance(gameTokenData, request)
            .should.eventually.rejectedWith(PlayServiceErrors.UnsupportedPlayMethodError);
    });

    it("Get player balance", async () => {
        const getBalance = stub(PlayerWalletImpl.prototype, "getPlayerBalance").resolves({ main: 676 });
        const balance = await playService.getPlayerBalances({
            brandId: 1,
            currency: "USD",
            playerCode: "player011"
        } as GameTokenData);
        expect(balance).deep.equal({ USD: { main: 676 } });
        getBalance.restore();
    });

    it("Get game balance with internal filter", async () => {
        const walletKey = "player:1:player011:USD";
        const account = {
            inc: undefined,
            get: () => {
                return 2000;
            },
        };
        const walletAccounts = {};
        const getAccountMock = mock();
        getAccountMock.twice().returns(account);
        walletAccounts["get"] = getAccountMock;

        const walletMock = {
            key: walletKey,
            data: mock(),
            accounts: walletAccounts,
            changes: mock(),
            redeemBonusCoins: mock(),
            getBalance: mock(),
            getResultBalance: () => ({
                main: 222
            })
        };
        getWalletStub.returns(Promise.resolve(walletMock));
        const balance = await playService.getGameBalance({
            brandId: 1,
            currency: "USD",
            playerCode: "player011"
        } as GameTokenData, ONLY_INTERNAL_BALANCE_FILTER);
        expect(balance).deep.equal({ "main": 20 });
    });

    it("Commit win without token flow", async () => {
        const walletKey = "player:9:playerCode:USD";
        const commitGamePayment = stub(PlayerWalletImpl.prototype, "commitGamePayment");
        const getResultBalance = stub(PlayerWalletImpl.prototype, "getResultBalance");
        commitGamePayment.resolves();
        getResultBalance.resolves({ main: 456 });

        const accountIncMock = mock();
        const account = {
            inc: accountIncMock,
            get: () => {
                return 2000;
            },
        };
        const walletAccounts = {};
        const getAccountMock = mock();
        getAccountMock.twice().returns(account);
        walletAccounts["get"] = getAccountMock;

        const walletMock = {
            key: walletKey,
            data: mock(),
            accounts: walletAccounts,
            changes: mock(),
            redeemBonusCoins: mock(),
            getBalance: mock()
        };

        const getWalletFromTransMock = mock();
        getWalletFromTransMock.atLeast(1).returns(walletMock);

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(INSUFFICIENT_BALANCE),
        }));
        await playService.commitWinWithoutToken({
            brandId: 1,
            roundId: "qwwqw",
            code: "code",
            currency: "USD"
        } as PaymentRequestWithoutToken)
            .should.eventually.rejectedWith(WalletErrors.InsufficientEntityBalanceError);

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(BAD_TRANSACTION_ID),
        }));
        await playService.commitWinWithoutToken({
            brandId: 1,
            roundId: "qwwqw",
            code: "code",
            currency: "USD"
        } as PaymentRequestWithoutToken)
            .should.eventually.rejectedWith(WalletErrors.BadTransactionId);

        findTransactionStub.returns(Promise.resolve({
            id: "betTransactionId1",
            data: [ { account: "main", amount: -2000, trxType: "bet", walletKey: walletKey } ]
        }));
        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(TRANSACTION_EXISTS),
        }));
        const result = await playService.commitWinWithoutToken({
            brandId: 1,
            code: "code",
            roundId: "qwwqw",
            currency: "USD"
        } as PaymentRequestWithoutToken);
        expect(result).deep.equal({ main: 456 });

        findTransactionStub.returns(Promise.reject(new WalletErrors.TransactionIsProcessing()));
        await playService.commitWinWithoutToken({
            brandId: 1,
            code: "code",
            roundId: "qwwqw",
            currency: "USD"
        } as PaymentRequestWithoutToken)
            .should.eventually.rejectedWith(WalletErrors.TransactionIsProcessing);

        findTransactionStub.returns(Promise.reject("error"));
        await playService.commitWinWithoutToken({
            brandId: 1,
            code: "code",
            roundId: "qwwqw",
            currency: "USD"
        } as PaymentRequestWithoutToken)
            .should.eventually.rejectedWith("error");

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject("some error"),
        }));
        await playService.commitWinWithoutToken({
            brandId: 1,
            roundId: "qwwqw",
            code: "code",
            currency: "USD"
        } as PaymentRequestWithoutToken)
            .should.eventually.rejectedWith("some error");

        commitGamePayment.restore();
        getResultBalance.restore();
    });

    it("Commit game payment", async () => {
        const walletKey = "player:9:playerCode:USD";
        const commitGamePayment = stub(PlayerWalletImpl.prototype, "commitGamePayment");
        const getResultBalance = stub(PlayerWalletImpl.prototype, "getResultBalance");
        const getBalance = stub(PlayerWalletImpl.prototype, "getPlayerBalance");
        commitGamePayment.resolves();
        getResultBalance.resolves({ main: 456 });
        getBalance.resolves({ main: 543 });

        const accountIncMock = mock();
        const account = {
            inc: accountIncMock,
            data: {},
            get: () => {
                return 2000;
            },
        };
        const walletAccounts = {};
        const getAccountMock = mock();
        getAccountMock.atLeast(1).returns(account);
        walletAccounts["get"] = getAccountMock;

        const walletMock = {
            key: walletKey,
            data: mock(),
            accounts: walletAccounts,
            changes: mock(),
        };

        const getWalletFromTransMock = mock();
        getWalletFromTransMock.atLeast(1).returns(walletMock);

        const gameTokenData = {
            playerCode: "PL00001",
            gameCode: "game-code",
            brandId: 1,
            currency: "USD"
        };
        const request = {
            transactionId: await WalletFacade.generateTrxId(),
            gameToken: "token",
            roundId: "qwe",
            gameSessionId: "1",
            ts: new Date().toISOString()
        };
        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(INSUFFICIENT_BALANCE),
        }));
        await playService.commitGamePayment(gameTokenData, request)
            .should.eventually.rejectedWith(WalletErrors.InsufficientBalanceError);

        await playService.commitGamePayment(gameTokenData, { ...request, freeBetCoin: 0.1 })
            .should.eventually.rejectedWith(PromoWalletErrors.InsufficientFreebet);

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(BAD_TRANSACTION_ID),
        }));
        await playService.commitGamePayment(gameTokenData, request)
            .should.eventually.rejectedWith(WalletErrors.BadTransactionId);

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject(TRANSACTION_EXISTS),
        }));
        let result = await playService.commitGamePayment(gameTokenData, request);
        expect(result).deep.equal({ main: 543 });
        const commitMock = mock().returns({});
        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: commitMock
        }));
        result = await playService.commitGamePayment(gameTokenData, request);
        expect(result).deep.equal({ main: 456 });
        expect(commitMock.callCount).eq(1);
        expect(commitMock.args[0].length).eq(0);

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.ROLLBACK,
                operationName: WALLET_OPERATION_NAME.PLAY_ROLLBACK,
                gameId: "2",
                externalTrxId: "extTxId",
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.reject("error"),
        }));
        await playService.commitGamePayment(gameTokenData, request)
            .should.eventually.rejectedWith("error");

        getBalance.restore();
        getResultBalance.restore();
        commitGamePayment.restore();
    });

    it("Start game for mrch populates currency replacement", async () => {
        getWalletStub.returns(Promise.resolve({
            accounts: {
                get: () => ({
                    get: () => 10,
                    data: {
                        stars: 5,
                        [`promo/1/1/amount/4/5/${+Date.now() - 1000}/${+promoEndDate}/amount`]: 60,
                        [`promo/1/1/amount/4/5/${+Date.now() - 1000}/${+promoEndDate}/expireIn`]: *********
                    }
                }),
            }
        }));

        validatePlayerRestrictions.returns(Promise.resolve({
            hasRealityCheck: () => true,
            getRealityCheck: () => false
        }));

        getBrandStub.returns(Promise.resolve({
            findPlayer: mock(),
            type: ENTITY_TYPE.MERCHANT,
            path: "",
            name: "brand",
            isSuspended: () => false,
            isMaster: () => true,
            isMerchant: true,
            countryExists: () => true,
            currencyExists: () => true,
            underMaintenance: () => false,
            getVersion: () => 1,
            id: 1,
            getCountries: () => [ "AU" ]
        }));

        findOneEntityGame.returns(Promise.resolve({
            game: {
                limits: {
                    USD: {
                        stakeAll: [ 1, 2, 3 ],
                        stakeMax: 3,
                        stakeMin: 1,
                        stakeDef: 1
                    }
                },
                features: {},
                getLimits: () => ({
                    stakeAll: [ 1, 2, 3 ],
                    stakeMax: 3,
                    stakeMin: 1,
                    stakeDef: 1
                })
            },
            settings: {},
            isLiveGame: () => false
        }));

        verifyStartGameToken.returns(Promise.resolve({
            brandId: 1,
            playmode: PlayMode.REAL,
            gameCode: "GAME001",
            currency: "USD",
            playerCode: "PL0001"
        }));

        getEntitySettings.returns(Promise.resolve({ enablePhantomFeatures: true }));

        findOnePlayer.returns(Promise.resolve({
            isSuspended: () => false,
            toInfo: () => ({})
        }));

        const externalCode = "super.domain.com";
        const site = await getAvailableSiteService({ id: 1 } as any)
            .create({ url: `http://${externalCode}`, externalCode, status: AVAILABLE_SITE_STATUSES.NORMAL });

        findOneMerchant.returns({
            findOne: () => Promise.resolve({
                getGameTokenInfo: () => {
                    return {
                        gameTokenData: {
                            brandId: 1,
                            playmode: PlayMode.REAL,
                            gameCode: "GAME001",
                            currency: "USD",
                            playerCode: "PL0001"
                        },
                        operatorSiteExternalCode: "super.domain.com"
                    };
                },
                // tslint:disable-next-line:no-empty
                toInfo: () => {
                },
                params: {
                    gameClientCurrencyReplacement: {
                        "USD": "PS"
                    }
                },
                getAdapter: async () => {
                    return {
                        getBalances: () => {
                            return {
                                USD: {
                                    main: 0.1,
                                }
                            };
                        },
                    };
                }
            })
        });

        const result = await PlayService.startGame({} as StartGameRequest, "1.0.4.0");
        delete result.gameToken;
        expect(result).deep.equal({
            balance: {
                main: 0.1,
            },
            player: {
                code: "PL0001",
                country: "AU",
                currency: "USD",
                email: undefined,
                firstName: undefined,
                isTest: false,
                language: undefined,
                lastName: undefined,
                nickname: undefined,
                status: undefined,
                isVip: false,
                brandId: 1,
                isPublicChatBlock: false,
                isPrivateChatBlock: false,
                isTracked: false,
                hasWarn: false,
                nicknameChangeAttemptsLeft: undefined
            },
            limits: {
                stakeAll: [ 1, 2, 3 ],
                stakeMax: 3,
                stakeMin: 1,
                stakeDef: 1
            },
            brandSettings: {
                fullscreen: true
            },
            gameSettings: {},
            settings: {
                transferEnabled: undefined,
                rtpConfigurator: undefined
            },
            jrsdSettings: {
                realityCheck: false
            },
            jurisdictionCode: undefined,
            playedFromCountry: "AU",
            region: "default",
            currencyReplacement: "PS",
            logoutOptions: undefined,
            phantom: { jackpots: [ { jackpotId: "phantom_jackpot" } ] },
            jackpots: [
                {
                    id: "phantom_jackpot",
                    allowMissingContribution: false,
                    jackpotId: "phantom_jackpot",
                    isGameOwned: false,
                    gameHistoryEnabled: true,
                    paymentStatisticEnabled: true,
                    winPaymentType: "jackpot_win",
                    type: "mwjp"
                }
            ],
            renderType: undefined,
            brandInfo: {
                name: "brand",
                title: ""
            },
            operatorSiteId: site.id,
            useGameProviderLimits: undefined,
        });
    });

    it("Start game with phantom payload", async () => {
        getWalletStub.returns(Promise.resolve({
            accounts: {
                get: () => ({
                    get: () => 10,
                    data: {
                        stars: 5,
                        [`promo/1/1/amount/4/5/${+Date.now() - 1000}/${+promoEndDate}/amount`]: 60,
                        [`promo/1/1/amount/4/5/${+Date.now() - 1000}/${+promoEndDate}/expireIn`]: *********
                    }
                }),
            }
        }));

        validatePlayerRestrictions.returns(Promise.resolve({
            hasRealityCheck: () => true,
            getRealityCheck: () => false
        }));
        verifyStartGameToken.returns(Promise.reject(new token.TokenVerifyException()));
        await PlayService.startGame({} as StartGameRequest, "1.0.4.0")
            .should.eventually.rejectedWith(Errors.StartGameTokenError);

        verifyStartGameToken.returns(Promise.reject(new token.TokenExpiredException()));
        await PlayService.startGame({} as StartGameRequest, "1.0.4.0")
            .should.eventually.rejectedWith(Errors.StartGameTokenExpired);

        verifyStartGameToken.returns(Promise.reject("error"));
        await PlayService.startGame({} as StartGameRequest, "1.0.4.0")
            .should.eventually.rejectedWith("error");

        getBrandStub.returns(Promise.resolve({
            underMaintenance: () => true
        }));
        verifyStartGameToken.returns(Promise.resolve({ brandId: 1 }));
        await PlayService.startGame({} as StartGameRequest, "1.0.4.0")
            .should.eventually.rejectedWith(Errors.EntityUnderMaintenanceError);

        getBrandStub.returns(Promise.resolve({
            findPlayer: mock(),
            type: ENTITY_TYPE.BRAND,
            path: "",
            name: "brand",
            isSuspended: () => false,
            isMaster: () => true,
            countryExists: () => true,
            underMaintenance: () => false,
            getVersion: () => 1,
            getCountries: () => [ "AU" ]
        }));

        findOneEntityGame.returns(Promise.resolve({
            game: {
                limits: {
                    USD: {
                        stakeAll: [ 1, 2, 3 ],
                        stakeMax: 3,
                        stakeMin: 1,
                        stakeDef: 1
                    }
                },
                features: {},
                getLimits: () => ({
                    stakeAll: [ 1, 2, 3 ],
                    stakeMax: 3,
                    stakeMin: 1,
                    stakeDef: 1
                })
            },
            settings: {},
            isLiveGame: () => false
        }));

        verifyStartGameToken.returns(Promise.resolve({
            brandId: 1,
            playmode: PlayMode.REAL,
            gameCode: "GAME001",
            currency: "USD",
            playerCode: "PL0001"
        }));

        getEntitySettings.returns(Promise.resolve({ enablePhantomFeatures: true }));

        findOnePlayer.returns(Promise.resolve({
            isSuspended: () => true,
            toInfo: () => ({})
        }));

        await PlayService.startGame({} as StartGameRequest, "1.0.4.0")
            .should.eventually.rejectedWith(Errors.PlayerIsSuspended);

        findOnePlayer.returns(Promise.resolve({
            isSuspended: () => false,
            toInfo: () => ({})
        }));

        const result = await PlayService.startGame({} as StartGameRequest, "1.0.4.0");
        delete result.gameToken;
        expect(result).deep.equal({
            balance: {
                main: 0.1,
            },
            player: {
                nicknameChangeAttemptsLeft: undefined
            },
            limits: {
                stakeAll: [ 1, 2, 3 ],
                stakeMax: 3,
                stakeMin: 1,
                stakeDef: 1
            },
            brandSettings: {
                fullscreen: true
            },
            gameSettings: {},
            settings: {
                rtpConfigurator: undefined
            },
            jrsdSettings: {
                realityCheck: false
            },
            jurisdictionCode: undefined,
            playedFromCountry: "AU",
            region: "default",
            phantom: { jackpots: [ { jackpotId: "phantom_jackpot" } ] },
            jackpots: [
                {
                    id: "phantom_jackpot",
                    allowMissingContribution: false,
                    jackpotId: "phantom_jackpot",
                    isGameOwned: false,
                    gameHistoryEnabled: true,
                    paymentStatisticEnabled: true,
                    winPaymentType: "jackpot_win",
                    type: "mwjp"
                }
            ],
            renderType: undefined,
            brandInfo: {
                name: "brand",
                title: ""
            },
            useGameProviderLimits: undefined,
        });
    });

    it("Start game without phantom payload for a player", async () => {
        getWalletStub.returns(Promise.resolve({
            accounts: {
                get: () => ({
                    get: () => 10,
                    data: {
                        stars: 5,
                        [`promo/1/1/amount/4/5/${+Date.now() - 1000}/${+promoEndDate}/amount`]: 60,
                        [`promo/1/1/amount/4/5/${+Date.now() - 1000}/${+promoEndDate}/expireIn`]: *********
                    }
                }),
            }
        }));

        validatePlayerRestrictions.returns(Promise.resolve({
            hasRealityCheck: () => true,
            getRealityCheck: () => false
        }));
        getBrandStub.returns(Promise.resolve({
            findPlayer: mock(),
            type: ENTITY_TYPE.MERCHANT,
            path: "",
            name: "brand",
            isSuspended: () => false,
            isMaster: () => true,
            countryExists: () => true,
            underMaintenance: () => false,
            getVersion: () => 1,
            getCountries: () => [ "AU" ]
        }));

        findOneEntityGame.returns(Promise.resolve({
            game: {
                limits: {
                    USD: {
                        stakeAll: [ 1, 2, 3 ],
                        stakeMax: 3,
                        stakeMin: 1,
                        stakeDef: 1
                    }
                },
                features: {},
                getLimits: () => ({
                    stakeAll: [ 1, 2, 3 ],
                    stakeMax: 3,
                    stakeMin: 1,
                    stakeDef: 1
                })
            },
            settings: {},
            isLiveGame: () => false
        }));

        verifyStartGameToken.returns(Promise.resolve({
            brandId: 1,
            playmode: PlayMode.REAL,
            gameCode: "GAME001",
            currency: "USD",
            playerCode: "PL0001",
            disablePlayerPhantomFeatures: true
        }));

        getEntitySettings.returns(Promise.resolve({ enablePhantomFeatures: true }));

        findOnePlayer.returns(Promise.resolve({
            isSuspended: () => false,
            toInfo: () => ({})
        }));

        const result = await PlayService.startGame({} as StartGameRequest, "1.0.4.0");
        delete result.gameToken;
        expect(result).deep.equal({
            balance: {
                main: 0.1,
            },
            player: {
                nicknameChangeAttemptsLeft: undefined
            },
            limits: {
                stakeAll: [ 1, 2, 3 ],
                stakeMax: 3,
                stakeMin: 1,
                stakeDef: 1
            },
            brandSettings: {
                fullscreen: true
            },
            gameSettings: {},
            settings: {
                rtpConfigurator: undefined
            },
            jrsdSettings: {
                realityCheck: false
            },
            jurisdictionCode: undefined,
            playedFromCountry: "AU",
            region: "default",
            renderType: undefined,
            brandInfo: {
                name: "brand",
                title: ""
            },
            useGameProviderLimits: undefined,
        });
    });

    it("Start game with PT jurisdiction code and buyFeatureJrsdPtLegacy", async () => {
        getWalletStub.returns(Promise.resolve({
            accounts: {
                get: () => ({
                    get: () => 10,
                    data: {
                        stars: 5,
                        [`promo/1/1/amount/4/5/${+Date.now() - 1000}/${+promoEndDate}/amount`]: 60,
                        [`promo/1/1/amount/4/5/${+Date.now() - 1000}/${+promoEndDate}/expireIn`]: *********
                    }
                }),
            }
        }));

        validatePlayerRestrictions.returns(Promise.resolve({
            hasRealityCheck: () => true,
            getRealityCheck: () => false
        }));

        getBrandStub.returns(Promise.resolve({
            findPlayer: mock(),
            type: ENTITY_TYPE.MERCHANT,
            path: "",
            name: "merchant",
            isSuspended: () => false,
            isMaster: () => true,
            isMerchant: true,
            countryExists: () => true,
            currencyExists: () => true,
            underMaintenance: () => false,
            getVersion: () => 1,
            id: 1,
            getCountries: () => [ "AU" ]
        }));

        const entityGame = {
            game: {
                limits: {
                    USD: {
                        stakeAll: [ 1, 2, 3 ],
                        stakeMax: 3,
                        stakeMin: 1,
                        stakeDef: 1
                    }
                },
                features: {},
                clientFeatures: {
                    buyFeatureJrsdPtLegacy: true
                },
                getLimits: () => ({
                    stakeAll: [ 1, 2, 3 ],
                    stakeMax: 3,
                    stakeMin: 1,
                    stakeDef: 1
                })
            },
            settings: {},
            isLiveGame: () => false
        };

        findOneEntityGame.returns(Promise.resolve(entityGame));

        verifyStartGameToken.returns(Promise.resolve({
            brandId: 1,
            playmode: PlayMode.REAL,
            gameCode: "GAME001",
            currency: "USD",
            playerCode: "PL0001",
            disablePlayerPhantomFeatures: true
        }));

        getEntitySettings.returns(Promise.resolve({ enablePhantomFeatures: true }));

        findOneMerchant.returns({
            findOne: () => Promise.resolve({
                getGameTokenInfo: () => {
                    return {
                        gameTokenData: {
                            brandId: 1,
                            playmode: PlayMode.REAL,
                            gameCode: "GAME001",
                            currency: "USD",
                            playerCode: "PL0001"
                        }
                    };
                },
                // tslint:disable-next-line:no-empty
                toInfo: () => {
                },
                params: {},
                getAdapter: async () => {
                    return {
                        getBalances: () => {
                            return {
                                USD: {
                                    main: 0.1,
                                }
                            };
                        },
                    };
                }
            })
        });

        const jurisdiction = {
            code: "PT",
            settings: {
                buyFeature: false
            }
        };
        findAllJurisdictions.returns([jurisdiction]);

        let result = await PlayService.startGame({} as StartGameRequest, "1.0.4.0");
        delete result.gameToken;

        expect(result.jrsdSettings).deep.equal({
            buyFeature: true,
            realityCheck: false
        });

        expect(result.jurisdictionCode).equal("PT");
        expect(result.gameSettings).deep.equal({
            buyFeatureJrsdPtLegacy: true
        });

        expect(result).deep.equal({
            balance: {
                main: 0.1,
            },
            player: {
                brandId: 1,
                code: "PL0001",
                country: "AU",
                currency: "USD",
                email: undefined,
                firstName: undefined,
                hasWarn: false,
                isPrivateChatBlock: false,
                isPublicChatBlock: false,
                isTest: false,
                isTracked: false,
                isVip: false,
                language: undefined,
                lastName: undefined,
                nickname: undefined,
                nicknameChangeAttemptsLeft: undefined,
                status: undefined
            },
            limits: {
                stakeAll: [ 1, 2, 3 ],
                stakeMax: 3,
                stakeMin: 1,
                stakeDef: 1
            },
            brandSettings: {
                fullscreen: true
            },
            gameSettings: {
                buyFeatureJrsdPtLegacy: true
            },
            settings: {
                rtpConfigurator: undefined,
                transferEnabled: undefined
            },
            jrsdSettings: {
                buyFeature: true,
                realityCheck: false
            },
            jurisdictionCode: "PT",
            logoutOptions: undefined,
            operatorSiteId: undefined,
            playedFromCountry: "AU",
            region: "default",
            renderType: undefined,
            brandInfo: {
                name: "merchant",
                title: ""
            },
            useGameProviderLimits: undefined
        });

        entityGame.game.clientFeatures.buyFeatureJrsdPtLegacy = false;
        jurisdiction.settings.buyFeature = false;

        result = await PlayService.startGame({} as StartGameRequest, "1.0.4.0");
        delete result.gameToken;

        expect(result.jrsdSettings).deep.equal({
            buyFeature: false,
            realityCheck: false
        });

        expect(result.jurisdictionCode).equal("PT");
        expect(result.gameSettings).deep.equal({
            buyFeatureJrsdPtLegacy: false
        });

        delete entityGame.game.clientFeatures.buyFeatureJrsdPtLegacy;
        jurisdiction.settings.buyFeature = false;

        result = await PlayService.startGame({} as StartGameRequest, "1.0.4.0");
        delete result.gameToken;

        expect(result.jrsdSettings).deep.equal({
            buyFeature: false,
            realityCheck: false
        });

        expect(result.jurisdictionCode).equal("PT");
        expect(result.gameSettings).deep.equal({});

        delete jurisdiction.settings.buyFeature;

        result = await PlayService.startGame({} as StartGameRequest, "1.0.4.0");
        delete result.gameToken;

        expect(result.jrsdSettings).deep.equal({
            realityCheck: false
        });

        expect(result.jurisdictionCode).equal("PT");
        expect(result.gameSettings).deep.equal({});
    });

    it("Finalize Game", async () => {
        const gameTokenData = {
            playerCode: "PL00001",
            gameCode: "game-code",
            brandId: 1,
            currency: "USD",
            playeMode: "real"
        };
        verifyGameToken.returns(Promise.resolve(gameTokenData));
        const trxId = await WalletFacade.generateTrxId();
        const finalizeGameRequest: FinalizeGameRequest = {
            gameToken: await generateGameToken(gameTokenData),
            roundId: "111",
            roundPID: "roundPid",
            roundStatistics: undefined,
            transactionId: trxId,
            operation: "finalize-game",
            finalizationType: BrandFinalizationType.ROUND_STATISTICS,
            eventId: 1,
            deviceId: "web",
            ts: new Date().toISOString()
        };
        logExternalTransaction.returns(Promise.resolve());
        await PlayService.finalizeGame(finalizeGameRequest);

        expect(logExternalTransaction.args[0]).deep.equal([
            {
                changes: [
                    {
                        account: "main",
                        amount: 0,
                        property: "balance",
                        trxType: "bet",
                        walletKey: "player:1:PL00001:USD"
                    },
                    {
                        account: "main",
                        amount: 0,
                        property: "balance",
                        trxType: "win",
                        walletKey: "player:1:PL00001:USD"
                    }
                ],
                operation: {
                    gameId: "111",
                    operationId: 1,
                    operationName: "play",
                    params: {
                        finalized: true,
                        gameCode: "game-code",
                        isTest: false,
                        roundEnded: true
                    }
                },
                trxId: trxId
            }
        ]);
    });

    it("Commit offline deferred payment", async () => {
        const payment: DeferredPayment = {
            id: "1111",
            brandId: 1,
            paymentType: "tournament",
            paymentMethod: DeferredPaymentMethod.BONUS,
            playerCode: "player001",
            amount: 1000,
            transactionId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            gameCode: "game001",
            currencyCode: "USD",
            sourceTransactionId: "test123"
        } as any;

        const accountIncMock = mock();
        const account = {
            inc: accountIncMock,
            data: {},
            get: () => {
                return 2000;
            },
        };
        const walletAccounts = {};
        const getAccountMock = mock();
        getAccountMock.atLeast(1).returns(account);
        walletAccounts["get"] = getAccountMock;

        const walletMock = {
            key: "player:1:player001:USD",
            data: mock(),
            accounts: walletAccounts,
            changes: mock(),
        };

        const getWalletFromTransMock = mock();
        getWalletFromTransMock.atLeast(1).returns(walletMock);

        startTransactionStub.returns(Promise.resolve({
            trxId: "GWjztAAAArUAAAK2GWjztGSGZ5U=",
            operation: {
                operationId: OPERATION_ID.WIN,
                operationName: WALLET_OPERATION_NAME.BONUS,
            },
            changes: {},
            wallets: mock(),
            getWallet: getWalletFromTransMock,
            commit: () => Promise.resolve()
        }));

        await playService.commitOfflineDeferredPayment("promo_id", payment);
        expect(startTransactionStub.args).deep.eq([
            [
                "GWjztAAAArUAAAK2GWjztGSGZ5U=",
                {
                    operationId: 1,
                    operationName: "bonus",
                    params: {
                        deferredPaymentId: "1111",
                        externalTrxId: "promo_id",
                        gameCode: "game001",
                        paymentType: "tournament",
                        sourceTransactionId: "test123"
                    }
                }
            ]
        ]);
        expect(accountIncMock.args).deep.eq([ [ "balance", 100000, "credit" ] ]);
    });
});
