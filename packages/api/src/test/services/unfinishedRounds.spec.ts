import { suite, test, timeout } from "mocha-typescript";
import { expect, should, use } from "chai";
import { BaseEntity } from "../../skywind/entities/entity";
import { truncate } from "../entities/helper";
import { SinonStub, stub } from "sinon";
import * as Errors from "../../skywind/errors";
import { getRoundHistoryServiceFactory } from "../../skywind/history/gameHistoryServiceFactory";
import { verifyInternalToken } from "../../skywind/utils/token";
import { publicId } from "@skywind-group/sw-utils";
import { DynamicDomain } from "../../skywind/entities/domain";

const request = require("request");
const factory = require("factory-girl").factory;
const chaiAsPromised = require("chai-as-promised");

@suite(timeout(20000))
class UnfinishedRoundsSpec {
    public static brand: BaseEntity;
    public static domain: DynamicDomain;
    public static service = getRoundHistoryServiceFactory().getUnfinishedRoundsHistoryService();

    public requestGetMock: SinonStub;

    public static async before() {
        should();
        use(chaiAsPromised);

        await truncate();

        UnfinishedRoundsSpec.domain = await factory.create("DynamicDomain");
        UnfinishedRoundsSpec.brand = await factory.create("Brand", {}, {
            dynamicDomainId: UnfinishedRoundsSpec.domain.id,
            environment: UnfinishedRoundsSpec.domain.environment
        });

    }

    public before() {
        this.requestGetMock = stub(request, "get");
    }

    public after() {
        this.requestGetMock.restore();
    }

    @test()
    public async getRound() {
        const decodedGameContextId = "games:context:5:PL0001:sw_al:web";
        const mockRound = await factory.attrs("RoundHistory",
            { gameContextId: decodedGameContextId });
        this.requestGetMock.yields(null, { statusCode: 200 }, [mockRound]);

        const expectedGameContextId = "games:context:" + publicId.instance.encode(5) + ":PL0001:sw_al:web";

        const round = await UnfinishedRoundsSpec.service.findOne(UnfinishedRoundsSpec.brand, {
            playerCode: "chebureck", roundId: "Ax12Ec"});

        expect(this.requestGetMock.calledOnce).to.be.true;
        expect(this.requestGetMock.firstCall.args[0].includes(UnfinishedRoundsSpec.domain.domain)).to.be.true;
        expect(await verifyInternalToken(this.requestGetMock.firstCall.args[1].qs.token)).contains({
            brandId: UnfinishedRoundsSpec.brand.id,
            playerCode: "chebureck",
            roundId: "Ax12Ec",
            sortOrder: "DESC"
        });

        expect(round).contains({
            gameContextId: expectedGameContextId
        });
    }

    @test()
    public async getRounds() {
        const decodedGameContextId = "games:context:5:PL0001:sw_al:web";
        const mockRounds = await factory.attrsMany("RoundHistory",
            10,
            { gameContextId: decodedGameContextId });
        this.requestGetMock.yields(null, { statusCode: 200 }, mockRounds);

        const expectedGameContextId = "games:context:" + publicId.instance.encode(5) + ":PL0001:sw_al:web";

        let rounds = await UnfinishedRoundsSpec.service.getRounds(UnfinishedRoundsSpec.brand, {
            playerCode: "chebureck", roundId: "Ax12Ec"});

        expect(rounds.length).to.be.equal(10);
        expect(this.requestGetMock.calledOnce).to.be.true;
        expect(this.requestGetMock.firstCall.args[0].includes(UnfinishedRoundsSpec.domain.domain)).to.be.true;
        expect(await verifyInternalToken(this.requestGetMock.firstCall.args[1].qs.token)).contains({
            brandId: UnfinishedRoundsSpec.brand.id,
            playerCode: "chebureck",
            roundId: "Ax12Ec",
            sortOrder: "DESC"
        });

        expect(rounds[0]).contains({
            gameContextId: expectedGameContextId
        });

        rounds = await UnfinishedRoundsSpec.service.getRounds(UnfinishedRoundsSpec.brand, {
            playerCode: "chebureck",
            roundId: "Ax12Ec",
            gameContextId: rounds[0].gameContextId
        });
        expect(rounds.length).to.be.equal(10);
        expect(this.requestGetMock.calledTwice).to.be.true;
        expect(await verifyInternalToken(this.requestGetMock.secondCall.args[1].qs.token)).contains({
            brandId: UnfinishedRoundsSpec.brand.id,
            playerCode: "chebureck",
            roundId: "Ax12Ec",
            sortOrder: "DESC",
            gameContextId: decodedGameContextId
        });
    }

    @test()
    public async getRoundsWhenGameServerUnavailable() {
        this.requestGetMock.throws(new Errors.ErrorQueryingGameServer());

        await UnfinishedRoundsSpec.service.getRounds(UnfinishedRoundsSpec.brand, {
            playerCode: "chebureck", sortOrder: "ASC",  status: "broken", firstTs__gte: "2015-05-15" })
            .should.eventually.rejectedWith(Errors.ErrorQueryingGameServer);

        expect(this.requestGetMock.calledOnce).to.be.true;
        expect(await verifyInternalToken(this.requestGetMock.firstCall.args[1].qs.token)).contains({
            brandId: UnfinishedRoundsSpec.brand.id,
            playerCode: "chebureck",
            sortOrder: "ASC",
            status: "broken",
            ts__gte: Date.parse("2015-05-15")
        });
    }

    @test()
    public async getRoundsWithoutPlayerCode() {
        await UnfinishedRoundsSpec.service.getRounds(UnfinishedRoundsSpec.brand, {
            sortOrder: "ASC",  status: "broken", firstTs__gte: "2015-05-15", playerCode: undefined })
            .should.eventually.rejectedWith(Errors.ValidationError).then(err => {
                expect(err.message).to.be.equal("Validation error: one of parameters should be present: playerCode, roundId, gameContextId");
            });

        expect(this.requestGetMock.called).to.be.false;
    }

}
