import {
    AuthSessionServiceImpl,
    buildSessionKey,
    getAuthSessionService
} from "../../skywind/services/authSessionService";
import { expect, should, use } from "chai";
import * as chaiAsPromised from "chai-as-promised";
import { suite, test, timeout } from "mocha-typescript";
import config from "../../skywind/config";
import { UserType } from "../../skywind/entities/user";
import * as redis from "../../skywind/storage/redis";
import { truncate } from "../entities/helper";

@suite("AuthSessionSpec", timeout(20000))
class AuthSessionSpec {
    private static readonly expiresIn: number = 1;
    private authSessionService: AuthSessionServiceImpl = getAuthSessionService();

    public static async before() {
        await truncate();
        use(chaiAsPromised);
        should();

        config.accessToken.expiresIn = AuthSessionSpec.expiresIn;
    }

    public static async after() {
        config.accessToken.expiresIn = 3600;
    }

    private async delay(seconds: number): Promise<void> {
        return new Promise<void>(resolve => {
            setTimeout(resolve, seconds * 1000);
        });
    }

    private async checkNumberOfSessions(userId: number, expectedNumber: number) {
        await redis.usingDb(async (client) => {
            const numberOfSessions = await client.zcount(buildSessionKey(userId), 0, Date.now());

            expect(numberOfSessions).to.be.eq(expectedNumber);
        });
    }

    private buildTestData(startUserId: number) {
        return [{
            userId: startUserId++,
            type: UserType.OPERATOR_API,
            deny: null
        }, {
            userId: startUserId++,
            type: UserType.BO,
            deny: null
        }, {
            userId: startUserId++,
            type: UserType.BO,
            deny: false
        }, {
            userId: startUserId,
            type: UserType.BO,
            deny: true
        }];
    }

    @test("Save auth session in redis and check if it exists")
    public async createSessionAndCheckIfExists() {
        const testData = this.buildTestData(1);

        for (const input of testData) {
            const sessionId = await this.authSessionService.createSession(input.userId, input.type, input.deny);
            const checkResult = await this.authSessionService.checkSession(input.userId, sessionId);

            expect(checkResult).to.be.true;

            await this.authSessionService.removeOneUserSession(input.userId, sessionId);
            await this.authSessionService.checkSession(input.userId, sessionId)
                .should.eventually.rejectedWith("Access Session is expired");
        }
    }

    @test("Save 2 auth sessions in redis and check if they should exists")
    public async createTwoSessionsAndCheck() {
        const testData = this.buildTestData(5);

        for (const input of testData) {
            const sessionId1 = await this.authSessionService.createSession(input.userId, input.type, input.deny);
            const sessionId2 = await this.authSessionService.createSession(input.userId, input.type, input.deny);

            if (!(input.deny && input.type === UserType.BO)) {
                const checkResult1 = await this.authSessionService.checkSession(input.userId, sessionId1);
                expect(checkResult1).to.be.true;
            } else {
                await this.authSessionService.checkSession(input.userId, sessionId1)
                    .should.eventually.rejectedWith("Access Session is expired");
            }

            const checkResult2 = await this.authSessionService.checkSession(input.userId, sessionId2);
            expect(checkResult2).to.be.true;

            await this.authSessionService.removeOneUserSession(input.userId, sessionId1);
            await this.authSessionService.removeOneUserSession(input.userId, sessionId2);
        }
    }

    @test("Save and check after expiration")
    public async checkExpiredSession() {
        const testData = this.buildTestData(9);
        for (const input of testData) {
            const sessionId = await this.authSessionService.createSession(input.userId, input.type, input.deny);
            await this.delay(AuthSessionSpec.expiresIn + 1);

            await this.authSessionService.checkSession(input.userId, sessionId)
                .should.eventually.rejectedWith("Access Session is expired");
        }
    }

    @test("Test session Id for operator api user")
    public async checkSessionIdForOperatorAPIUser() {
        const userId = 14;
        const sessionId1 = await this.authSessionService.createSession(userId, UserType.OPERATOR_API, false);
        await this.authSessionService.checkSession(userId, sessionId1);
        const sessionId2 = await this.authSessionService.createSession(userId, UserType.OPERATOR_API, false);
        await this.authSessionService.checkSession(userId, sessionId2);

        await this.checkNumberOfSessions(userId, 2);
        
        await this.authSessionService.removeOneUserSession(userId, sessionId1);
        await this.authSessionService.checkSession(userId, sessionId1)
            .should.eventually.rejectedWith("Access Session is expired");
    }

    @test("Test session Id with multiple session Ids")
    public async checkSessionIdForOperatorApiWithMultipleSession() {
        const userId = 15;
        await this.authSessionService.createSession(userId, UserType.OPERATOR_API);
        await this.authSessionService.createSession(userId, UserType.OPERATOR_API);
        const sessionId = await this.authSessionService.createSession(userId, UserType.BO, true);
        await this.authSessionService.checkSession(userId, sessionId);

        await this.checkNumberOfSessions(userId, 1);
        
        await this.authSessionService.removeOneUserSession(userId, sessionId);
        await this.authSessionService.checkSession(userId, sessionId)
            .should.eventually.rejectedWith("Access Session is expired");
    }

    @test("Test session Id with multiple expired session Ids")
    public async checkSessionIdForOperatorApiWithMultipleExpiredSession() {
        const userId = 15;
        await this.authSessionService.createSession(userId, UserType.OPERATOR_API);
        await this.authSessionService.createSession(userId, UserType.OPERATOR_API);
        await this.delay(2);
        
        const sessionId = await this.authSessionService.createSession(userId, UserType.OPERATOR_API);
        await this.authSessionService.checkSession(userId, sessionId);

        await this.checkNumberOfSessions(userId, 1);

        await this.authSessionService.removeOneUserSession(userId, sessionId);
        await this.authSessionService.checkSession(userId, sessionId)
            .should.eventually.rejectedWith("Access Session is expired");
    }
    
    @test("Remove expired sessions during checkSession")
    public async testRemoveExpiredSessionDuringCheckSession() {
        config.accessToken.expiresIn = 3;
        
        const userId = 25;
        await this.authSessionService.createSession(userId, UserType.OPERATOR_API);
        await this.delay(1);
        await this.authSessionService.createSession(userId, UserType.OPERATOR_API);
        await this.delay(1);
        const sessionId = await this.authSessionService.createSession(userId, UserType.OPERATOR_API);
        await this.delay(2);

        await this.checkNumberOfSessions(userId, 3);
        await this.authSessionService.checkSession(userId, sessionId);
        await this.checkNumberOfSessions(userId, 1);
    }

    @test("Remove all sessions belong to user")
    public async removeAllUserSessions() {
        const userId = 35;
        await this.authSessionService.createSession(userId, UserType.OPERATOR_API);
        await this.authSessionService.createSession(userId, UserType.OPERATOR_API);
        await this.checkNumberOfSessions(userId, 2);

        await this.authSessionService.removeAllUserSessions(userId);
        await this.checkNumberOfSessions(userId, 0);
    }
}
