import { Application } from "express";
import * as http from "http";
import * as Socket<PERSON> from "socket.io-v2";
import { expect } from "chai";
import { complexStructure, createComplexStructure, truncate } from "../entities/helper";
import { factory } from "factory-girl";
import { FACTORY } from "../factories/common";
import * as EntityService from "../../skywind/services/entity";
import { BrandEntity } from "../../skywind/entities/brand";
import getPlayerService from "../../skywind/services/brandPlayer";
import EntitySettingsService from "../../skywind/services/settings";
import { getTerminalPlayerLoginService } from "../../skywind/services/player/playerLogin";
import * as PlayerLoginService from "../../skywind/services/playerLogin";

import { application } from "../../skywind/serverPlayer";
import socketPlayer from "../../skywind/api/socketPlayer";

describe("Player Socket API", () => {
    let app: Application;
    let ioServer: SocketIO.Server;
    let serverSocket: SocketIO.Socket;
    let clientSocket;

    let brand: BrandEntity;
    let accessToken: string;

    const startServer = async (server: Application, port: number): Promise<void> =>
        new Promise<void>(resolve => {
            const httpServer: http.Server = http.createServer(server);
            httpServer.listen(port, null, () => {
                ioServer = require("socket.io-v2")(httpServer);
                ioServer.on("connection", (socket) => {
                    serverSocket = socket;
                    socketPlayer(socket);
                });
                clientSocket = require("socket.io-client")(`http://localhost:${port}`);
                clientSocket.on("connect", resolve);
            });
        });

    before(async () => {
        app = await application.get();
        await startServer(app, 3001);

        await truncate();

        const master = await createComplexStructure();
        const parent = await master.find({ path: ":TLE1:ENT1:" });

        await factory.create(FACTORY.BRAND, {}, {
            parent: parent,
            name: "brand1",
            description: "brand1 description",
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
        });

        const tle1 = await EntityService.findOne({ key: complexStructure.tle1.key }, EntityService.PATH_CHAR);
        brand = tle1.find({ name: "brand1" }) as BrandEntity;

        await getPlayerService().create(brand, {
            code: "PL00001",
            password: "ACAB!Area51",
            firstName: "Ernest-Miller",
            lastName: "Hemingway",
            email: "<EMAIL>",
            country: "US",
            currency: "USD",
            language: "en"
        });

        await new EntitySettingsService(tle1).patch({
            isPlayerCodeUniqueInSubtree: true
        });
        const terminalPlayerAuthService = getTerminalPlayerLoginService(tle1);
        const loginResult: PlayerLoginService.LoginInfo = await terminalPlayerAuthService.login({
            code: "PL00001",
            password: "ACAB!Area51"
        }, "********");
        accessToken = loginResult.token;
    });

    after(() => {
        ioServer.close();
        clientSocket.close();
    });

    it("Should work", (done) => {
        clientSocket.on("hello", (arg) => {
            expect(arg).to.be.equal("world");
            done();
        });
        serverSocket.emit("hello", "world");
    });

    it("Get player info", (done) => {
        clientSocket.on("player-info", (player) => {
            expect(player.code).equal("PL00001");
            expect(player.country).equal("US");
            expect(player.currency).equal("USD");
            expect(player.isTest).equal(false);
            expect(player.language).equal("en");
            expect(player.status).equal("normal");
            done();
        });
        clientSocket.on("player-error", (arg) => {
            expect.fail(arg);
            done();
        });
        clientSocket.emit("get-player-info", { token: accessToken });
    });
});
