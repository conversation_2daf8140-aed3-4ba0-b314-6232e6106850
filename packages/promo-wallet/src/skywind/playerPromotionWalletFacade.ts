import { ITransaction } from "@skywind-group/sw-wallet";
import { FreebetsData, PlayerFreebetWallet } from "./playerFreebetWallet";
import { PlayerBonusCoinWallet } from "./playerBonusCoinWallet";
import {
    getAsyncLocalWalletStore,
    PlayerWallet,
    PlayerWalletImpl
} from "@skywind-group/sw-management-wallet";
import { PROMO_TYPE } from "./constants";
import { config } from "./config";
import { logging } from "@skywind-group/sw-utils";
import { IIncrementalChange } from "@skywind-group/sw-wallet";

const log = logging.logger("player-promotion-wallet-facade");

export interface HasPromoResult {
    result: boolean;
    existenceType?: string;
}

export class PlayerPromotionWalletFacade {

    constructor(readonly playerWallet: PlayerWallet) {
    }

    public static create(brandId: number, playerCode: string, currency: string): PlayerPromotionWalletFacade {
        return new PlayerPromotionWalletFacade(new PlayerWalletImpl(brandId, playerCode, currency));
    }

    public async hasPromo(promoId: number, promoType: string): Promise<HasPromoResult> {
        // todo aguzanov refactor - two many redis requests
        switch (promoType) {
            case PROMO_TYPE.FREEBET:
                const fb = await this.getFreebetWallet();
                return {
                    result: fb.hasFreebetPromo(promoId)
                };

            case PROMO_TYPE.BONUS_COIN:
                const bns = await this.getBonusCoinWallet();
                const current = bns.getCurrentPromo();
                const has = current && current.promoId === promoId;

                return {
                    result: has,
                    existenceType: has && current.existenceType
                };

            default:
                return  {
                    result: false
                };
        }
    }

    public async getBonusCoinWallet(trx?: ITransaction): Promise<PlayerBonusCoinWallet> {
        return new PlayerBonusCoinWallet(await this.playerWallet.getWallet(trx), this.playerWallet.currencyCode);
    }

    public async getFreebetWallet(trx?: ITransaction): Promise<PlayerFreebetWallet> {
        return new PlayerFreebetWallet(await this.playerWallet.getWallet(trx), this.playerWallet.currencyCode);
    }

    public async addFreebets(trx: ITransaction, ...freeBets: FreebetsData[]): Promise<void> {
        const freebetWallet = await this.getFreebetWallet(trx);
        const changes: IIncrementalChange[] = [];
        for (const freeBet of freeBets) {
            changes.push(freebetWallet.addFreebets(freeBet));
        }

        if (config.asyncLocalWalletEnabled && getAsyncLocalWalletStore() !== undefined) {
            log.debug({ changes }, "Get raw wallet from incremental changes");
            const rawWallet = {};
            for (const change of changes) {
                for (const account of change.accounts) {
                    const key = `${account.name}:${change.property}`;
                    rawWallet[key] = change.amount;
                }
            }
            // Update the async local wallet store when an account update is performed
            getAsyncLocalWalletStore().wallet = rawWallet;
            log.debug({ store: getAsyncLocalWalletStore() }, "Raw wallet store");
        }
    }

    public async resetFreebets(trx: ITransaction, ...freeBets: FreebetsData[]): Promise<void> {
        const freebetWallet = await this.getFreebetWallet(trx);
        for (const freeBet of freeBets) {
            freebetWallet.resetFreebets(freeBet);
        }
    }
}
