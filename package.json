{"name": "sw-management", "private": true, "scripts": {"start": "/------------- START THE BUILDING COMMANDS ----------------/", "lint": "eslint .", "compile": "./node_modules/.bin/lerna run compile && ./node_modules/.bin/lerna run version", "preinstall": "node ./scripts/preinstall.cjs", "generate-ts": "node ./scripts/generate-ts.mjs", "end": "/------------- END THE BUILDING COMMANDS ----------------/", "prepareRelease": "lerna version ${npm_config_lerna_version} --no-git-tag-version --force-publish --yes && node scripts/normalize-version.mjs", "compile:api": "lerna run compile --scope @skywind-group/sw-management-api", "sync-lib": "node scripts/sync-lib.mjs", "full-clean": "npm run clean && npm i && lerna run compile && lerna run version", "dev:mapi": "INTERNAL_SERVER_PORT=9000 lerna run dev:mapi --scope @skywind-group/sw-management-api", "dev:terminal": "INTERNAL_SERVER_PORT=9004 lerna run dev:terminal --scope @skywind-group/sw-management-api", "dev:player": "INTERNAL_SERVER_PORT=9005 lerna run dev:player --scope @skywind-group/sw-management-api", "dev:game-auth": "INTERNAL_SERVER_PORT=9007 lerna run dev:game-auth --scope @skywind-group/sw-management-api", "dev:site": "INTERNAL_SERVER_PORT=9007 lerna run dev:site --scope @skywind-group/sw-management-api", "dev:gameprovider-api": "INTERNAL_SERVER_PORT=9006 lerna run dev --scope @skywind-group/sw-management-gameprovider-api", "clean:lib": "<PERSON>rna run clean", "clean": "rm -rf node_modules package-lock.json packages/*/{package-lock.json,node_modules,out,lib,coverage,.nyc_output}", "test:ts:api": "NODE_OPTIONS=--max-http-header-size=40960 ./node_modules/.bin/lerna run test:ts --scope @skywind-group/sw-management-api", "lint:fix": "eslint . --fix"}, "devDependencies": {"@skywind-group/sw-utils": "2.3.8", "@types/lodash": "4.17.12", "@types/node": "22.14.1", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "bole": "5.0.15", "chai": "~4.3.10", "chai-as-promised": "^7.1.1", "chai-datetime": "1.8.1", "chai-shallow-deep-equal": "1.4.4", "dotenv": "^16.0.0", "eslint": "^9.24.0", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-unicorn": "^58.0.0", "lerna": "8.2.2", "mocha": "10.7.3", "mocha-typescript": "1.1.12", "reflect-metadata": "0.1.13", "sinon": "14.0.2", "sinon-chai": "3.7.0", "ts-node": "^10.7.0", "tsconfig-paths": "^4.2.0", "typescript": "5.6.3"}, "engines": {"node": ">=18"}, "workspaces": ["packages/*"], "packageManager": "npm@11.3.0"}